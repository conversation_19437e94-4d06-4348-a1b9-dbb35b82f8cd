# EduLite Development Setup Guide

## 🚀 Quick Start

Your development environment is now fully configured! Here's what's been set up:

### ✅ Installed Dependencies

#### Core Development Tools:
- **Testing Framework**: Jest + React Testing Library
- **PWA Support**: next-pwa + workbox-webpack-plugin  
- **Performance Tools**: @next/bundle-analyzer
- **Virtual Scrolling**: react-window (for large lists)
- **Cross-platform Scripts**: cross-env

#### Development Scripts Available:
```bash
# Development
npm run dev              # Start Next.js dev server
npm run dev:turbo        # Start with Turbo mode (faster)
npm run dev:debug        # Start with Node.js debugger

# AI Development  
npm run genkit:dev       # Start Genkit development server
npm run genkit:watch     # Start Genkit with auto-reload

# Building & Analysis
npm run build            # Production build
npm run build:analyze    # Build with bundle analysis
npm run start            # Start production server

# Code Quality
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues automatically
npm run typecheck        # TypeScript type checking

# Testing
npm run test             # Run tests
npm run test:watch       # Run tests in watch mode
npm run test:coverage    # Run tests with coverage report

# Utilities
npm run clean            # Clean build artifacts
```

## 🛠️ Development Workflow

### 1. **Start Development Servers**

**Terminal 1 - Main App:**
```bash
npm run dev
```
Access at: http://localhost:3000

**Terminal 2 - AI Development (Optional):**
```bash
npm run genkit:dev
```
Access Genkit UI at: http://localhost:4000

### 2. **Environment Variables**

Your `.env` file is configured with:
```env
GOOGLE_API_KEY=AIzaSyA5ABjAQSO8VEehq2koBrLcLxtwqlyf2Y4
ADMIN_EMAIL=<EMAIL>
```

### 3. **Testing Setup**

Run your first test:
```bash
npm run test
```

The testing environment includes:
- ✅ Jest configuration with Next.js integration
- ✅ React Testing Library setup
- ✅ Firebase mocks
- ✅ Next.js router mocks
- ✅ Coverage reporting (70% threshold)

### 4. **Code Quality Checks**

Before committing code:
```bash
npm run typecheck    # Check TypeScript
npm run lint         # Check code style
npm run test         # Run tests
```

## 📊 Performance Monitoring

### Bundle Analysis
```bash
npm run build:analyze
```
This will generate a bundle analysis report to help optimize your app size.

### Development Debugging
```bash
npm run dev:debug
```
Starts the dev server with Node.js debugger attached.

## 🔧 IDE Configuration

### VS Code Extensions (Recommended):
- ES7+ React/Redux/React-Native snippets
- Tailwind CSS IntelliSense
- TypeScript Importer
- Jest Runner
- Firebase Explorer

### VS Code Settings:
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

## 🚨 Security Notes

The npm audit shows some vulnerabilities in development dependencies (react-email, socket.io). These are:
- ✅ **Not affecting production** (dev dependencies only)
- ✅ **Your main Next.js version (15.3.3)** is secure
- ✅ **Firebase dependencies** are up to date

## 📁 Project Structure Reminder

```
src/
├── actions/           # Server Actions (Database operations)
├── ai/               # Genkit AI flows and tools
├── app/              # Next.js App Router pages
├── components/       # React components
├── contexts/         # React contexts (Auth, etc.)
├── hooks/           # Custom React hooks
├── lib/             # Utilities and configurations
└── types/           # TypeScript type definitions

docs/                # Documentation and guides
public/              # Static assets
```

## 🎯 Next Development Steps

1. **Start with improvements from our recommendations:**
   - Performance optimizations
   - PWA implementation
   - Enhanced error handling
   - Security enhancements

2. **Create your first test:**
   ```bash
   # Create a test file
   touch src/components/__tests__/Logo.test.tsx
   ```

3. **Set up PWA:**
   - Follow the PWA implementation guide
   - Configure service worker
   - Add manifest.json

4. **Implement performance optimizations:**
   - Add pagination to large lists
   - Implement virtual scrolling
   - Add skeleton loading states

## 🔄 Git Workflow

Recommended workflow:
```bash
git checkout -b feature/your-feature-name
# Make changes
npm run typecheck && npm run lint && npm run test
git add .
git commit -m "feat: your feature description"
git push origin feature/your-feature-name
```

## 🆘 Troubleshooting

### Common Issues:

1. **Port already in use:**
   ```bash
   # Kill process on port 3000
   npx kill-port 3000
   ```

2. **Node modules issues:**
   ```bash
   npm run clean
   npm install
   ```

3. **TypeScript errors:**
   ```bash
   npm run typecheck
   ```

4. **Firebase connection issues:**
   - Check service-account.json exists
   - Verify environment variables
   - Check Firebase project settings

Your development environment is ready! 🎉
