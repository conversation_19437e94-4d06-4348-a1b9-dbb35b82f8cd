import { render, screen, waitFor } from '@testing-library/react'
import { Logo } from '@/components/navigation/Logo'

// Mock Next.js Image component
jest.mock('next/image', () => ({
  __esModule: true,
  default: (props: any) => {
    // eslint-disable-next-line @next/next/no-img-element
    return <img {...props} />
  },
}))

describe('Logo Component', () => {
  it('renders the logo with loading state initially', () => {
    render(<Logo />)

    // Should show loading spinner initially
    expect(screen.getByRole('link')).toBeInTheDocument()
  })

  it('renders the logo with institution name after loading', async () => {
    render(<Logo />)

    // Wait for the component to load settings
    await waitFor(() => {
      expect(screen.getByText('EduLite')).toBeInTheDocument()
    })
  })

  it('renders with different sizes', async () => {
    render(<Logo size="lg" />)

    await waitFor(() => {
      const logoText = screen.getByText('EduLite')
      expect(logoText).toHaveClass('text-3xl')
    })
  })

  it('renders as a link to home page', () => {
    render(<Logo />)

    const linkElement = screen.getByRole('link')
    expect(linkElement).toHaveAttribute('href', '/')
  })
})
