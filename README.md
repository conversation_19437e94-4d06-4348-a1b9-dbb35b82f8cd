l# EduLite - Student Management System

EduLite is a modern, AI-enhanced student management system designed for mid-size educational institutions. It aims to streamline administrative tasks, manage student applications, courses, leads, and provide a portal for students.

## Project Overview

This application is built with a focus on a clean user experience, efficient data management, and leveraging AI for assistance. It includes features for:

*   **Admin Dashboard:** For managing leads, courses, student applications, and system settings.
*   **Student Portal:** For students to view their enrolled courses and access relevant information.
*   **Application System:** Allows prospective students to apply for courses.
*   **AI Assistant:** Provides support by answering frequently asked questions.

## Tech Stack

*   **Frontend:**
    *   [Next.js](https://nextjs.org/) (with App Router)
    *   [React](https://reactjs.org/)
    *   [TypeScript](https://www.typescriptlang.org/)
*   **UI:**
    *   [ShadCN UI](https://ui.shadcn.com/)
    *   [Tailwind CSS](https://tailwindcss.com/)
*   **AI Integration:**
    *   [Genkit (by Google)](https://firebase.google.com/docs/genkit)
*   **Styling:**
    *   Tailwind CSS
    *   CSS Variables for theming
*   **Forms:**
    *   React Hook Form
    *   Zod for validation

## Getting Started

### Prerequisites

*   Node.js (LTS version recommended)
*   npm or yarn

### Installation

1.  **Clone the repository (if applicable):**
    ```bash
    # git clone <repository-url>
    # cd <project-directory>
    ```

2.  **Install dependencies:**
    ```bash
    npm install
    # or
    # yarn install
    ```

3.  **Set up environment variables:**
    Create a `.env.local` file in the root of your project and add any necessary environment variables.
    *   For Genkit with Google AI, you'll typically need `GOOGLE_API_KEY`.

    Example `.env.local`:
    ```env
    GOOGLE_API_KEY=your_google_api_key_here
    ```

### Running the Development Server

1.  **Start the Next.js development server:**
    This will run the main application.
    ```bash
    npm run dev
    # or
    # yarn dev
    ```
    The application will usually be available at `http://localhost:9002`.

2.  **Start the Genkit development server (in a separate terminal):**
    This runs the Genkit flows and makes them available for inspection and local development.
    ```bash
    npm run genkit:dev
    # or for auto-reloading on changes
    # npm run genkit:watch
    ```
    The Genkit Developer UI will typically be available at `http://localhost:4000`.

## Project Structure (Key Directories)

*   `src/app/`: Contains the Next.js pages and layouts (using App Router).
    *   `src/app/(app)/`: Authenticated routes for the admin and student dashboards.
    *   `src/app/api/`: Reserved for future API routes (currently using Server Actions exclusively).
*   `src/components/`: Reusable React components.
    *   `src/components/ui/`: ShadCN UI components.
    *   `src/components/ai/`: Components related to AI functionality.
*   `src/ai/`: Genkit related code.
    *   `src/ai/flows/`: Genkit flow definitions.
*   `src/lib/`: Utility functions and Firebase configuration.
*   `src/contexts/`: React context providers (e.g., AuthContext).
*   `src/hooks/`: Custom React hooks.
*   `src/types/`: TypeScript type definitions.
*   `public/`: Static assets.

## Pending Development Work

For a detailed list of pending tasks and future development plans, please refer to the `pdwork.md` file in the root of this project.

## Managing User Roles (Admin & Accountant)

For information on how to create Admin and Accountant user accounts by assigning roles in Firebase, please refer to `docs/managing-user-roles.md`.

## Contributing

(Details to be added if this project becomes collaborative)

---

This README provides a basic overview. Feel free to expand it as the project grows!
