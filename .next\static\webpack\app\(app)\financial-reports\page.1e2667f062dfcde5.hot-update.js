"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(app)/financial-reports/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FinancialReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _actions_feeActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/actions/feeActions */ \"(app-pages-browser)/./src/actions/feeActions.ts\");\n/* harmony import */ var _actions_courseActions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/courseActions */ \"(app-pages-browser)/./src/actions/courseActions.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter-x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/piggy-bank.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-bar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst feeStatuses = [\n    'Pending',\n    'Paid',\n    'Partially Paid',\n    'Overdue'\n];\nconst statusVariantMap = {\n    Pending: 'default',\n    'Partially Paid': 'secondary',\n    Paid: 'outline',\n    Overdue: 'destructive'\n};\nfunction FinancialReportsPage() {\n    _s();\n    const { user, isLoading: isAuthLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined); // Start with no date filter to show all records\n    const [courseId, setCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchReportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FinancialReportsPage.useCallback[fetchReportData]\": async ()=>{\n            if (!user || !_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser) return;\n            setIsLoading(true);\n            try {\n                const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser.getIdToken();\n                const filters = {\n                    startDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, 'yyyy-MM-dd') : undefined,\n                    endDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.to) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, 'yyyy-MM-dd') : undefined,\n                    courseId: courseId || undefined,\n                    status: status || undefined\n                };\n                const data = await (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getFinancialReportData)(filters, idToken);\n                setReportData(data);\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"Could not fetch report data.\";\n                toast({\n                    title: \"Error\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"FinancialReportsPage.useCallback[fetchReportData]\"], [\n        user,\n        dateRange,\n        courseId,\n        status,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            async function fetchInitialCourses() {\n                try {\n                    const fetchedCourses = await (0,_actions_courseActions__WEBPACK_IMPORTED_MODULE_3__.getCourses)();\n                    setCourses(fetchedCourses);\n                } catch (error) {\n                    toast({\n                        title: \"Error\",\n                        description: \"Could not load courses for filtering.\",\n                        variant: \"destructive\"\n                    });\n                }\n            }\n            fetchInitialCourses();\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            if (!isAuthLoading && user) {\n                fetchReportData();\n            }\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        fetchReportData,\n        isAuthLoading,\n        user\n    ]);\n    const clearFilters = ()=>{\n        setDateRange(undefined); // Clear date range to show all records\n        setCourseId('');\n        setStatus('');\n        // Fetch data immediately after clearing filters\n        setTimeout(()=>fetchReportData(), 100);\n    };\n    const summaryStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FinancialReportsPage.useMemo[summaryStats]\": ()=>{\n            const totalDue = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalDue\": (sum, fee)=>sum + fee.amountDue\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalDue\"], 0);\n            const totalPaid = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalPaid\": (sum, fee)=>sum + fee.amountPaid\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalPaid\"], 0);\n            const totalOutstanding = totalDue - totalPaid;\n            return {\n                totalDue,\n                totalPaid,\n                totalOutstanding,\n                recordCount: reportData.length\n            };\n        }\n    }[\"FinancialReportsPage.useMemo[summaryStats]\"], [\n        reportData\n    ]);\n    const exportToCSV = ()=>{\n        if (reportData.length === 0) {\n            toast({\n                title: \"No data to export\",\n                variant: \"default\"\n            });\n            return;\n        }\n        const headers = [\n            'ID',\n            'Student Name',\n            'Course Name',\n            'Fee Description',\n            'Amount Due',\n            'Amount Paid',\n            'Remaining',\n            'Due Date',\n            'Status',\n            'Last Payment Date'\n        ];\n        const csvRows = [\n            headers.join(',')\n        ];\n        for (const fee of reportData){\n            const remaining = fee.amountDue - fee.amountPaid;\n            const values = [\n                fee.id,\n                '\"'.concat(fee.studentName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.courseName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.feeDescription.replace(/\"/g, '\"\"'), '\"'),\n                fee.amountDue.toFixed(2),\n                fee.amountPaid.toFixed(2),\n                remaining.toFixed(2),\n                fee.dueDate,\n                fee.status,\n                fee.lastPaymentDate || ''\n            ];\n            csvRows.push(values.join(','));\n        }\n        const csvString = csvRows.join('\\n');\n        const blob = new Blob([\n            csvString\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"financial_report_\".concat(new Date().toISOString().split('T')[0], \".csv\"));\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                        children: \"Financial Reports\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 140,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Generate and view financial reports based on various filters.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                            children: \"Report Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Due Date Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"justify-start text-left font-normal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 157,\n                                                            columnNumber: 33\n                                                        }, this),\n                                                        (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? dateRange.to ? \"\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\"), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, \"LLL dd, y\")) : (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\") : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Pick a date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                className: \"w-auto p-0\",\n                                                align: \"start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                    mode: \"range\",\n                                                    selected: dateRange,\n                                                    onSelect: setDateRange,\n                                                    initialFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: courseId || '_all_',\n                                        onValueChange: (value)=>setCourseId(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Courses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Courses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    courses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                            value: course.id,\n                                                            children: course.name\n                                                        }, course.id, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 180,\n                                                            columnNumber: 52\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: status || '_all_',\n                                        onValueChange: (value)=>setStatus(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    feeStatuses.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                            value: s,\n                                                            className: \"capitalize\",\n                                                            children: s\n                                                        }, s, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 51\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: fetchReportData,\n                                        disabled: isLoading,\n                                        className: \"w-full\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 38\n                                        }, this) : 'Apply Filters'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: clearFilters,\n                                        disabled: isLoading,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 194,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Collected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 210,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 208,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalPaid.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Outstanding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-destructive\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalOutstanding.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 216,\n                        columnNumber: 14\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Filtered Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: summaryStats.recordCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 14\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        children: \"Filtered Fee Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                        children: [\n                                            \"A detailed list of fee records matching the selected filters. Found \",\n                                            reportData.length,\n                                            \" records.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                onClick: exportToCSV,\n                                disabled: isLoading || reportData.length === 0,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 237,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 251,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 250,\n                            columnNumber: 22\n                        }, this) : reportData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No records match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Due Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Due\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Paid\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableBody, {\n                                        children: reportData.map((fee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: fee.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 274,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.courseName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.feeDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 276,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(fee.dueDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 277,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                            variant: statusVariantMap[fee.status],\n                                                            className: \"capitalize\",\n                                                            children: fee.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 52\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountDue.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountPaid.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, fee.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 236,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(FinancialReportsPage, \"Zre8dwTF6Nm0zsIjUOUCv22haPk=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = FinancialReportsPage;\nvar _c;\n$RefreshReg$(_c, \"FinancialReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx\n"));

/***/ })

});