"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-email";
exports.ids = ["vendor-chunks/@react-email"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@react-email/body/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/body/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Body: () => (/* binding */ Body)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/body.tsx\n\n\nvar Body = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (_a, ref) => {\n    var _b = _a, { children, style } = _b, props = __objRest(_b, [\"children\", \"style\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"body\", __spreadProps(__spreadValues({}, props), { ref, style, children }));\n  }\n);\nBody.displayName = \"Body\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/body/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/button/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@react-email/button/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/button.tsx\n\n\n// src/utils/px-to-pt.ts\nvar pxToPt = (px) => typeof px === \"number\" && !isNaN(Number(px)) ? px * 3 / 4 : null;\n\n// src/utils/parse-padding.ts\nfunction convertToPx(value) {\n  let px = 0;\n  if (!value) {\n    return px;\n  }\n  if (typeof value === \"number\") {\n    return value;\n  }\n  const matches = /^([\\d.]+)(px|em|rem|%)$/.exec(value);\n  if (matches && matches.length === 3) {\n    const numValue = parseFloat(matches[1]);\n    const unit = matches[2];\n    switch (unit) {\n      case \"px\":\n        return numValue;\n      case \"em\":\n      case \"rem\":\n        px = numValue * 16;\n        return px;\n      case \"%\":\n        px = numValue / 100 * 600;\n        return px;\n      default:\n        return numValue;\n    }\n  } else {\n    return 0;\n  }\n}\nfunction parsePadding({\n  padding = \"\",\n  paddingTop,\n  paddingRight,\n  paddingBottom,\n  paddingLeft\n}) {\n  let pt = 0;\n  let pr = 0;\n  let pb = 0;\n  let pl = 0;\n  if (typeof padding === \"number\") {\n    pt = padding;\n    pr = padding;\n    pb = padding;\n    pl = padding;\n  } else {\n    const values = padding.split(/\\s+/);\n    switch (values.length) {\n      case 1:\n        pt = convertToPx(values[0]);\n        pr = convertToPx(values[0]);\n        pb = convertToPx(values[0]);\n        pl = convertToPx(values[0]);\n        break;\n      case 2:\n        pt = convertToPx(values[0]);\n        pb = convertToPx(values[0]);\n        pr = convertToPx(values[1]);\n        pl = convertToPx(values[1]);\n        break;\n      case 3:\n        pt = convertToPx(values[0]);\n        pr = convertToPx(values[1]);\n        pl = convertToPx(values[1]);\n        pb = convertToPx(values[2]);\n        break;\n      case 4:\n        pt = convertToPx(values[0]);\n        pr = convertToPx(values[1]);\n        pb = convertToPx(values[2]);\n        pl = convertToPx(values[3]);\n        break;\n      default:\n        break;\n    }\n  }\n  return {\n    pt: paddingTop ? convertToPx(paddingTop) : pt,\n    pr: paddingRight ? convertToPx(paddingRight) : pr,\n    pb: paddingBottom ? convertToPx(paddingBottom) : pb,\n    pl: paddingLeft ? convertToPx(paddingLeft) : pl\n  };\n}\n\n// src/button.tsx\n\nfunction computeFontWidthAndSpaceCount(expectedWidth) {\n  let smallestSpaceCount = 0;\n  while (expectedWidth / smallestSpaceCount / 2 > 5) {\n    smallestSpaceCount++;\n  }\n  return [expectedWidth / smallestSpaceCount / 2, smallestSpaceCount];\n}\nvar Button = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (_a, ref) => {\n    var _b = _a, { children, style, target = \"_blank\" } = _b, props = __objRest(_b, [\"children\", \"style\", \"target\"]);\n    var _a2, _b2, _c, _d;\n    const { pt, pr, pb, pl } = parsePadding({\n      padding: style == null ? void 0 : style.padding,\n      paddingLeft: (_a2 = style == null ? void 0 : style.paddingLeft) != null ? _a2 : style == null ? void 0 : style.paddingInline,\n      paddingRight: (_b2 = style == null ? void 0 : style.paddingRight) != null ? _b2 : style == null ? void 0 : style.paddingInline,\n      paddingTop: (_c = style == null ? void 0 : style.paddingTop) != null ? _c : style == null ? void 0 : style.paddingBlock,\n      paddingBottom: (_d = style == null ? void 0 : style.paddingBottom) != null ? _d : style == null ? void 0 : style.paddingBlock\n    });\n    const y = pt + pb;\n    const textRaise = pxToPt(y);\n    const [plFontWidth, plSpaceCount] = computeFontWidthAndSpaceCount(pl);\n    const [prFontWidth, prSpaceCount] = computeFontWidthAndSpaceCount(pr);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n      \"a\",\n      __spreadProps(__spreadValues({}, props), {\n        ref,\n        style: buttonStyle(__spreadProps(__spreadValues({}, style), { pt, pr, pb, pl })),\n        target,\n        children: [\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            \"span\",\n            {\n              dangerouslySetInnerHTML: {\n                // The `&#8202;` is as close to `1px` of an empty character as we can get, then, we use the `mso-font-width`\n                // to scale it according to what padding the developer wants. `mso-font-width` also does not allow for percentages\n                // >= 500% so we need to add extra spaces accordingly.\n                //\n                // See https://github.com/resend/react-email/issues/1512 for why we do not use letter-spacing instead.\n                __html: `<!--[if mso]><i style=\"mso-font-width:${plFontWidth * 100}%;mso-text-raise:${textRaise}\" hidden>${\"&#8202;\".repeat(\n                  plSpaceCount\n                )}</i><![endif]-->`\n              }\n            }\n          ),\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", { style: buttonTextStyle(pb), children }),\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            \"span\",\n            {\n              dangerouslySetInnerHTML: {\n                __html: `<!--[if mso]><i style=\"mso-font-width:${prFontWidth * 100}%\" hidden>${\"&#8202;\".repeat(\n                  prSpaceCount\n                )}&#8203;</i><![endif]-->`\n              }\n            }\n          )\n        ]\n      })\n    );\n  }\n);\nButton.displayName = \"Button\";\nvar buttonStyle = (style) => {\n  const _a = style || {}, { pt, pr, pb, pl } = _a, rest = __objRest(_a, [\"pt\", \"pr\", \"pb\", \"pl\"]);\n  return __spreadProps(__spreadValues({\n    lineHeight: \"100%\",\n    textDecoration: \"none\",\n    display: \"inline-block\",\n    maxWidth: \"100%\"\n  }, rest), {\n    padding: `${pt}px ${pr}px ${pb}px ${pl}px`\n  });\n};\nvar buttonTextStyle = (pb) => {\n  return {\n    maxWidth: \"100%\",\n    display: \"inline-block\",\n    lineHeight: \"120%\",\n    msoPaddingAlt: \"0px\",\n    msoTextRaise: pxToPt(pb || 0)\n  };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/button/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/container/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@react-email/container/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Container: () => (/* binding */ Container)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/container.tsx\n\n\nvar Container = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children, style } = _b, props = __objRest(_b, [\"children\", \"style\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"table\",\n    __spreadProps(__spreadValues({\n      align: \"center\",\n      width: \"100%\"\n    }, props), {\n      border: 0,\n      cellPadding: \"0\",\n      cellSpacing: \"0\",\n      ref,\n      role: \"presentation\",\n      style: __spreadValues({ maxWidth: \"37.5em\" }, style),\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tbody\", { children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tr\", { style: { width: \"100%\" }, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"td\", { children }) }) })\n    })\n  );\n});\nContainer.displayName = \"Container\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/container/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/head/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/head/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Head: () => (/* binding */ Head)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/head.tsx\n\n\nvar Head = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (_a, ref) => {\n    var _b = _a, { children } = _b, props = __objRest(_b, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"head\", __spreadProps(__spreadValues({}, props), { ref, children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"meta\", { content: \"text/html; charset=UTF-8\", httpEquiv: \"Content-Type\" }),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"meta\", { name: \"x-apple-disable-message-reformatting\" }),\n      children\n    ] }));\n  }\n);\nHead.displayName = \"Head\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/head/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/heading/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-email/heading/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Heading: () => (/* binding */ Heading)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/heading.tsx\n\n\n// src/utils/spaces.ts\nvar withMargin = (props) => {\n  const nonEmptyStyles = [\n    withSpace(props.m, [\"margin\"]),\n    withSpace(props.mx, [\"marginLeft\", \"marginRight\"]),\n    withSpace(props.my, [\"marginTop\", \"marginBottom\"]),\n    withSpace(props.mt, [\"marginTop\"]),\n    withSpace(props.mr, [\"marginRight\"]),\n    withSpace(props.mb, [\"marginBottom\"]),\n    withSpace(props.ml, [\"marginLeft\"])\n  ].filter((s) => Object.keys(s).length);\n  const mergedStyles = nonEmptyStyles.reduce((acc, style) => {\n    return __spreadValues(__spreadValues({}, acc), style);\n  }, {});\n  return mergedStyles;\n};\nvar withSpace = (value, properties) => {\n  return properties.reduce((styles, property) => {\n    if (!isNaN(parseFloat(value))) {\n      return __spreadProps(__spreadValues({}, styles), { [property]: `${value}px` });\n    }\n    return styles;\n  }, {});\n};\n\n// src/heading.tsx\n\nvar Heading = (_a) => {\n  var _b = _a, {\n    as: Tag = \"h1\",\n    children,\n    style,\n    m,\n    mx,\n    my,\n    mt,\n    mr,\n    mb,\n    ml\n  } = _b, props = __objRest(_b, [\n    \"as\",\n    \"children\",\n    \"style\",\n    \"m\",\n    \"mx\",\n    \"my\",\n    \"mt\",\n    \"mr\",\n    \"mb\",\n    \"ml\"\n  ]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_1__.Slot,\n    __spreadProps(__spreadValues({}, props), {\n      style: __spreadValues(__spreadValues({}, withMargin({ m, mx, my, mt, mr, mb, ml })), style),\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Tag, { children })\n    })\n  );\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/heading/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!****************************************************************************************************!*\
  !*** ./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => refs.forEach((ref) => setRef(ref, node));\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVhY3QtZW1haWwvaGVhZGluZy9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhDQUFpQjtBQUMxQjtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIi9ob21lL3VzZXIvRWR1TGl0ZS9ub2RlX21vZHVsZXMvQHJlYWN0LWVtYWlsL2hlYWRpbmcvbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1jb21wb3NlLXJlZnMvZGlzdC9pbmRleC5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvY29tcG9zZS1yZWZzL3NyYy9jb21wb3NlUmVmcy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gc2V0UmVmKHJlZiwgdmFsdWUpIHtcbiAgaWYgKHR5cGVvZiByZWYgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHJlZih2YWx1ZSk7XG4gIH0gZWxzZSBpZiAocmVmICE9PSBudWxsICYmIHJlZiAhPT0gdm9pZCAwKSB7XG4gICAgcmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgfVxufVxuZnVuY3Rpb24gY29tcG9zZVJlZnMoLi4ucmVmcykge1xuICByZXR1cm4gKG5vZGUpID0+IHJlZnMuZm9yRWFjaCgocmVmKSA9PiBzZXRSZWYocmVmLCBub2RlKSk7XG59XG5mdW5jdGlvbiB1c2VDb21wb3NlZFJlZnMoLi4ucmVmcykge1xuICByZXR1cm4gUmVhY3QudXNlQ2FsbGJhY2soY29tcG9zZVJlZnMoLi4ucmVmcyksIHJlZnMpO1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZVJlZnMsXG4gIHVzZUNvbXBvc2VkUmVmc1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!********************************************************************************************!*\
  !*** ./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/html/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/html/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Html: () => (/* binding */ Html)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/html.tsx\n\n\nvar Html = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children, lang = \"en\", dir = \"ltr\" } = _b, props = __objRest(_b, [\"children\", \"lang\", \"dir\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"html\", __spreadProps(__spreadValues({}, props), { dir, lang, ref, children }));\n});\nHtml.displayName = \"Html\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/html/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/preview/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-email/preview/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Preview: () => (/* binding */ Preview),\n/* harmony export */   renderWhiteSpace: () => (/* binding */ renderWhiteSpace)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/preview.tsx\n\n\nvar PREVIEW_MAX_LENGTH = 150;\nvar Preview = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children = \"\" } = _b, props = __objRest(_b, [\"children\"]);\n  let text = Array.isArray(children) ? children.join(\"\") : children;\n  text = text.substr(0, PREVIEW_MAX_LENGTH);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n    \"div\",\n    __spreadProps(__spreadValues({\n      style: {\n        display: \"none\",\n        overflow: \"hidden\",\n        lineHeight: \"1px\",\n        opacity: 0,\n        maxHeight: 0,\n        maxWidth: 0\n      }\n    }, props), {\n      ref,\n      children: [\n        text,\n        renderWhiteSpace(text)\n      ]\n    })\n  );\n});\nPreview.displayName = \"Preview\";\nvar renderWhiteSpace = (text) => {\n  if (text.length >= PREVIEW_MAX_LENGTH) {\n    return null;\n  }\n  const whiteSpaceCodes = \"\\xA0\\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF\";\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { children: whiteSpaceCodes.repeat(PREVIEW_MAX_LENGTH - text.length) });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/preview/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/section/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-email/section/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Section: () => (/* binding */ Section)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/section.tsx\n\n\nvar Section = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children, style } = _b, props = __objRest(_b, [\"children\", \"style\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"table\",\n    __spreadProps(__spreadValues({\n      align: \"center\",\n      width: \"100%\"\n    }, props), {\n      border: 0,\n      cellPadding: \"0\",\n      cellSpacing: \"0\",\n      ref,\n      role: \"presentation\",\n      style,\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tbody\", { children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tr\", { children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"td\", { children }) }) })\n    })\n  );\n});\nSection.displayName = \"Section\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/section/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/text/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/text/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Text: () => (/* binding */ Text)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/text.tsx\n\n\nvar Text = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { style } = _b, props = __objRest(_b, [\"style\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"p\",\n    __spreadProps(__spreadValues({}, props), {\n      ref,\n      style: __spreadValues({\n        fontSize: \"14px\",\n        lineHeight: \"24px\",\n        margin: \"16px 0\"\n      }, style)\n    })\n  );\n});\nText.displayName = \"Text\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/text/dist/index.mjs\n");

/***/ })

};
;