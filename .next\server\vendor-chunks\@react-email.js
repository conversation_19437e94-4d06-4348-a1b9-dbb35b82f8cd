"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@react-email";
exports.ids = ["vendor-chunks/@react-email"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@react-email/body/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/body/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Body: () => (/* binding */ Body)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/body.tsx\n\n\nvar Body = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (_a, ref) => {\n    var _b = _a, { children, style } = _b, props = __objRest(_b, [\"children\", \"style\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"body\", __spreadProps(__spreadValues({}, props), { ref, style, children }));\n  }\n);\nBody.displayName = \"Body\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/body/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/button/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@react-email/button/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/button.tsx\n\n\n// src/utils/px-to-pt.ts\nvar pxToPt = (px) => typeof px === \"number\" && !isNaN(Number(px)) ? px * 3 / 4 : null;\n\n// src/utils/parse-padding.ts\nfunction convertToPx(value) {\n  let px = 0;\n  if (!value) {\n    return px;\n  }\n  if (typeof value === \"number\") {\n    return value;\n  }\n  const matches = /^([\\d.]+)(px|em|rem|%)$/.exec(value);\n  if (matches && matches.length === 3) {\n    const numValue = parseFloat(matches[1]);\n    const unit = matches[2];\n    switch (unit) {\n      case \"px\":\n        return numValue;\n      case \"em\":\n      case \"rem\":\n        px = numValue * 16;\n        return px;\n      case \"%\":\n        px = numValue / 100 * 600;\n        return px;\n      default:\n        return numValue;\n    }\n  } else {\n    return 0;\n  }\n}\nfunction parsePadding({\n  padding = \"\",\n  paddingTop,\n  paddingRight,\n  paddingBottom,\n  paddingLeft\n}) {\n  let pt = 0;\n  let pr = 0;\n  let pb = 0;\n  let pl = 0;\n  if (typeof padding === \"number\") {\n    pt = padding;\n    pr = padding;\n    pb = padding;\n    pl = padding;\n  } else {\n    const values = padding.split(/\\s+/);\n    switch (values.length) {\n      case 1:\n        pt = convertToPx(values[0]);\n        pr = convertToPx(values[0]);\n        pb = convertToPx(values[0]);\n        pl = convertToPx(values[0]);\n        break;\n      case 2:\n        pt = convertToPx(values[0]);\n        pb = convertToPx(values[0]);\n        pr = convertToPx(values[1]);\n        pl = convertToPx(values[1]);\n        break;\n      case 3:\n        pt = convertToPx(values[0]);\n        pr = convertToPx(values[1]);\n        pl = convertToPx(values[1]);\n        pb = convertToPx(values[2]);\n        break;\n      case 4:\n        pt = convertToPx(values[0]);\n        pr = convertToPx(values[1]);\n        pb = convertToPx(values[2]);\n        pl = convertToPx(values[3]);\n        break;\n      default:\n        break;\n    }\n  }\n  return {\n    pt: paddingTop ? convertToPx(paddingTop) : pt,\n    pr: paddingRight ? convertToPx(paddingRight) : pr,\n    pb: paddingBottom ? convertToPx(paddingBottom) : pb,\n    pl: paddingLeft ? convertToPx(paddingLeft) : pl\n  };\n}\n\n// src/button.tsx\n\nfunction computeFontWidthAndSpaceCount(expectedWidth) {\n  let smallestSpaceCount = 0;\n  while (expectedWidth / smallestSpaceCount / 2 > 5) {\n    smallestSpaceCount++;\n  }\n  return [expectedWidth / smallestSpaceCount / 2, smallestSpaceCount];\n}\nvar Button = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (_a, ref) => {\n    var _b = _a, { children, style, target = \"_blank\" } = _b, props = __objRest(_b, [\"children\", \"style\", \"target\"]);\n    var _a2, _b2, _c, _d;\n    const { pt, pr, pb, pl } = parsePadding({\n      padding: style == null ? void 0 : style.padding,\n      paddingLeft: (_a2 = style == null ? void 0 : style.paddingLeft) != null ? _a2 : style == null ? void 0 : style.paddingInline,\n      paddingRight: (_b2 = style == null ? void 0 : style.paddingRight) != null ? _b2 : style == null ? void 0 : style.paddingInline,\n      paddingTop: (_c = style == null ? void 0 : style.paddingTop) != null ? _c : style == null ? void 0 : style.paddingBlock,\n      paddingBottom: (_d = style == null ? void 0 : style.paddingBottom) != null ? _d : style == null ? void 0 : style.paddingBlock\n    });\n    const y = pt + pb;\n    const textRaise = pxToPt(y);\n    const [plFontWidth, plSpaceCount] = computeFontWidthAndSpaceCount(pl);\n    const [prFontWidth, prSpaceCount] = computeFontWidthAndSpaceCount(pr);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n      \"a\",\n      __spreadProps(__spreadValues({}, props), {\n        ref,\n        style: buttonStyle(__spreadProps(__spreadValues({}, style), { pt, pr, pb, pl })),\n        target,\n        children: [\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            \"span\",\n            {\n              dangerouslySetInnerHTML: {\n                // The `&#8202;` is as close to `1px` of an empty character as we can get, then, we use the `mso-font-width`\n                // to scale it according to what padding the developer wants. `mso-font-width` also does not allow for percentages\n                // >= 500% so we need to add extra spaces accordingly.\n                //\n                // See https://github.com/resend/react-email/issues/1512 for why we do not use letter-spacing instead.\n                __html: `<!--[if mso]><i style=\"mso-font-width:${plFontWidth * 100}%;mso-text-raise:${textRaise}\" hidden>${\"&#8202;\".repeat(\n                  plSpaceCount\n                )}</i><![endif]-->`\n              }\n            }\n          ),\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", { style: buttonTextStyle(pb), children }),\n          /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n            \"span\",\n            {\n              dangerouslySetInnerHTML: {\n                __html: `<!--[if mso]><i style=\"mso-font-width:${prFontWidth * 100}%\" hidden>${\"&#8202;\".repeat(\n                  prSpaceCount\n                )}&#8203;</i><![endif]-->`\n              }\n            }\n          )\n        ]\n      })\n    );\n  }\n);\nButton.displayName = \"Button\";\nvar buttonStyle = (style) => {\n  const _a = style || {}, { pt, pr, pb, pl } = _a, rest = __objRest(_a, [\"pt\", \"pr\", \"pb\", \"pl\"]);\n  return __spreadProps(__spreadValues({\n    lineHeight: \"100%\",\n    textDecoration: \"none\",\n    display: \"inline-block\",\n    maxWidth: \"100%\"\n  }, rest), {\n    padding: `${pt}px ${pr}px ${pb}px ${pl}px`\n  });\n};\nvar buttonTextStyle = (pb) => {\n  return {\n    maxWidth: \"100%\",\n    display: \"inline-block\",\n    lineHeight: \"120%\",\n    msoPaddingAlt: \"0px\",\n    msoTextRaise: pxToPt(pb || 0)\n  };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/button/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/container/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@react-email/container/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Container: () => (/* binding */ Container)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/container.tsx\n\n\nvar Container = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children, style } = _b, props = __objRest(_b, [\"children\", \"style\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"table\",\n    __spreadProps(__spreadValues({\n      align: \"center\",\n      width: \"100%\"\n    }, props), {\n      border: 0,\n      cellPadding: \"0\",\n      cellSpacing: \"0\",\n      ref,\n      role: \"presentation\",\n      style: __spreadValues({ maxWidth: \"37.5em\" }, style),\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tbody\", { children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tr\", { style: { width: \"100%\" }, children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"td\", { children }) }) })\n    })\n  );\n});\nContainer.displayName = \"Container\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/container/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/head/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/head/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Head: () => (/* binding */ Head)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/head.tsx\n\n\nvar Head = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n  (_a, ref) => {\n    var _b = _a, { children } = _b, props = __objRest(_b, [\"children\"]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"head\", __spreadProps(__spreadValues({}, props), { ref, children: [\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"meta\", { content: \"text/html; charset=UTF-8\", httpEquiv: \"Content-Type\" }),\n      /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"meta\", { name: \"x-apple-disable-message-reformatting\" }),\n      children\n    ] }));\n  }\n);\nHead.displayName = \"Head\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/head/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/heading/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-email/heading/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Heading: () => (/* binding */ Heading)\n/* harmony export */ });\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/heading.tsx\n\n\n// src/utils/spaces.ts\nvar withMargin = (props) => {\n  const nonEmptyStyles = [\n    withSpace(props.m, [\"margin\"]),\n    withSpace(props.mx, [\"marginLeft\", \"marginRight\"]),\n    withSpace(props.my, [\"marginTop\", \"marginBottom\"]),\n    withSpace(props.mt, [\"marginTop\"]),\n    withSpace(props.mr, [\"marginRight\"]),\n    withSpace(props.mb, [\"marginBottom\"]),\n    withSpace(props.ml, [\"marginLeft\"])\n  ].filter((s) => Object.keys(s).length);\n  const mergedStyles = nonEmptyStyles.reduce((acc, style) => {\n    return __spreadValues(__spreadValues({}, acc), style);\n  }, {});\n  return mergedStyles;\n};\nvar withSpace = (value, properties) => {\n  return properties.reduce((styles, property) => {\n    if (!isNaN(parseFloat(value))) {\n      return __spreadProps(__spreadValues({}, styles), { [property]: `${value}px` });\n    }\n    return styles;\n  }, {});\n};\n\n// src/heading.tsx\n\nvar Heading = (_a) => {\n  var _b = _a, {\n    as: Tag = \"h1\",\n    children,\n    style,\n    m,\n    mx,\n    my,\n    mt,\n    mr,\n    mb,\n    ml\n  } = _b, props = __objRest(_b, [\n    \"as\",\n    \"children\",\n    \"style\",\n    \"m\",\n    \"mx\",\n    \"my\",\n    \"mt\",\n    \"mr\",\n    \"mb\",\n    \"ml\"\n  ]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\n    _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_1__.Slot,\n    __spreadProps(__spreadValues({}, props), {\n      style: __spreadValues(__spreadValues({}, withMargin({ m, mx, my, mt, mr, mb, ml })), style),\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Tag, { children })\n    })\n  );\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/heading/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!****************************************************************************************************!*\
  !*** ./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => refs.forEach((ref) => setRef(ref, node));\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AcmVhY3QtZW1haWwvaGVhZGluZy9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhDQUFpQjtBQUMxQjtBQUlFO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxlZHVsaXRlXFxub2RlX21vZHVsZXNcXEByZWFjdC1lbWFpbFxcaGVhZGluZ1xcbm9kZV9tb2R1bGVzXFxAcmFkaXgtdWlcXHJlYWN0LWNvbXBvc2UtcmVmc1xcZGlzdFxcaW5kZXgubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L2NvbXBvc2UtcmVmcy9zcmMvY29tcG9zZVJlZnMudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmZ1bmN0aW9uIHNldFJlZihyZWYsIHZhbHVlKSB7XG4gIGlmICh0eXBlb2YgcmVmID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICByZWYodmFsdWUpO1xuICB9IGVsc2UgaWYgKHJlZiAhPT0gbnVsbCAmJiByZWYgIT09IHZvaWQgMCkge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH1cbn1cbmZ1bmN0aW9uIGNvbXBvc2VSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIChub2RlKSA9PiByZWZzLmZvckVhY2goKHJlZikgPT4gc2V0UmVmKHJlZiwgbm9kZSkpO1xufVxuZnVuY3Rpb24gdXNlQ29tcG9zZWRSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIFJlYWN0LnVzZUNhbGxiYWNrKGNvbXBvc2VSZWZzKC4uLnJlZnMpLCByZWZzKTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VSZWZzLFxuICB1c2VDb21wb3NlZFJlZnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!********************************************************************************************!*\
  !*** ./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/heading/node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/html/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/html/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Html: () => (/* binding */ Html)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/html.tsx\n\n\nvar Html = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children, lang = \"en\", dir = \"ltr\" } = _b, props = __objRest(_b, [\"children\", \"lang\", \"dir\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"html\", __spreadProps(__spreadValues({}, props), { dir, lang, ref, children }));\n});\nHtml.displayName = \"Html\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/html/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/preview/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-email/preview/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Preview: () => (/* binding */ Preview),\n/* harmony export */   renderWhiteSpace: () => (/* binding */ renderWhiteSpace)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/preview.tsx\n\n\nvar PREVIEW_MAX_LENGTH = 150;\nvar Preview = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children = \"\" } = _b, props = __objRest(_b, [\"children\"]);\n  let text = Array.isArray(children) ? children.join(\"\") : children;\n  text = text.substr(0, PREVIEW_MAX_LENGTH);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\n    \"div\",\n    __spreadProps(__spreadValues({\n      style: {\n        display: \"none\",\n        overflow: \"hidden\",\n        lineHeight: \"1px\",\n        opacity: 0,\n        maxHeight: 0,\n        maxWidth: 0\n      }\n    }, props), {\n      ref,\n      children: [\n        text,\n        renderWhiteSpace(text)\n      ]\n    })\n  );\n});\nPreview.displayName = \"Preview\";\nvar renderWhiteSpace = (text) => {\n  if (text.length >= PREVIEW_MAX_LENGTH) {\n    return null;\n  }\n  const whiteSpaceCodes = \"\\xA0\\u200C\\u200B\\u200D\\u200E\\u200F\\uFEFF\";\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", { children: whiteSpaceCodes.repeat(PREVIEW_MAX_LENGTH - text.length) });\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/preview/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/section/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@react-email/section/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Section: () => (/* binding */ Section)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/section.tsx\n\n\nvar Section = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { children, style } = _b, props = __objRest(_b, [\"children\", \"style\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"table\",\n    __spreadProps(__spreadValues({\n      align: \"center\",\n      width: \"100%\"\n    }, props), {\n      border: 0,\n      cellPadding: \"0\",\n      cellSpacing: \"0\",\n      ref,\n      role: \"presentation\",\n      style,\n      children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tbody\", { children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"tr\", { children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"td\", { children }) }) })\n    })\n  );\n});\nSection.displayName = \"Section\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/section/dist/index.mjs\n");

/***/ }),

/***/ "(action-browser)/./node_modules/@react-email/text/dist/index.mjs":
/*!*******************************************************!*\
  !*** ./node_modules/@react-email/text/dist/index.mjs ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Text: () => (/* binding */ Text)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(action-browser)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/text.tsx\n\n\nvar Text = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a, ref) => {\n  var _b = _a, { style } = _b, props = __objRest(_b, [\"style\"]);\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\n    \"p\",\n    __spreadProps(__spreadValues({}, props), {\n      ref,\n      style: __spreadValues({\n        fontSize: \"14px\",\n        lineHeight: \"24px\",\n        margin: \"16px 0\"\n      }, style)\n    })\n  );\n});\nText.displayName = \"Text\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@react-email/text/dist/index.mjs\n");

/***/ })

};
;