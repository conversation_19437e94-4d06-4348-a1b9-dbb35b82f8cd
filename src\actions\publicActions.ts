'use server';
import { db } from '@/lib/firebase-admin';
import type { CourseFeeStructure } from '@/types';

const COURSE_FEE_STRUCTURES_COLLECTION = 'courseFeeStructures';

/**
 * Fetches the fee structure for a given course. This is a public action and does not require authentication.
 * @param courseId The ID of the course.
 * @returns The course fee structure or null if not found.
 */
export async function getPublicCourseFeeStructure(courseId: string): Promise<CourseFeeStructure | null> {
  const docRef = db.collection(COURSE_FEE_STRUCTURES_COLLECTION).doc(courseId);
  const docSnap = await docRef.get();

  if (docSnap.exists) {
    return JSON.parse(JSON.stringify({ id: docSnap.id, ...docSnap.data() } as CourseFeeStructure));
  } else {
    return null;
  }
}
