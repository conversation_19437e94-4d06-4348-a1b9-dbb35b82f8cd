"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/apply/page",{

/***/ "(app-pages-browser)/./src/components/apply/ApplicationForm.tsx":
/*!**************************************************!*\
  !*** ./src/components/apply/ApplicationForm.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApplicationForm: () => (/* binding */ ApplicationForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(app-pages-browser)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! react-hook-form */ \"(app-pages-browser)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/dist/esm/index.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/form */ \"(app-pages-browser)/./src/components/ui/form.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Loader2,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var firebase_auth__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! firebase/auth */ \"(app-pages-browser)/./node_modules/firebase/auth/dist/esm/index.esm.js\");\n/* harmony import */ var _actions_applicationActions__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/actions/applicationActions */ \"(app-pages-browser)/./src/actions/applicationActions.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ ApplicationForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst educationLevels = [\n    {\n        id: '10th',\n        label: '10th Grade',\n        yearFieldName: 'tenthCompletionYear'\n    },\n    {\n        id: '12th',\n        label: '12th Grade',\n        yearFieldName: 'twelfthCompletionYear'\n    },\n    {\n        id: 'diploma',\n        label: 'Diploma',\n        yearFieldName: 'diplomaCompletionYear'\n    },\n    {\n        id: 'graduate',\n        label: \"Bachelor's Degree\",\n        yearFieldName: 'graduateCompletionYear'\n    },\n    {\n        id: 'masters',\n        label: \"Master's Degree\",\n        yearFieldName: 'mastersCompletionYear'\n    }\n];\nconst currentYear = new Date().getFullYear();\nconst minBirthYear = currentYear - 100;\nconst minCompletionYear = currentYear - 50;\nconst applicationFormSchema = zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n    fullName: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(2, {\n        message: 'Full name must be at least 2 characters.'\n    }),\n    email: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().email({\n        message: 'Please enter a valid email address.'\n    }),\n    mobileNumber: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().regex(/^[6-9]\\d{9}$/, {\n        message: 'Please enter a valid 10-digit Indian mobile number starting with 6, 7, 8, or 9.'\n    }),\n    motherName: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(2, {\n        message: \"Mother's name must be at least 2 characters.\"\n    }),\n    fatherName: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(2, {\n        message: \"Father's name must be at least 2 characters.\"\n    }),\n    dob_day: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1, {\n        message: 'Day is required.'\n    }),\n    dob_month: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1, {\n        message: 'Month is required.'\n    }),\n    dob_year: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1, {\n        message: 'Year is required.'\n    }),\n    religion: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(2, {\n        message: \"Religion is required.\"\n    }),\n    desiredCourse: zod__WEBPACK_IMPORTED_MODULE_2__.z.string({\n        required_error: 'Please select a course.'\n    }).min(1, {\n        message: 'Please select a course.'\n    }),\n    previousEducation: zod__WEBPACK_IMPORTED_MODULE_2__.z.array(zod__WEBPACK_IMPORTED_MODULE_2__.z.string()).nonempty({\n        message: 'Please select at least one qualification.'\n    }),\n    reference: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(1, {\n        message: \"Reference is required.\"\n    }),\n    password: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().min(6, {\n        message: 'Password must be at least 6 characters.'\n    }).optional(),\n    confirmPassword: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n    educationDetails: zod__WEBPACK_IMPORTED_MODULE_2__.z.object({\n        tenthCompletionYear: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n        twelfthCompletionYear: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n        diplomaCompletionYear: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n        graduateCompletionYear: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional(),\n        mastersCompletionYear: zod__WEBPACK_IMPORTED_MODULE_2__.z.string().optional()\n    }).optional()\n}).refine((data)=>{\n    if (data.password) {\n        return data.password === data.confirmPassword;\n    }\n    return true;\n}, {\n    message: \"Passwords don't match\",\n    path: [\n        \"confirmPassword\"\n    ]\n}).superRefine((data, ctx)=>{\n    // --- Date of Birth validation ---\n    if (data.dob_day && data.dob_month && data.dob_year) {\n        const day = parseInt(data.dob_day, 10);\n        const month = parseInt(data.dob_month, 10);\n        const year = parseInt(data.dob_year, 10);\n        const date = new Date(year, month - 1, day);\n        if (isNaN(date.getTime()) || date.getFullYear() !== year || date.getMonth() + 1 !== month || date.getDate() !== day) {\n            ctx.addIssue({\n                code: zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodIssueCode.custom,\n                message: \"Invalid date.\",\n                path: [\n                    \"dob_day\"\n                ]\n            });\n        } else if (year > currentYear || year < minBirthYear) {\n            ctx.addIssue({\n                code: zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodIssueCode.custom,\n                message: \"Year must be after \".concat(minBirthYear, \".\"),\n                path: [\n                    \"dob_year\"\n                ]\n            });\n        }\n    }\n    // --- Conditional Education Details validation ---\n    const selectedEducation = data.previousEducation || [];\n    const educationDetails = data.educationDetails || {};\n    educationLevels.forEach((level)=>{\n        if (selectedEducation.includes(level.id)) {\n            const yearValue = educationDetails[level.yearFieldName];\n            const fieldPath = \"educationDetails.\".concat(level.yearFieldName);\n            if (!yearValue || yearValue.trim() === '') {\n                ctx.addIssue({\n                    code: zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodIssueCode.custom,\n                    message: \"Year is required.\",\n                    path: [\n                        fieldPath\n                    ]\n                });\n            } else if (!/^\\d{4}$/.test(yearValue)) {\n                ctx.addIssue({\n                    code: zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodIssueCode.custom,\n                    message: \"Must be a 4-digit year.\",\n                    path: [\n                        fieldPath\n                    ]\n                });\n            } else {\n                const numericYear = parseInt(yearValue, 10);\n                if (numericYear > currentYear || numericYear < minCompletionYear) {\n                    ctx.addIssue({\n                        code: zod__WEBPACK_IMPORTED_MODULE_2__.z.ZodIssueCode.custom,\n                        message: \"Year must be between \".concat(minCompletionYear, \" and \").concat(currentYear, \".\"),\n                        path: [\n                            fieldPath\n                        ]\n                    });\n                }\n            }\n        }\n    });\n});\nfunction ApplicationForm(param) {\n    let { courses: availableCourses } = param;\n    var _form_formState_errors_previousEducation;\n    _s();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_14__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_9__.useState)(false);\n    const form = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_15__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_1__.zodResolver)(applicationFormSchema),\n        defaultValues: {\n            fullName: (user === null || user === void 0 ? void 0 : user.name) || '',\n            email: (user === null || user === void 0 ? void 0 : user.email) || '',\n            mobileNumber: '',\n            motherName: '',\n            fatherName: '',\n            dob_day: '',\n            dob_month: '',\n            dob_year: '',\n            religion: '',\n            previousEducation: [],\n            reference: '',\n            password: '',\n            confirmPassword: '',\n            educationDetails: {\n                tenthCompletionYear: '',\n                twelfthCompletionYear: '',\n                diplomaCompletionYear: '',\n                graduateCompletionYear: '',\n                mastersCompletionYear: ''\n            }\n        },\n        mode: 'onBlur'\n    });\n    const watchedPreviousEducation = form.watch('previousEducation') || [];\n    const groupedCourses = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)({\n        \"ApplicationForm.useMemo[groupedCourses]\": ()=>{\n            return availableCourses.reduce({\n                \"ApplicationForm.useMemo[groupedCourses]\": (acc, course)=>{\n                    const groupName = \"\".concat(course.name, \" (\").concat(course.code, \")\");\n                    if (!acc[groupName]) {\n                        acc[groupName] = [];\n                    }\n                    acc[groupName].push(course);\n                    return acc;\n                }\n            }[\"ApplicationForm.useMemo[groupedCourses]\"], {});\n        }\n    }[\"ApplicationForm.useMemo[groupedCourses]\"], [\n        availableCourses\n    ]);\n    async function onSubmit(data) {\n        if (isSubmitting) {\n            return;\n        }\n        setIsSubmitting(true);\n        let userId = user === null || user === void 0 ? void 0 : user.uid;\n        let createdUser = false;\n        try {\n            var _selectedCourse_specializations;\n            if (!userId) {\n                if (!data.password) {\n                    toast({\n                        title: 'Password Required',\n                        description: 'Please provide a password to create your account.',\n                        variant: 'destructive'\n                    });\n                    setIsSubmitting(false);\n                    return;\n                }\n                const userCredential = await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_11__.createUserWithEmailAndPassword)(_lib_firebase__WEBPACK_IMPORTED_MODULE_10__.auth, data.email, data.password);\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_11__.updateProfile)(userCredential.user, {\n                    displayName: data.fullName\n                });\n                userId = userCredential.user.uid;\n                createdUser = true;\n            } else {\n                if (_lib_firebase__WEBPACK_IMPORTED_MODULE_10__.auth.currentUser && _lib_firebase__WEBPACK_IMPORTED_MODULE_10__.auth.currentUser.displayName !== data.fullName) {\n                    await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_11__.updateProfile)(_lib_firebase__WEBPACK_IMPORTED_MODULE_10__.auth.currentUser, {\n                        displayName: data.fullName\n                    });\n                }\n            }\n            const dateOfBirthString = (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_16__.format)(new Date(parseInt(data.dob_year), parseInt(data.dob_month) - 1, parseInt(data.dob_day)), 'yyyy-MM-dd');\n            const selectedCourse = availableCourses.find((c)=>c.id === data.desiredCourse);\n            const specialization = selectedCourse === null || selectedCourse === void 0 ? void 0 : (_selectedCourse_specializations = selectedCourse.specializations) === null || _selectedCourse_specializations === void 0 ? void 0 : _selectedCourse_specializations[0];\n            const applicationPayload = {\n                fullName: data.fullName,\n                email: data.email,\n                mobileNumber: data.mobileNumber,\n                motherName: data.motherName,\n                fatherName: data.fatherName,\n                dateOfBirth: dateOfBirthString,\n                religion: data.religion,\n                desiredCourse: data.desiredCourse,\n                specialization,\n                previousEducation: data.previousEducation,\n                reference: data.reference,\n                userId: userId,\n                educationDetails: data.educationDetails\n            };\n            await (0,_actions_applicationActions__WEBPACK_IMPORTED_MODULE_12__.createApplication)(applicationPayload);\n            if (createdUser) {\n                await (0,firebase_auth__WEBPACK_IMPORTED_MODULE_11__.signOut)(_lib_firebase__WEBPACK_IMPORTED_MODULE_10__.auth);\n            }\n            router.push('/apply/success');\n        } catch (error) {\n            console.error(\"Error during application submission or user creation:\", error);\n            let errorMessage = 'An unexpected error occurred. Please try again.';\n            if (error.code) {\n                switch(error.code){\n                    case 'auth/email-already-in-use':\n                        errorMessage = 'This email address is already in use. Please try logging in or use a different email.';\n                        break;\n                    case 'auth/weak-password':\n                        errorMessage = 'The password is too weak. Please choose a stronger password.';\n                        break;\n                    default:\n                        errorMessage = \"Registration failed: \".concat(error.message);\n                }\n            }\n            toast({\n                title: 'Submission Failed',\n                description: errorMessage,\n                variant: 'destructive'\n            });\n        } finally{\n            setIsSubmitting(false);\n        }\n    }\n    const years = Array.from({\n        length: currentYear - minBirthYear + 1\n    }, (_, i)=>currentYear - i);\n    const completionYears = Array.from({\n        length: currentYear - minCompletionYear + 1\n    }, (_, i)=>currentYear - i);\n    const months = [\n        {\n            value: '1',\n            label: 'January'\n        },\n        {\n            value: '2',\n            label: 'February'\n        },\n        {\n            value: '3',\n            label: 'March'\n        },\n        {\n            value: '4',\n            label: 'April'\n        },\n        {\n            value: '5',\n            label: 'May'\n        },\n        {\n            value: '6',\n            label: 'June'\n        },\n        {\n            value: '7',\n            label: 'July'\n        },\n        {\n            value: '8',\n            label: 'August'\n        },\n        {\n            value: '9',\n            label: 'September'\n        },\n        {\n            value: '10',\n            label: 'October'\n        },\n        {\n            value: '11',\n            label: 'November'\n        },\n        {\n            value: '12',\n            label: 'December'\n        }\n    ];\n    const days = Array.from({\n        length: 31\n    }, (_, i)=>i + 1);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.Form, {\n        ...form,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: form.handleSubmit(onSubmit),\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                    control: form.control,\n                    name: \"fullName\",\n                    render: (param)=>{\n                        let { field } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                    children: \"Full Name\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"John Doe\",\n                                        ...field,\n                                        disabled: isSubmitting\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 17\n                                    }, void 0)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 13\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-6 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                            control: form.control,\n                            name: \"motherName\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                            children: \"Mother's Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"Jane Doe\",\n                                                ...field,\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 290,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 17\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 283,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                            control: form.control,\n                            name: \"fatherName\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                            children: \"Father's Name\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"Richard Doe\",\n                                                ...field,\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 296,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-6 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                    children: \"Date of Birth\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-3 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                            control: form.control,\n                                            name: \"dob_day\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                                    className: \"flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            onValueChange: field.onChange,\n                                                            value: field.value,\n                                                            disabled: isSubmitting,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                            placeholder: \"Day\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                            lineNumber: 322,\n                                                                            columnNumber: 42\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: days.map((day)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: String(day),\n                                                                            children: day\n                                                                        }, day, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                            lineNumber: 325,\n                                                                            columnNumber: 44\n                                                                        }, void 0))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 324,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {\n                                                            className: \"pt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                            lineNumber: 328,\n                                                            columnNumber: 23\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                            control: form.control,\n                                            name: \"dob_month\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                                    className: \"flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            onValueChange: field.onChange,\n                                                            value: field.value,\n                                                            disabled: isSubmitting,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                            placeholder: \"Month\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                            lineNumber: 339,\n                                                                            columnNumber: 42\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                        lineNumber: 339,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 338,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: months.map((month)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: month.value,\n                                                                            children: month.label\n                                                                        }, month.value, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                            lineNumber: 342,\n                                                                            columnNumber: 48\n                                                                        }, void 0))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 341,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                            lineNumber: 337,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {\n                                                            className: \"pt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 24\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 332,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                            control: form.control,\n                                            name: \"dob_year\",\n                                            render: (param)=>{\n                                                let { field } = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                                    className: \"flex flex-col\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                            onValueChange: field.onChange,\n                                                            value: field.value,\n                                                            disabled: isSubmitting,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                            placeholder: \"Year\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                            lineNumber: 356,\n                                                                            columnNumber: 42\n                                                                        }, void 0)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                        lineNumber: 356,\n                                                                        columnNumber: 27\n                                                                    }, void 0)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 355,\n                                                                    columnNumber: 25\n                                                                }, void 0),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                                    children: years.map((year)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                            value: String(year),\n                                                                            children: year\n                                                                        }, year, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                            lineNumber: 359,\n                                                                            columnNumber: 46\n                                                                        }, void 0))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 358,\n                                                                    columnNumber: 25\n                                                                }, void 0)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 23\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {\n                                                            className: \"pt-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                            lineNumber: 362,\n                                                            columnNumber: 24\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 353,\n                                                    columnNumber: 21\n                                                }, void 0);\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                            control: form.control,\n                            name: \"religion\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                            children: \"Religion\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                placeholder: \"e.g., Hinduism, Christianity\",\n                                                ...field,\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 21\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 17\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 12\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-6 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                            control: form.control,\n                            name: \"email\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                            children: \"Email Address\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"email\",\n                                                placeholder: \"<EMAIL>\",\n                                                ...field,\n                                                disabled: isSubmitting || !!user\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                            control: form.control,\n                            name: \"mobileNumber\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                            children: \"Mobile Number\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 403,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"tel\",\n                                                placeholder: \"9876543210\",\n                                                ...field,\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 407,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 384,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                    control: form.control,\n                    name: \"desiredCourse\",\n                    render: (param)=>{\n                        let { field } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                    children: \"Desired Course\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: field.onChange,\n                                    value: field.value,\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: \"Select a course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: Object.keys(groupedCourses).length > 0 ? Object.entries(groupedCourses).map((param)=>{\n                                                let [groupName, coursesInGroup] = param;\n                                                if (coursesInGroup.length === 1) {\n                                                    const course = coursesInGroup[0];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                        value: course.id,\n                                                        children: groupName\n                                                    }, course.id, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                } else {\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectGroup, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectLabel, {\n                                                                children: groupName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 29\n                                                            }, void 0),\n                                                            coursesInGroup.map((course)=>{\n                                                                var _course_specializations;\n                                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: course.id,\n                                                                    children: ((_course_specializations = course.specializations) === null || _course_specializations === void 0 ? void 0 : _course_specializations[0]) || 'General'\n                                                                }, course.id, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 443,\n                                                                    columnNumber: 31\n                                                                }, void 0);\n                                                            })\n                                                        ]\n                                                    }, groupName, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 27\n                                                    }, void 0);\n                                                }\n                                            }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                value: \"no-courses\",\n                                                disabled: true,\n                                                children: \"No courses available\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 22\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 13\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                    className: \"text-base\",\n                                    children: \"Previous Education / Qualifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormDescription, {\n                                    children: \"Select all that apply. Completion year will be required for selected items.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 462,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                control: form.control,\n                                name: \"previousEducation\",\n                                render: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: educationLevels.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                                control: form.control,\n                                                name: \"previousEducation\",\n                                                render: (param)=>{\n                                                    let { field } = param;\n                                                    var _field_value;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                                        className: \"flex flex-row items-center space-x-3 space-y-0\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_7__.Checkbox, {\n                                                                    checked: (_field_value = field.value) === null || _field_value === void 0 ? void 0 : _field_value.includes(item.id),\n                                                                    onCheckedChange: (checked)=>{\n                                                                        var _field_value, _form_formState_errors_educationDetails;\n                                                                        const newValue = checked ? [\n                                                                            ...field.value || [],\n                                                                            item.id\n                                                                        ] : (_field_value = field.value) === null || _field_value === void 0 ? void 0 : _field_value.filter((value)=>value !== item.id);\n                                                                        field.onChange(newValue);\n                                                                        // Trigger validation for the related year field when checkbox changes\n                                                                        if (!checked && ((_form_formState_errors_educationDetails = form.formState.errors.educationDetails) === null || _form_formState_errors_educationDetails === void 0 ? void 0 : _form_formState_errors_educationDetails[item.yearFieldName])) {\n                                                                            form.clearErrors(\"educationDetails.\".concat(item.yearFieldName));\n                                                                        }\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                    lineNumber: 486,\n                                                                    columnNumber: 41\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 41\n                                                            }, void 0),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                                className: \"font-normal\",\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 41\n                                                            }, void 0)\n                                                        ]\n                                                    }, item.id, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 37\n                                                    }, void 0);\n                                                }\n                                            }, item.id, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 29\n                                            }, void 0))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 21\n                                    }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 468,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {\n                            children: (_form_formState_errors_previousEducation = form.formState.errors.previousEducation) === null || _form_formState_errors_previousEducation === void 0 ? void 0 : _form_formState_errors_previousEducation.message\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 514,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 9\n                }, this),\n                watchedPreviousEducation.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4 rounded-md border p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-sm font-medium text-foreground\",\n                            children: \"Please provide completion years:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: educationLevels.map((level)=>{\n                                if (watchedPreviousEducation.includes(level.id)) {\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                                        control: form.control,\n                                        name: \"educationDetails.\".concat(level.yearFieldName),\n                                        render: (param)=>{\n                                            let { field } = param;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                                        children: [\n                                                            level.label,\n                                                            \" Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                            type: \"number\",\n                                                            placeholder: \"YYYY\",\n                                                            ...field,\n                                                            disabled: isSubmitting\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 29\n                                                        }, void 0)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                        lineNumber: 532,\n                                                        columnNumber: 27\n                                                    }, void 0),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                        lineNumber: 540,\n                                                        columnNumber: 27\n                                                    }, void 0)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 530,\n                                                columnNumber: 25\n                                            }, void 0);\n                                        }\n                                    }, \"\".concat(level.id, \"-year-input\"), false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 21\n                                    }, this);\n                                }\n                                return null;\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 519,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                    control: form.control,\n                    name: \"reference\",\n                    render: (param)=>{\n                        let { field } = param;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                    children: \"Reference\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 557,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                    onValueChange: field.onChange,\n                                    value: field.value,\n                                    disabled: isSubmitting,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                    placeholder: \"How did you hear about us?\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 561,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 560,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"Friend\",\n                                                    children: \"Friend / Referral\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"Social Media\",\n                                                    children: \"Social Media\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 566,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"Online Ad\",\n                                                    children: \"Online Advertisement\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"Search Engine\",\n                                                    children: \"Search Engine (Google, etc.)\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"Event\",\n                                                    children: \"Educational Event\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 569,\n                                                    columnNumber: 19\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                    value: \"Other\",\n                                                    children: \"Other\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                    lineNumber: 570,\n                                                    columnNumber: 19\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 564,\n                                            columnNumber: 17\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, void 0),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 15\n                                }, void 0)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 13\n                        }, void 0);\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 552,\n                    columnNumber: 9\n                }, this),\n                !user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 gap-6 md:grid-cols-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                            control: form.control,\n                            name: \"password\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                            children: \"Create a Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 585,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"password\",\n                                                placeholder: \"••••••••\",\n                                                ...field,\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 587,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 589,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 584,\n                                    columnNumber: 21\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 580,\n                            columnNumber: 17\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormField, {\n                            control: form.control,\n                            name: \"confirmPassword\",\n                            render: (param)=>{\n                                let { field } = param;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormItem, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormLabel, {\n                                            children: \"Confirm Password\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormControl, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                type: \"password\",\n                                                placeholder: \"••••••••\",\n                                                ...field,\n                                                disabled: isSubmitting\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                                lineNumber: 600,\n                                                columnNumber: 25\n                                            }, void 0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 599,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_form__WEBPACK_IMPORTED_MODULE_4__.FormMessage, {}, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                            lineNumber: 602,\n                                            columnNumber: 21\n                                        }, void 0)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                                    lineNumber: 597,\n                                    columnNumber: 21\n                                }, void 0);\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 17\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 579,\n                    columnNumber: 13\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    type: \"submit\",\n                    className: \"w-full bg-accent hover:bg-accent/90 text-accent-foreground\",\n                    disabled: isSubmitting || availableCourses.length === 0,\n                    children: [\n                        isSubmitting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"mr-2 h-4 w-4 animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 27\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Loader2_Send_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                            lineNumber: 611,\n                            columnNumber: 79\n                        }, this),\n                        user ? 'Submit Application' : 'Create Account & Submit Application'\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n                    lineNumber: 610,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n            lineNumber: 268,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\apply\\\\ApplicationForm.tsx\",\n        lineNumber: 267,\n        columnNumber: 5\n    }, this);\n}\n_s(ApplicationForm, \"0iHfpl7HmGcS8iASgYoEvieq8D0=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_13__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_14__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        react_hook_form__WEBPACK_IMPORTED_MODULE_15__.useForm\n    ];\n});\n_c = ApplicationForm;\nvar _c;\n$RefreshReg$(_c, \"ApplicationForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/apply/ApplicationForm.tsx\n"));

/***/ })

});