
'use client';

import * as React from 'react';
import { <PERSON>, <PERSON>Chart, CartesianGrid, XAxis, YAxis } from 'recharts';
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig,
} from '@/components/ui/chart';
import type { Lead } from '@/types';
import { Skeleton } from '@/components/ui/skeleton';

interface LeadsByStatusChartProps {
  leads: Lead[];
  isLoading: boolean;
}

const chartConfig = {
  count: {
    label: 'Leads',
    color: 'hsl(var(--chart-1))',
  },
} satisfies ChartConfig;

export function LeadsByStatusChart({ leads, isLoading }: LeadsByStatusChartProps) {
  const chartData = React.useMemo(() => {
    if (isLoading) {
      // Return dummy data for skeleton layout
      return [
        { status: 'New', count: 0 },
        { status: 'Contacted', count: 0 },
        { status: 'Qualified', count: 0 },
        { status: 'Lost', count: 0 },
      ];
    }

    const statusCounts: Record<Lead['status'], number> = {
      New: 0,
      Contacted: 0,
      Qualified: 0,
      Lost: 0,
    };

    for (const lead of leads) {
      if (statusCounts.hasOwnProperty(lead.status)) {
        statusCounts[lead.status]++;
      }
    }

    return Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
    }));
  }, [leads, isLoading]);

  if (isLoading) {
    return (
      <div className="min-h-[300px] w-full flex items-end gap-4 p-4">
          <Skeleton className="h-full w-full" />
      </div>
    )
  }

  return (
    <ChartContainer config={chartConfig} className="min-h-[300px] w-full">
      <BarChart data={chartData} accessibilityLayer>
        <CartesianGrid vertical={false} />
        <XAxis
          dataKey="status"
          tickLine={false}
          tickMargin={10}
          axisLine={false}
          stroke="#888888"
          fontSize={12}
        />
        <YAxis
          stroke="#888888"
          fontSize={12}
          tickLine={false}
          axisLine={false}
          allowDecimals={false}
        />
        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent indicator="dot" />}
        />
        <Bar dataKey="count" fill="hsl(var(--primary))" radius={4} />
      </BarChart>
    </ChartContainer>
  );
}
