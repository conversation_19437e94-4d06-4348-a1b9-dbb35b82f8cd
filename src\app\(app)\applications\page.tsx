
'use client';
import { useState, useEffect, useCallback } from 'react';
import type { StudentApplication, Course } from '@/types';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, CheckCircle, XCircle, MoreHorizontal, Filter, FileText, Loader2, Trash2 } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { type DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useToast } from '@/hooks/use-toast';
import { getApplications, updateApplicationStatus, deleteApplication } from '@/actions/applicationActions';
import { getCourses } from '@/actions/courseActions';
import { ApplicationDetailsDialog } from '@/components/applications/ApplicationDetailsDialog';
import { auth } from '@/lib/firebase';
import { Skeleton } from '@/components/ui/skeleton';

type Checked = DropdownMenuCheckboxItemProps["checked"]

const statusVariantMap: Record<StudentApplication['status'], 'default' | 'secondary' | 'outline' | 'destructive'> = {
  Pending: 'default',
  Reviewed: 'secondary',
  Accepted: 'outline', 
  Rejected: 'destructive',
};

export default function ApplicationsPage() {
  const [applications, setApplications] = useState<StudentApplication[]>([]);
  const [courses, setCourses] = useState<Course[]>([]); 
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const [filterStatus, setFilterStatus] = useState<Record<StudentApplication['status'], Checked>>({
    Pending: true,
    Reviewed: true,
    Accepted: true,
    Rejected: true,
  });
  const [selectedApplication, setSelectedApplication] = useState<StudentApplication | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [applicationToDelete, setApplicationToDelete] = useState<StudentApplication | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const fetchPageData = useCallback(async () => {
    setIsLoading(true);
    try {
      if (!auth.currentUser) {
        toast({ title: "Unauthorized", description: "You must be logged in to view applications.", variant: "destructive" });
        setIsLoading(false);
        return;
      }
      const idToken = await auth.currentUser.getIdToken();

      const [fetchedApplications, fetchedCourses] = await Promise.all([
        getApplications(idToken),
        getCourses()
      ]);
      setApplications(fetchedApplications);
      setCourses(fetchedCourses);
    } catch (error) {
      console.error("Failed to fetch page data:", error);
      const errorMessage = error instanceof Error ? error.message : "Could not fetch applications or courses.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

   useEffect(() => {
    fetchPageData();
  }, [fetchPageData]);

  const handleStatusChange = async (applicationId: string, newStatus: StudentApplication['status']) => {
    try {
      if (!auth.currentUser) {
        throw new Error("You must be logged in to perform this action.");
      }
      const idToken = await auth.currentUser.getIdToken();
      const updatedApplication = await updateApplicationStatus(applicationId, newStatus, idToken);
      if (updatedApplication) {
        setApplications(prev => 
          prev.map(app => app.id === applicationId ? { ...app, status: newStatus } : app)
        );
        toast({
          title: "Status Updated",
          description: `Application status changed to ${newStatus}.`,
        });
        if (newStatus === 'Accepted') {
          fetchPageData();
        }
      } else {
        throw new Error("Application not found or update failed.");
      }
    } catch (error) {
      console.error("Failed to update status:", error);
      const errorMessage = error instanceof Error ? error.message : "Could not update application status.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const handleViewDetails = (application: StudentApplication) => {
    setSelectedApplication(application);
    setIsDetailsDialogOpen(true);
  };

  const openDeleteDialog = (application: StudentApplication) => {
    setApplicationToDelete(application);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirmed = async () => {
    if (!applicationToDelete) return;

    setIsDeleting(true);
    try {
      if (!auth.currentUser) throw new Error("Authentication required.");
      const idToken = await auth.currentUser.getIdToken();
      await deleteApplication(applicationToDelete.id, idToken);

      setApplications(prev => prev.filter(app => app.id !== applicationToDelete.id));
      toast({
        title: "Application Deleted",
        description: `The application for ${applicationToDelete.fullName} has been permanently removed.`,
      });
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Failed to delete application:", error);
      const errorMessage = error instanceof Error ? error.message : "Could not delete the application.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setApplicationToDelete(null);
    }
  };

  const filteredApplications = applications.filter(app => filterStatus[app.status]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">Student Applications</h1>
            <p className="text-muted-foreground">Review and manage incoming student applications.</p>
          </div>
          <Skeleton className="h-10 w-40" />
        </div>
        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle><Skeleton className="h-7 w-48" /></CardTitle>
            <CardDescription><Skeleton className="h-4 w-64" /></CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-lg border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead><Skeleton className="h-5 w-24" /></TableHead>
                    <TableHead><Skeleton className="h-5 w-32" /></TableHead>
                    <TableHead><Skeleton className="h-5 w-40" /></TableHead>
                    <TableHead><Skeleton className="h-5 w-28" /></TableHead>
                    <TableHead><Skeleton className="h-5 w-20" /></TableHead>
                    <TableHead className="text-right"><Skeleton className="h-5 w-16" /></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-28" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20 rounded-full" /></TableCell>
                      <TableCell className="text-right"><Skeleton className="h-8 w-8" /></TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }


  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">Student Applications</h1>
            <p className="text-muted-foreground">Review and manage incoming student applications.</p>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="mr-2 h-4 w-4" /> Filter Status
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56">
              <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {(Object.keys(filterStatus) as Array<StudentApplication['status']>).map((status) => (
                <DropdownMenuCheckboxItem
                  key={status}
                  checked={filterStatus[status]}
                  onCheckedChange={(checked) => setFilterStatus(prev => ({...prev, [status]: checked}))}
                >
                  {status}
                </DropdownMenuCheckboxItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="font-headline text-xl">Application Queue</CardTitle>
            <CardDescription>All submitted student applications.</CardDescription>
          </CardHeader>
          <CardContent>
            {filteredApplications.length === 0 ? (
              <div className="text-center py-10 text-muted-foreground">
                <FileText className="mx-auto h-12 w-12 mb-4" />
                <p className="text-xl font-semibold">No applications found.</p>
                <p>Check filters or wait for new submissions.</p>
              </div>
            ) : (
            <div className="rounded-lg border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Applicant Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredApplications.map((app) => (
                    <TableRow key={app.id}>
                      <TableCell className="font-medium">{app.fullName}</TableCell>
                      <TableCell>{app.email}</TableCell>
                      <TableCell>{courses.find(c => c.id === app.desiredCourse)?.name || app.desiredCourse}</TableCell>
                      <TableCell>{new Date(app.applicationDate).toLocaleDateString()}</TableCell>
                      <TableCell>
                        <Badge variant={statusVariantMap[app.status] || 'default'}>{app.status}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleViewDetails(app)}>
                              <Eye className="mr-2 h-4 w-4" /> View Full Application
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handleStatusChange(app.id, 'Reviewed')}>
                              Mark as Reviewed
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleStatusChange(app.id, 'Accepted')} className="text-green-600 focus:text-green-700">
                              <CheckCircle className="mr-2 h-4 w-4" /> Accept Application
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleStatusChange(app.id, 'Rejected')} className="text-red-600 focus:text-red-700">
                              <XCircle className="mr-2 h-4 w-4" /> Reject Application
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => openDeleteDialog(app)} className="text-destructive focus:text-destructive">
                               <Trash2 className="mr-2 h-4 w-4" /> Delete Application
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            )}
          </CardContent>
        </Card>
      </div>
      {selectedApplication && (
        <ApplicationDetailsDialog
          application={selectedApplication}
          courses={courses}
          open={isDetailsDialogOpen}
          onOpenChange={setIsDetailsDialogOpen}
        />
      )}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the
              application for <span className="font-semibold">{applicationToDelete?.fullName}</span>. 
              This does not delete the associated user account.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirmed}
              disabled={isDeleting}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
