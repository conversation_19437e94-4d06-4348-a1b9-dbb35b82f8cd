self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"40e323d092f4fe6243e29231a0ad5ad2f5c7876fcd\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"60303f2b9af336075ec77d0b18e89d61fde457cab0\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"700b35b9f722b57616199b50e07d3a3b7ab763800d\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"406f03995b60825f4e2bea909d60056c2ccd1ee92f\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"602274c78dc569bffbff02a4276368f3d4067ed319\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"606eb0d3fb0c4eae1cc864c78c10446dd0626f2304\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"60987cf7fb8cce7b6dc798e83a43be4fa20f8292eb\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"0070fe5732caf243aef8b39bb6a8ede9079e8e425d\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    },\n    \"606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CapplicationActions.ts%22%2C%5B%7B%22id%22%3A%2240900b861f6f45eccaa2a9da8b3d5c1dba5b2ec3de%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240e323d092f4fe6243e29231a0ad5ad2f5c7876fcd%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%2260303f2b9af336075ec77d0b18e89d61fde457cab0%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700b35b9f722b57616199b50e07d3a3b7ab763800d%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CcourseActions.ts%22%2C%5B%7B%22id%22%3A%22005d4a5b437a0b6d24c6eca0aa2755c4f6f403d542%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%22406f03995b60825f4e2bea909d60056c2ccd1ee92f%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%22602274c78dc569bffbff02a4276368f3d4067ed319%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%22606eb0d3fb0c4eae1cc864c78c10446dd0626f2304%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%2C%7B%22id%22%3A%2260987cf7fb8cce7b6dc798e83a43be4fa20f8292eb%22%2C%22exportedName%22%3A%22updateCourse%22%7D%5D%5D%2C%5B%22C%3A%5C%5CCode%5C%5Cedulite%5C%5Csrc%5C%5Cactions%5C%5CsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220070fe5732caf243aef8b39bb6a8ede9079e8e425d%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%22606c35f9b2c14eb4a7fe45fafcfbc4c9655b84cd09%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"