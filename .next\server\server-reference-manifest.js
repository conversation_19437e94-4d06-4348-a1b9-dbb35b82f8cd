self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"409f6ff83b9b73a53624cfc2f5d187c74987a5053c\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"40fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"700448f4f2e40efadb47c3fb96528a3f14ea9412fc\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"009c0144138e71f927f6b6128abac9af653ace8e6a\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"40c42c05097c34fba00d481a7a13b45d7e2a441500\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"60007135f09206169d61b1879131f2b188f6ca7145\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"604272f45f8bbd0468962eb9ac5e035ac45411e62b\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"60d4a7dc7a318f1476dbfc45480ce527f41a74a116\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"0019407feca76ba7040707b01ae4a047038925003a\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/login/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/login/page\": \"action-browser\",\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/settings/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"60f7303ab06601c74445c84a8fcdd854f22649025e\": {\n      \"workers\": {\n        \"app/apply/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/login/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/apply/page\": \"action-browser\",\n        \"app/login/page\": \"action-browser\",\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/settings/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"400961ae80af3e5bf40f87603a262f1c996bb15d07\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/settings/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"40b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/settings/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"40fa6a9a84abf529eb18d5444b85f0886286a8c164\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/settings/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"60102ed85997759b8f5cfbb7f89738b8355c44fffb\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\",\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/settings/page\": \"action-browser\",\n        \"app/(app)/my-courses/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"4005e26bff2f5631a710d6de50448396377cb049a7\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"403a2ab99a6f29c23abb715882adfe852a85332922\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"608e5e0073fe55d01ee36c06e951f36b122b117626\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"609d200741b72cb238bfcd9dd47885c203c8677d15\": {\n      \"workers\": {\n        \"app/(app)/dashboard/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FleadActions.ts%22%2C%5B%7B%22id%22%3A%224005e26bff2f5631a710d6de50448396377cb049a7%22%2C%22exportedName%22%3A%22createLead%22%7D%2C%7B%22id%22%3A%22403a2ab99a6f29c23abb715882adfe852a85332922%22%2C%22exportedName%22%3A%22getLeads%22%7D%2C%7B%22id%22%3A%22608e5e0073fe55d01ee36c06e951f36b122b117626%22%2C%22exportedName%22%3A%22updateLead%22%7D%2C%7B%22id%22%3A%22609d200741b72cb238bfcd9dd47885c203c8677d15%22%2C%22exportedName%22%3A%22deleteLead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/dashboard/page\": \"action-browser\"\n      }\n    },\n    \"40593e880ef14d60b9c3b1724b24fd07f88a0cbf40\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"40cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"6027867f25ea8a390912c6bae666e619eeb6ccd7f7\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"6038bb83c9531228a7731771b9e5433d3b4bcc1c9e\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"60432c95134d581a0e62c0af2908cffbf09a881c19\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"604e6831fd424189a1e924ebf784ccd255e74e136f\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"609c03b98fb8ecc43b490dbbbf47c3876540279004\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"60e1a170d4ae6e047da18471d53ea9ec9a653092dd\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"60f52ec005ff077fa447ce3825bce1eba1c90b3f13\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"70103bd2fdb69234881fdfaa2e53c8db37416591f1\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"709d8b9e8f51fd98b937102a71a15851e86dcb6f6e\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"70f11fdc673437874199a82af360c9280176f11784\": {\n      \"workers\": {\n        \"app/(app)/fee-management/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        },\n        \"app/(app)/my-fees/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FfeeActions.ts%22%2C%5B%7B%22id%22%3A%2240593e880ef14d60b9c3b1724b24fd07f88a0cbf40%22%2C%22exportedName%22%3A%22sendOverdueFeeReminders%22%7D%2C%7B%22id%22%3A%2240cbf3afc6f726fa6b225b0b25cd9dd0ace8bb18c7%22%2C%22exportedName%22%3A%22getStudentFees%22%7D%2C%7B%22id%22%3A%226027867f25ea8a390912c6bae666e619eeb6ccd7f7%22%2C%22exportedName%22%3A%22getFinancialReportData%22%7D%2C%7B%22id%22%3A%226038bb83c9531228a7731771b9e5433d3b4bcc1c9e%22%2C%22exportedName%22%3A%22getCourseFeeStructureByCourseId%22%7D%2C%7B%22id%22%3A%2260432c95134d581a0e62c0af2908cffbf09a881c19%22%2C%22exportedName%22%3A%22getStudentFeesByStudentId%22%7D%2C%7B%22id%22%3A%22604e6831fd424189a1e924ebf784ccd255e74e136f%22%2C%22exportedName%22%3A%22createStudentFee%22%7D%2C%7B%22id%22%3A%22609c03b98fb8ecc43b490dbbbf47c3876540279004%22%2C%22exportedName%22%3A%22recordManualPayment%22%7D%2C%7B%22id%22%3A%2260e1a170d4ae6e047da18471d53ea9ec9a653092dd%22%2C%22exportedName%22%3A%22deleteStudentFee%22%7D%2C%7B%22id%22%3A%2260f52ec005ff077fa447ce3825bce1eba1c90b3f13%22%2C%22exportedName%22%3A%22getStudentFeeById%22%7D%2C%7B%22id%22%3A%2270103bd2fdb69234881fdfaa2e53c8db37416591f1%22%2C%22exportedName%22%3A%22saveCourseFeeStructure%22%7D%2C%7B%22id%22%3A%22709d8b9e8f51fd98b937102a71a15851e86dcb6f6e%22%2C%22exportedName%22%3A%22updateFeeStatus%22%7D%2C%7B%22id%22%3A%2270f11fdc673437874199a82af360c9280176f11784%22%2C%22exportedName%22%3A%22generateFeeForEnrolledStudents%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/fee-management/page\": \"action-browser\",\n        \"app/(app)/my-fees/page\": \"action-browser\"\n      }\n    },\n    \"404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00\": {\n      \"workers\": {\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/settings/page\": \"action-browser\"\n      }\n    },\n    \"40b42655cdd814dd1970e5d17ac8ad8e94f45d5148\": {\n      \"workers\": {\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/settings/page\": \"action-browser\"\n      }\n    },\n    \"40bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f\": {\n      \"workers\": {\n        \"app/(app)/settings/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FhealthCheckActions.ts%22%2C%5B%7B%22id%22%3A%22404fb6fae3f9ebbc199b0059a85f7abcda95f4ea00%22%2C%22exportedName%22%3A%22runFirestoreHealthCheck%22%7D%2C%7B%22id%22%3A%2240b42655cdd814dd1970e5d17ac8ad8e94f45d5148%22%2C%22exportedName%22%3A%22verifyAdminStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FadminActions.ts%22%2C%5B%7B%22id%22%3A%2240bb9e9c8ea50c05354ca5ed1bbffb900648f40e1f%22%2C%22exportedName%22%3A%22forceLogoutAllUsers%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/settings/page\": \"action-browser\"\n      }\n    },\n    \"60540a5dc37ad10a33704bdc442140d37e0158fe0b\": {\n      \"workers\": {\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"70baad81b25b10f8f95cace4326786c3c47a925834\": {\n      \"workers\": {\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    },\n    \"784354c75bcfd2a0ffe175a72bf36b617ad70903a4\": {\n      \"workers\": {\n        \"app/(app)/my-courses/page\": {\n          \"moduleId\": \"(action-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FsettingsActions.ts%22%2C%5B%7B%22id%22%3A%220019407feca76ba7040707b01ae4a047038925003a%22%2C%22exportedName%22%3A%22getSystemSettings%22%7D%2C%7B%22id%22%3A%2260f7303ab06601c74445c84a8fcdd854f22649025e%22%2C%22exportedName%22%3A%22updateSystemSettings%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FnotificationActions.ts%22%2C%5B%7B%22id%22%3A%22400961ae80af3e5bf40f87603a262f1c996bb15d07%22%2C%22exportedName%22%3A%22markAllNotificationsAsRead%22%7D%2C%7B%22id%22%3A%2240b07e2f689be8ff8b38e4cdefae147bc2cdf4c9e8%22%2C%22exportedName%22%3A%22getNotifications%22%7D%2C%7B%22id%22%3A%2240fa6a9a84abf529eb18d5444b85f0886286a8c164%22%2C%22exportedName%22%3A%22createNotification%22%7D%2C%7B%22id%22%3A%2260102ed85997759b8f5cfbb7f89738b8355c44fffb%22%2C%22exportedName%22%3A%22markNotificationAsRead%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FapplicationActions.ts%22%2C%5B%7B%22id%22%3A%22409f6ff83b9b73a53624cfc2f5d187c74987a5053c%22%2C%22exportedName%22%3A%22createApplication%22%7D%2C%7B%22id%22%3A%2240fb21fee9e6a92fa1b5c09a3bfb1b0d543eacbded%22%2C%22exportedName%22%3A%22getApplications%22%7D%2C%7B%22id%22%3A%22608ad7fbb633a27d096ad5b7ff8c37be90d5ab5e3a%22%2C%22exportedName%22%3A%22deleteApplication%22%7D%2C%7B%22id%22%3A%22700448f4f2e40efadb47c3fb96528a3f14ea9412fc%22%2C%22exportedName%22%3A%22updateApplicationStatus%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FcourseActions.ts%22%2C%5B%7B%22id%22%3A%22009c0144138e71f927f6b6128abac9af653ace8e6a%22%2C%22exportedName%22%3A%22getCourses%22%7D%2C%7B%22id%22%3A%2240c42c05097c34fba00d481a7a13b45d7e2a441500%22%2C%22exportedName%22%3A%22getCourseById%22%7D%2C%7B%22id%22%3A%2260007135f09206169d61b1879131f2b188f6ca7145%22%2C%22exportedName%22%3A%22updateCourse%22%7D%2C%7B%22id%22%3A%22604272f45f8bbd0468962eb9ac5e035ac45411e62b%22%2C%22exportedName%22%3A%22createCourse%22%7D%2C%7B%22id%22%3A%2260d4a7dc7a318f1476dbfc45480ce527f41a74a116%22%2C%22exportedName%22%3A%22deleteCourse%22%7D%5D%5D%2C%5B%22%2Fhome%2Fuser%2FEduLite%2Fsrc%2Factions%2FprogressActions.ts%22%2C%5B%7B%22id%22%3A%2260540a5dc37ad10a33704bdc442140d37e0158fe0b%22%2C%22exportedName%22%3A%22getStudentProgressForAllCourses%22%7D%2C%7B%22id%22%3A%2270baad81b25b10f8f95cace4326786c3c47a925834%22%2C%22exportedName%22%3A%22getStudentProgressForCourse%22%7D%2C%7B%22id%22%3A%22784354c75bcfd2a0ffe175a72bf36b617ad70903a4%22%2C%22exportedName%22%3A%22updateStudentProgress%22%7D%5D%5D%5D&__client_imported__=true!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(app)/my-courses/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"