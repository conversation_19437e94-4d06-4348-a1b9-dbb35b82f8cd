
'use server';
import { adminAuth } from './firebase-admin';
import type { DecodedIdToken } from 'firebase-admin/auth';

type Role = 'admin' | 'student' | 'accountant';

/**
 * Verifies a Firebase ID token and checks if the user has one of the allowed roles.
 * @param idToken The Firebase ID token string from the client.
 * @param allowedRoles An array of roles that are allowed to perform the action.
 * @returns The decoded token if verification and authorization are successful.
 * @throws An error if the token is invalid or the user is not authorized.
 */
export async function requireAuth(idToken: string, allowedRoles: Role[]): Promise<{ uid: string; token: DecodedIdToken }> {
  if (!adminAuth) {
    throw new Error('Admin SDK not initialized. Check server logs.');
  }

  if (!idToken) {
    throw new Error('Unauthorized: No ID token provided.');
  }

  try {
    const decodedToken = await adminAuth.verifyIdToken(idToken);
    
    // Default to 'student' role if the custom claim is missing or invalid.
    // This makes the server-side authorization consistent with the client-side role determination.
    const userRole: Role = 
      (decodedToken.role && ['admin', 'student', 'accountant'].includes(decodedToken.role as string))
      ? decodedToken.role as Role
      : 'student';

    // Now, we inject this determined role back into the token object for consistent use in downstream functions.
    // This avoids having to repeat the defaulting logic in every server action.
    const augmentedToken = { ...decodedToken, role: userRole };

    if (!allowedRoles.includes(userRole)) {
      // Log the specific reason for denial on the server for easier debugging.
      console.error(
        `Authorization failed for UID: ${decodedToken.uid}. ` +
        `User role: '${userRole}'. Required one of: [${allowedRoles.join(', ')}].`
      );
      throw new Error(`Forbidden: You do not have the required permissions to perform this action.`);
    }

    return { uid: decodedToken.uid, token: augmentedToken };
  } catch (error) {
    // Log the original error for server-side debugging
    console.error('Auth check failed:', error);
    
    // Re-throw a more generic error to the client to avoid leaking implementation details,
    // unless it's one of our specific authorization errors.
    if (error instanceof Error && (error.message.startsWith('Forbidden:') || error.message.startsWith('Unauthorized:'))) {
        throw error;
    }

    throw new Error('Authentication failed. Please sign in again.');
  }
}
