
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { createLead } from '@/actions/leadActions';
import type { Course, Lead } from '@/types';
import { Send, Loader2 } from 'lucide-react';
import { useState, useMemo } from 'react';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';

const leadCaptureFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  desiredCourse: z.string({ required_error: 'Please select a course.' }).min(1, { message: 'Please select a course.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  phone: z.string().regex(/^[6-9]\d{9}$/, { message: 'Please enter a valid 10-digit Indian mobile number.' }),
});

type LeadCaptureFormValues = z.infer<typeof leadCaptureFormSchema>;

export function LeadCaptureForm({ courses: availableCourses }: { courses: Course[] }) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<LeadCaptureFormValues>({
    resolver: zodResolver(leadCaptureFormSchema),
    defaultValues: {
      name: '',
      desiredCourse: '',
      email: '',
      phone: '',
    },
  });
  
  const groupedCourses = useMemo(() => {
    return availableCourses.reduce<Record<string, Course[]>>((acc, course) => {
      const groupName = `${course.name} (${course.code})`;
      if (!acc[groupName]) {
        acc[groupName] = [];
      }
      acc[groupName].push(course);
      return acc;
    }, {});
  }, [availableCourses]);

  async function onSubmit(data: LeadCaptureFormValues) {
    setIsSubmitting(true);
    try {
      const leadPayload: Omit<Lead, 'id' | 'inquiryDate' | 'status'> = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        source: 'QR Code Scan', // Automatically set source
        desiredCourse: data.desiredCourse,
        notes: '', // Notes can be added later by an admin
      };
      
      await createLead(leadPayload);

      toast({
        title: 'Inquiry Submitted!',
        description: `Thank you, ${data.name}. We have received your inquiry and will be in touch soon.`,
        variant: "default"
      });
      form.reset();

    } catch (error: any) {
      console.error("Error during lead submission:", error);
      toast({
        title: 'Submission Failed',
        description: error.message || 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Full Name</FormLabel>
              <FormControl>
                <Input placeholder="Your Name" {...field} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="desiredCourse"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Desired Course</FormLabel>
              <Select onValueChange={field.onChange} value={field.value} disabled={isSubmitting}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={"Select a course"} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {Object.keys(groupedCourses).length > 0 ? (
                    Object.entries(groupedCourses).map(([groupName, coursesInGroup]) => (
                      <SelectGroup key={groupName}>
                        <SelectLabel>{groupName}</SelectLabel>
                        {coursesInGroup.map(course => {
                          if (course.specializations && course.specializations.length > 0) {
                            return course.specializations.map(spec => (
                              <SelectItem key={`${course.id}-${spec}`} value={course.id}>
                                {spec}
                              </SelectItem>
                            ));
                          }
                          // Fallback for courses with no specializations
                          return (
                            <SelectItem key={course.id} value={course.id}>
                              General
                            </SelectItem>
                          );
                        })}
                      </SelectGroup>
                    ))
                  ) : (
                     <SelectItem value="no-courses" disabled>No courses available</SelectItem>
                  )}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email Address</FormLabel>
              <FormControl>
                <Input type="email" placeholder="<EMAIL>" {...field} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="phone"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mobile Number</FormLabel>
              <FormControl>
                <Input type="tel" placeholder="Your Mobile Number" {...field} disabled={isSubmitting} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <Button type="submit" className="w-full bg-accent hover:bg-accent/90 text-accent-foreground" disabled={isSubmitting || availableCourses.length === 0}>
          {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Send className="mr-2 h-4 w-4" />}
          Submit Inquiry
        </Button>
      </form>
    </Form>
  );
}
