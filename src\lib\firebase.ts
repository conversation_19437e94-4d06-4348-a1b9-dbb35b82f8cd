// Import the functions you need from the SDKs you need
import { initializeApp, getApps, getApp, type FirebaseApp } from "firebase/app";
import { getAuth, type Auth } from "firebase/auth";
import { getAnalytics, isSupported, type Analytics } from "firebase/analytics";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyB4Ul5GltonJL8bMA7OVYD07lmyCNEqIjc",
  authDomain: "edulite-vivekananda.firebaseapp.com",
  projectId: "edulite-vivekananda",
  storageBucket: "edulite-vivekananda.appspot.com",
  messagingSenderId: "987108410600",
  appId: "1:987108410600:web:6891cbc37cf341afdc355d",
  measurementId: "G-P3L2G9SB7P"
};

const app: FirebaseApp = getApps().length ? getApp() : initializeApp(firebaseConfig);
const auth: Auth = getAuth(app);
let analytics: Analytics | undefined;

// Initialize Analytics only on the client side, and only if supported.
if (typeof window !== 'undefined') {
    isSupported().then((supported) => {
        if (supported) {
            analytics = getAnalytics(app);
        }
    });
}

export { app, auth, analytics };
