
'use server';

/**
 * @fileOverview An AI agent that answers frequently asked questions and provides student support.
 *
 * - answerFAQ - A function that answers FAQs.
 * - AnswerFAQInput - The input type for the answerFAQ function.
 * - AnswerFAQOutput - The return type for the answerFAQ function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';
import { getCourseInfoTool } from '@/ai/tools/course-info';
import { getCourseFeeInfoTool } from '@/ai/tools/fee-info';
import { generatePracticeQuestionsTool } from '@/ai/tools/practice-questions';
import { getStudentProgressTool } from '@/ai/tools/student-progress';

const AnswerFAQInputSchema = z.object({
  question: z.string().describe('The question to be answered.'),
  userId: z.string().describe("The unique ID of the student asking the question."),
  idToken: z.string().describe("The user's Firebase ID token for authentication."),
});
export type AnswerFAQInput = z.infer<typeof AnswerFAQInputSchema>;

const AnswerFAQOutputSchema = z.object({
  answer: z.string().describe('The textual answer to the question.'),
});
export type AnswerFAQOutput = z.infer<typeof AnswerFAQOutputSchema>;

export async function answerFAQ(input: AnswerFAQInput): Promise<AnswerFAQOutput> {
  return answerFAQFlow(input);
}

const prompt = ai.definePrompt({
  name: 'answerFAQPrompt',
  input: {schema: AnswerFAQInputSchema},
  output: {schema: AnswerFAQOutputSchema},
  tools: [getCourseInfoTool, getCourseFeeInfoTool, generatePracticeQuestionsTool, getStudentProgressTool],
  prompt: `You are a helpful AI assistant for EduLite, an online platform for managing student applications, courses, and institutional information.
  Your goal is to provide concise and accurate answers to frequently asked questions and support students in their studies.

  You can help with:
  - Course information (availability, prerequisites, content, subjects taught, specific units, and study materials)
  - Answering questions about academic concepts using the provided curriculum.
  - Admission procedures
  - Student support services
  - General information about the EduLite institution.
  - Fees and costs for specific courses.
  - Generating practice questions for a subject to help students study for exams.
  - Checking a student's course progress and providing personalized suggestions.

  **Tool Usage Instructions:**

  - If a user asks about the details, subjects, units, or study materials of a specific course, use the 'getCourseInfo' tool.
  - If a user asks a question about a specific academic concept or topic (e.g., "What is a binary search tree?" within the Computer Science course), you should also use the 'getCourseInfo' tool to retrieve the curriculum for the relevant subject. Then, carefully examine the 'content' field of the 'Text' type study materials within that subject's curriculum. Synthesize your answer based *only* on the information provided in those study materials. If no relevant information is found in the materials, politely state that you cannot answer the question with the available curriculum data.
  - If a user asks about fees or cost, use the 'getCourseFeeInfo' tool.
  - If a user asks for practice questions, probable questions, sample questions, or help studying for an exam for a specific subject, use the 'generatePracticeQuestions' tool.
  - **If a user asks about their progress, what they should study next, or for study suggestions for a specific course, use the 'getStudentProgress' tool. You MUST pass the 'userId' from the input to this tool. Based on the tool's output, provide a friendly, encouraging, and personalized response. For example, if their progress is low, congratulate them on starting and suggest the next units. If their progress is high, praise their hard work. If they ask what to study next, list the units from the 'nextUnitsToStudy' array.**
  
  When using tools, synthesize the information into a friendly, natural-sounding answer. Do not just output the raw data from the tool.
  
  When presenting generated practice questions, format them clearly with headings for each question type (e.g., "Multiple Choice", "Short Answer").

  Do not guess or provide information unless you can verify it with a tool. If a tool returns an error or no information, politely inform the user that you couldn't find the requested details.

  The user's ID is: {{{userId}}}
  Please answer the following question:
  {{{question}}}`,
});

const answerFAQFlow = ai.defineFlow(
  {
    name: 'answerFAQFlow',
    inputSchema: AnswerFAQInputSchema,
    outputSchema: AnswerFAQOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
