# EduLite - Project Status & Roadmap

This document provides a high-level overview of the EduLite project's current status and outlines the next steps toward a production-ready application.

## I. Critical Next Steps (MVP Blockers)

The application's foundation is strong, but the following items are essential before it can be considered a Minimum Viable Product (MVP).

1.  **Transition from Mock Data to Firestore Database**:
    *   **Priority**: Highest. This is the most critical task remaining.
    *   **Task**: All server actions in `src/actions/*.ts` currently use temporary mock data from `src/lib/mockData.ts`. Each action must be rewritten to perform its CRUD (Create, Read, Update, Delete) operations against a Firebase Firestore database.
    *   **Impact**: This will make all data (Leads, Courses, Applications, Fees, etc.) persistent and real.

2.  **Secure All Server Actions**:
    *   **Priority**: High.
    *   **Task**: While some actions are protected, a full security audit is needed. Systematically apply the `requireAuth` security function to all sensitive server actions in `src/actions/*.ts` to ensure only users with the correct roles can perform specific operations.
    *   **Impact**: Prevents unauthorized data access and modification.

---

## II. Feature Status & Enhancements

This section details the features that are functionally complete from a UI and workflow perspective (pending the database integration mentioned above).

*   **User Authentication & Roles**: `(Completed)`
    *   Handles sign-up and sign-in with email/password.
    *   Robust role management ('admin', 'student', 'accountant') using Firebase Custom Claims.
    *   Correctly redirects users based on their role and application status.

*   **Application Process (`/apply`)**: `(Completed)`
    *   New users can create an account and submit a detailed application in a single, streamlined flow.
    *   The form includes comprehensive fields and validation.

*   **Course Management (`/courses`)**: `(Completed)`
    *   Admins can create, update, and delete courses.
    *   Includes a **Curriculum Manager** to define a full course hierarchy: Semesters -> Subjects -> Units -> Study Materials.
    *   Includes a **Fee Structure Manager** to define all costs associated with a course.

*   **Application Management (`/applications`)**: `(Completed)`
    *   Admins and Accountants can view and filter all student applications.
    *   Admins can change an application's status.
    *   **Automation**: Accepting an application automatically generates the correct fee records for the student based on the course's fee structure.

*   **Fee Management (`/fee-management`)**: `(Completed for Phase 1)`
    *   Accountants and Admins can view and manage all student fee records.
    *   Supports recording full or partial payments.
    *   Provides a link to a printable HTML invoice for each fee record.

*   **Student Portal (`/my-courses`)**: `(Completed)`
    *   Students have a personalized dashboard showing only their enrolled courses.
    *   They can view detailed course information, including the full curriculum (`Semester > Subject > Unit`) and study material links.
    *   Displays a summary of their fee status and course progress for each course.

*   **Lead Management (`/dashboard`)**: `(Completed for Phase 1)`
    *   Admins can view, create, edit, and delete leads.
    *   The dashboard includes basic statistics.

*   **AI Assistant (`/ai-assistant`)**: `(Completed for Phase 1)`
    *   A basic FAQ assistant is functional and integrated into the UI.

*   **Settings Page (`/settings`)**: `(Completed)`
    *   Users can update their display name and change their password.
    *   Admins have access to a special "Admin Zone" for high-privilege actions.

---

## III. Future Work (Post-MVP)

Once the critical items in Section I are complete, these features would be the next logical enhancements.

*   **Dashboard Enhancements**: Add real-data charts and an "Export Leads" feature (e.g., to CSV).
*   **Advanced Fee Management**: Generate PDF invoices, create financial reports, and implement automated overdue notices.
*   **AI Assistant Enhancements**: Integrate Genkit tools to allow the AI to fetch real-time information from the database (e.g., "What's the fee for CS101?", "What are the prerequisites for the MBA?").
*   **Notifications**: Implement email or in-app notifications for key events like application submission, status changes, and fee due dates.
*   **General UI/UX Polish**: Add more skeleton loaders for a smoother perceived performance, enhance global error handling, and conduct a thorough accessibility review.
*   **Academic Progress Tracking**: The current course progress bar is based on time. A future enhancement would be to track student grades, assignment submissions, and overall academic performance within each course.
