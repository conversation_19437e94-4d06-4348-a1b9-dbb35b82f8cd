
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Logo } from '@/components/navigation/Logo';
import Link from 'next/link';
import { CheckCircle } from 'lucide-react';

export default function ApplySuccessPage() {
  return (
    <div className="min-h-screen bg-background py-8 px-4 flex flex-col items-center justify-center">
      <div className="mb-8">
        <Logo size="lg" />
      </div>
      <Card className="w-full max-w-lg shadow-xl text-center">
        <CardHeader>
            <div className="mx-auto bg-green-100 rounded-full h-16 w-16 flex items-center justify-center">
                <CheckCircle className="h-10 w-10 text-green-600" />
            </div>
          <CardTitle className="font-headline text-3xl text-primary pt-4">Application Submitted!</CardTitle>
          <CardDescription>Thank you for applying to EduLite. Your application is now under review.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
            <p className="text-muted-foreground">
                You will receive an email notification once a decision has been made. 
                If your application is accepted, you will then be able to log in to the student portal using the credentials you provided.
            </p>
            <Button asChild className="w-full bg-primary hover:bg-primary/90">
                <Link href="/login">
                    Back to Login Page
                </Link>
            </Button>
        </CardContent>
      </Card>
    </div>
  );
}
