
'use server';
/**
 * @fileOverview Genkit tool to retrieve and analyze a student's progress in a course.
 */

import { ai } from '@/ai/genkit';
import { getCourses } from '@/actions/courseActions';
import { z } from 'zod';
import { db } from '@/lib/firebase-admin'; // Use admin DB
import type { StudentCourseProgress, Unit } from '@/types';

export const getStudentProgressTool = ai.defineTool(
  {
    name: 'getStudentProgress',
    description: "Get a student's current progress for a specific course, including completion percentage and a list of what to study next. Use this if the user asks about their progress, what they've completed, or what they should study next.",
    inputSchema: z.object({
      userId: z.string().describe("The unique ID of the student asking the question."),
      courseIdentifier: z.string().describe('The name or code of the course, e.g., "Bachelor of Computer Applications" or "BCA".'),
    }),
    outputSchema: z.union([
      z.object({
        courseName: z.string(),
        completedUnitsCount: z.number(),
        totalUnitsCount: z.number(),
        progressPercentage: z.number(),
        nextUnitsToStudy: z.array(z.string()).describe("A list of the names of the next few uncompleted units to suggest to the student."),
      }),
      z.object({ error: z.string() })
    ]),
  },
  async ({ userId, courseIdentifier }) => {
    try {
        const allCourses = await getCourses();
        const normalizedIdentifier = courseIdentifier.toLowerCase().trim();
        
        const course = allCourses.find(c => 
            c.name.toLowerCase().includes(normalizedIdentifier) || 
            c.code.toLowerCase() === normalizedIdentifier
        );

        if (!course) {
          return { error: `Course "${courseIdentifier}" not found.` };
        }

        const allUnits: Unit[] = course.semesters?.flatMap(
            semester => semester.subjects.flatMap(subject => subject.units || [])
        ) || [];

        const totalUnitsCount = allUnits.length;
        if (totalUnitsCount === 0) {
            return { error: `The course "${course.name}" has no units defined, so progress cannot be tracked.` };
        }

        // Fetch progress directly using Admin SDK
        const progressQuery = db.collection('studentCourseProgress')
                                .where('studentId', '==', userId)
                                .where('courseId', '==', course.id)
                                .limit(1);
        
        const snapshot = await progressQuery.get();
        
        let completedUnits = new Set<string>();
        if (!snapshot.empty) {
            const progressDoc = snapshot.docs[0].data() as StudentCourseProgress;
            completedUnits = new Set(progressDoc.completedUnits);
        }

        const completedUnitsCount = completedUnits.size;
        const progressPercentage = Math.round((completedUnitsCount / totalUnitsCount) * 100);

        const uncompletedUnits = allUnits.filter(unit => !completedUnits.has(unit.id));

        // Suggest the next 3 uncompleted units
        const nextUnitsToStudy = uncompletedUnits.slice(0, 3).map(unit => unit.name);
        
        return {
          courseName: course.name,
          completedUnitsCount,
          totalUnitsCount,
          progressPercentage,
          nextUnitsToStudy,
        };
    } catch (e: any) {
        console.error("Error in getStudentProgressTool:", e);
        return { error: `An internal error occurred while fetching student progress.` };
    }
  }
);
