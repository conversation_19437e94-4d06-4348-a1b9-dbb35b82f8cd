"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(app)/financial-reports/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FinancialReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _actions_feeActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/actions/feeActions */ \"(app-pages-browser)/./src/actions/feeActions.ts\");\n/* harmony import */ var _actions_courseActions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/courseActions */ \"(app-pages-browser)/./src/actions/courseActions.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter-x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/piggy-bank.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-bar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst feeStatuses = [\n    'Pending',\n    'Paid',\n    'Partially Paid',\n    'Overdue'\n];\nconst statusVariantMap = {\n    Pending: 'default',\n    'Partially Paid': 'secondary',\n    Paid: 'outline',\n    Overdue: 'destructive'\n};\nfunction FinancialReportsPage() {\n    _s();\n    const { user, isLoading: isAuthLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined); // Start with no date filter to show all records\n    const [courseId, setCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [paymentTransactions, setPaymentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchReportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FinancialReportsPage.useCallback[fetchReportData]\": async ()=>{\n            if (!user || !_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser) return;\n            setIsLoading(true);\n            try {\n                const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser.getIdToken();\n                const filters = {\n                    startDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, 'yyyy-MM-dd') : undefined,\n                    endDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.to) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, 'yyyy-MM-dd') : undefined,\n                    courseId: courseId || undefined,\n                    status: status || undefined\n                };\n                const [data, transactions] = await Promise.all([\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getFinancialReportData)(filters, idToken),\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getAllPaymentTransactions)(idToken)\n                ]);\n                setReportData(data);\n                setPaymentTransactions(transactions);\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"Could not fetch report data.\";\n                toast({\n                    title: \"Error\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"FinancialReportsPage.useCallback[fetchReportData]\"], [\n        user,\n        dateRange,\n        courseId,\n        status,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            async function fetchInitialCourses() {\n                try {\n                    const fetchedCourses = await (0,_actions_courseActions__WEBPACK_IMPORTED_MODULE_3__.getCourses)();\n                    setCourses(fetchedCourses);\n                } catch (error) {\n                    toast({\n                        title: \"Error\",\n                        description: \"Could not load courses for filtering.\",\n                        variant: \"destructive\"\n                    });\n                }\n            }\n            fetchInitialCourses();\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            if (!isAuthLoading && user) {\n                fetchReportData();\n            }\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        fetchReportData,\n        isAuthLoading,\n        user\n    ]);\n    const clearFilters = ()=>{\n        setDateRange(undefined); // Clear date range to show all records\n        setCourseId('');\n        setStatus('');\n        // Fetch data immediately after clearing filters\n        setTimeout(()=>fetchReportData(), 100);\n    };\n    const summaryStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FinancialReportsPage.useMemo[summaryStats]\": ()=>{\n            const totalDue = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalDue\": (sum, fee)=>sum + fee.amountDue\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalDue\"], 0);\n            const totalPaid = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalPaid\": (sum, fee)=>sum + fee.amountPaid\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalPaid\"], 0);\n            const totalOutstanding = totalDue - totalPaid;\n            return {\n                totalDue,\n                totalPaid,\n                totalOutstanding,\n                recordCount: reportData.length\n            };\n        }\n    }[\"FinancialReportsPage.useMemo[summaryStats]\"], [\n        reportData\n    ]);\n    const exportToCSV = ()=>{\n        if (reportData.length === 0) {\n            toast({\n                title: \"No data to export\",\n                variant: \"default\"\n            });\n            return;\n        }\n        const headers = [\n            'ID',\n            'Student Name',\n            'Course Name',\n            'Fee Description',\n            'Amount Due',\n            'Amount Paid',\n            'Remaining',\n            'Due Date',\n            'Status',\n            'Last Payment Date'\n        ];\n        const csvRows = [\n            headers.join(',')\n        ];\n        for (const fee of reportData){\n            const remaining = fee.amountDue - fee.amountPaid;\n            const values = [\n                fee.id,\n                '\"'.concat(fee.studentName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.courseName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.feeDescription.replace(/\"/g, '\"\"'), '\"'),\n                fee.amountDue.toFixed(2),\n                fee.amountPaid.toFixed(2),\n                remaining.toFixed(2),\n                fee.dueDate,\n                fee.status,\n                fee.lastPaymentDate || ''\n            ];\n            csvRows.push(values.join(','));\n        }\n        const csvString = csvRows.join('\\n');\n        const blob = new Blob([\n            csvString\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"financial_report_\".concat(new Date().toISOString().split('T')[0], \".csv\"));\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                        children: \"Financial Reports\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Generate and view financial reports based on various filters.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                            children: \"Report Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Due Date Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"justify-start text-left font-normal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 33\n                                                        }, this),\n                                                        (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? dateRange.to ? \"\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\"), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, \"LLL dd, y\")) : (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\") : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Pick a date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                className: \"w-auto p-0\",\n                                                align: \"start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                    mode: \"range\",\n                                                    selected: dateRange,\n                                                    onSelect: setDateRange,\n                                                    initialFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: courseId || '_all_',\n                                        onValueChange: (value)=>setCourseId(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Courses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Courses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    courses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                            value: course.id,\n                                                            children: course.name\n                                                        }, course.id, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 52\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: status || '_all_',\n                                        onValueChange: (value)=>setStatus(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    feeStatuses.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                            value: s,\n                                                            className: \"capitalize\",\n                                                            children: s\n                                                        }, s, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 51\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: fetchReportData,\n                                        disabled: isLoading,\n                                        className: \"w-full\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 38\n                                        }, this) : 'Apply Filters'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: clearFilters,\n                                        disabled: isLoading,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Collected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalPaid.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Outstanding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-destructive\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalOutstanding.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 14\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Filtered Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: summaryStats.recordCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 14\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        children: \"Filtered Fee Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                        children: [\n                                            \"A detailed list of fee records matching the selected filters. Found \",\n                                            reportData.length,\n                                            \" records.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                onClick: exportToCSV,\n                                disabled: isLoading || reportData.length === 0,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 22\n                        }, this) : reportData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No records match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Due Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Due\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Paid\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableBody, {\n                                        children: reportData.map((fee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: fee.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.courseName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.feeDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(fee.dueDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                            variant: statusVariantMap[fee.status],\n                                                            className: \"capitalize\",\n                                                            children: fee.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 52\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountDue.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountPaid.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, fee.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Payment Transaction History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 25\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                            variant: \"outline\",\n                                            children: [\n                                                paymentTransactions.length,\n                                                \" transactions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                children: \"Detailed history of all payment transactions recorded in the system.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 22\n                        }, this) : paymentTransactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No payment transactions found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 315,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Payment Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Payment Method\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Transaction ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Recorded By\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Notes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 320,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableBody, {\n                                        children: paymentTransactions.map((transaction)=>{\n                                            var _reportData_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(transaction.paymentDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: transaction.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: ((_reportData_find = reportData.find((fee)=>fee.id === transaction.feeId)) === null || _reportData_find === void 0 ? void 0 : _reportData_find.feeDescription) || 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: transaction.paymentMethod\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 343,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 342,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            transaction.amount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 345,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.transactionId || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.recordedBy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.notes || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, transaction.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 37\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 319,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 318,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 308,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(FinancialReportsPage, \"y8jEenDhakqQ36qS1wk5rlTo0OI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = FinancialReportsPage;\nvar _c;\n$RefreshReg$(_c, \"FinancialReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx\n"));

/***/ })

});