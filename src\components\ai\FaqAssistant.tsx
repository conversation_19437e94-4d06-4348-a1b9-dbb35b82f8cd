
'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Loader2, SendHorizonal, Bot, User } from 'lucide-react';
import { answerFAQ, type AnswerFAQOutput } from '@/ai/flows/answer-faq';
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';

interface Message {
  id: string;
  text: string;
  sender: 'user' | 'ai';
}

export function FaqAssistant() {
  const [question, setQuestion] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const handleAskQuestion = async () => {
    if (!question.trim() || !user || !auth.currentUser) return;

    const userMessage: Message = { id: `user-${Date.now()}`, text: question, sender: 'user' };
    setMessages(prev => [...prev, userMessage]);
    setQuestion('');
    setIsLoading(true);

    try {
      const idToken = await auth.currentUser.getIdToken();
      const result: AnswerFAQOutput = await answerFAQ({ question, userId: user.uid, idToken });

      if (result.answer) {
        const aiMessage: Message = { id: `ai-${Date.now()}`, text: result.answer, sender: 'ai' };
        setMessages(prev => [...prev, aiMessage]);
      }
      
    } catch (error) {
      console.error('Error in AI flow:', error);
      const errorMessageText = error instanceof Error ? error.message : "An unknown error occurred.";
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        text: `Sorry, I encountered an error: ${errorMessageText}`,
        sender: 'ai',
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };
  
  if (!user) {
    return (
        <Card className="w-full max-w-2xl mx-auto shadow-xl flex flex-col h-[70vh]">
            <CardHeader className="border-b">
                <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10 border-2 border-primary">
                        <AvatarFallback><Bot className="text-primary" /></AvatarFallback>
                    </Avatar>
                    <div>
                        <CardTitle className="font-headline text-2xl text-primary">AI Assistant</CardTitle>
                        <CardDescription>Ask about courses, your progress, or get study help.</CardDescription>
                    </div>
                </div>
            </CardHeader>
            <CardContent className="flex-grow flex items-center justify-center">
                <p className="text-muted-foreground">Please log in to use the AI assistant.</p>
            </CardContent>
        </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-xl flex flex-col h-[70vh]">
      <CardHeader className="border-b">
        <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10 border-2 border-primary">
                <AvatarFallback><Bot className="text-primary" /></AvatarFallback>
            </Avatar>
            <div>
                <CardTitle className="font-headline text-2xl text-primary">AI Assistant</CardTitle>
                <CardDescription>Ask about courses, fees, your progress, or get study help.</CardDescription>
            </div>
        </div>
      </CardHeader>
      <CardContent className="p-0 flex-grow flex flex-col">
        <ScrollArea className="flex-grow p-6 space-y-4">
          {messages.map((msg) => (
            <div
              key={msg.id}
              className={`flex items-end gap-2 ${msg.sender === 'user' ? 'justify-end' : ''}`}
            >
              {msg.sender === 'ai' && (
                <Avatar className="h-8 w-8">
                  <AvatarFallback><Bot className="text-accent" /></AvatarFallback>
                </Avatar>
              )}
              <div
                className={`max-w-[70%] rounded-xl px-4 py-3 shadow-md ${
                  msg.sender === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                <p className="text-sm whitespace-pre-wrap">{msg.text}</p>
              </div>
              {msg.sender === 'user' && (
                <Avatar className="h-8 w-8">
                  <AvatarFallback><User /></AvatarFallback>
                </Avatar>
              )}
            </div>
          ))}
          {isLoading && (
            <div className="flex items-end gap-2">
              <Avatar className="h-8 w-8">
                <AvatarFallback><Bot className="text-accent" /></AvatarFallback>
              </Avatar>
              <div className="max-w-[70%] rounded-xl px-4 py-3 shadow-md bg-muted text-muted-foreground">
                <Loader2 className="h-5 w-5 animate-spin" />
              </div>
            </div>
          )}
        </ScrollArea>
        <div className="border-t p-4 bg-background/50">
          <div className="flex items-center gap-2">
            <Input
              type="text"
              placeholder="e.g., What are the subjects in BCA?"
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && !isLoading && handleAskQuestion()}
              disabled={isLoading}
              className="flex-grow"
            />
            <Button onClick={handleAskQuestion} disabled={isLoading || !question.trim()} className="bg-accent hover:bg-accent/90 text-accent-foreground">
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <SendHorizonal className="h-4 w-4" />
              )}
              <span className="sr-only">Send</span>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
