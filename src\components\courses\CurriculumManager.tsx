
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import type { Course, Semester, Subject } from '@/types';
import { Button } from '@/components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
} from "@/components/ui/accordion"
import * as AccordionPrimitive from "@radix-ui/react-accordion";
import { PlusCircle, Edit, Trash2, Loader2, Info, Save, ChevronDown } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getCourseById, updateCourse } from '@/actions/courseActions';
import { SubjectDialog } from './SubjectDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';

interface CurriculumManagerProps {
  courseId: string;
}

export function CurriculumManager({ courseId }: CurriculumManagerProps) {
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  // State for modals/dialogs
  const [isSubjectDialogOpen, setIsSubjectDialogOpen] = useState(false);
  const [editingSubject, setEditingSubject] = useState<{ subject: Subject | null, semesterId: string } | null>(null);
  const [deletingSemester, setDeletingSemester] = useState<Semester | null>(null);
  const [deletingSubject, setDeletingSubject] = useState<{ subject: Subject, semesterId: string } | null>(null);


  const fetchCourseData = useCallback(async () => {
    setIsLoading(true);
    try {
      const fetchedCourse = await getCourseById(courseId);
      if (fetchedCourse) {
        // Ensure semesters are sorted
        fetchedCourse.semesters.sort((a, b) => a.semesterNumber - b.semesterNumber);
      }
      setCourse(fetchedCourse);
    } catch (error) {
      console.error("Failed to fetch course data:", error);
      toast({ title: "Error", description: "Could not fetch course curriculum.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [courseId, toast]);

  useEffect(() => {
    fetchCourseData();
  }, [fetchCourseData]);

  // --- Semester Management ---
  const handleAddSemester = () => {
    if (!course) return;
    const nextSemesterNumber = course.semesters.length > 0
      ? Math.max(...course.semesters.map(s => s.semesterNumber)) + 1
      : 1;
    
    const newSemester: Semester = {
      id: `sem-${Date.now()}`,
      semesterNumber: nextSemesterNumber,
      subjects: [],
    };

    setCourse(prev => prev ? { ...prev, semesters: [...prev.semesters, newSemester].sort((a,b) => a.semesterNumber - b.semesterNumber) } : null);
    toast({ title: "Semester Added", description: `Semester ${nextSemesterNumber} added. Click 'Save Curriculum' to persist.`});
  };

  const handleDeleteSemester = () => {
    if (!course || !deletingSemester) return;
    setCourse(prev => prev ? { ...prev, semesters: prev.semesters.filter(s => s.id !== deletingSemester.id) } : null);
    setDeletingSemester(null);
    toast({ title: "Semester Removed", description: `Semester ${deletingSemester.semesterNumber} removed. Click 'Save Curriculum' to persist.`});
  };

  // --- Subject Management ---
  const handleOpenSubjectDialog = (semesterId: string, subject?: Subject) => {
    setEditingSubject({ subject: subject || null, semesterId });
    setIsSubjectDialogOpen(true);
  };

  const handleSaveSubject = (subjectData: Omit<Subject, 'id'> | Subject) => {
    if (!course || !editingSubject) return;
    
    const { semesterId } = editingSubject;

    const newSemesters = course.semesters.map(semester => {
      if (semester.id === semesterId) {
        let newSubjects: Subject[];
        if ('id' in subjectData) { // Editing
          newSubjects = semester.subjects.map(s => s.id === subjectData.id ? subjectData : s);
        } else { // Adding
          const newSubject = { ...subjectData, id: `subj-${Date.now()}` };
          newSubjects = [...semester.subjects, newSubject];
        }
        return { ...semester, subjects: newSubjects };
      }
      return semester;
    });

    setCourse({ ...course, semesters: newSemesters });
    setIsSubjectDialogOpen(false);
    setEditingSubject(null);
  };

  const handleDeleteSubject = () => {
    if (!course || !deletingSubject) return;
    const { subject, semesterId } = deletingSubject;

    const newSemesters = course.semesters.map(semester => {
      if (semester.id === semesterId) {
        return { ...semester, subjects: semester.subjects.filter(s => s.id !== subject.id) };
      }
      return semester;
    });

    setCourse({ ...course, semesters: newSemesters });
    setDeletingSubject(null);
    toast({ title: "Subject Removed", description: `"${subject.name}" removed. Click 'Save Curriculum' to persist.` });
  };


  // --- Save All Changes ---
  const handleSaveChanges = async () => {
    if (!course || !user) return;
    setIsSaving(true);
    try {
      const idToken = await auth.currentUser?.getIdToken();
      if (!idToken) throw new Error("Authentication required.");
      await updateCourse(course, idToken);
      toast({ title: "Curriculum Saved", description: "All changes have been saved successfully." });
      fetchCourseData(); // Re-fetch to confirm
    } catch (error) {
      console.error("Failed to save curriculum:", error);
      toast({ title: "Error", description: "Could not save curriculum.", variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };


  if (isLoading) {
    return <div className="flex justify-center items-center py-8"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }
  if (!course) {
    return <div className="text-center py-8 text-muted-foreground"><Info className="mx-auto h-10 w-10" /><p>Course not found.</p></div>;
  }

  return (
    <div className="space-y-6">
        <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium text-foreground">Semesters</h3>
            <Button onClick={handleAddSemester}><PlusCircle className="mr-2 h-4 w-4" /> Add Semester</Button>
        </div>

        {course.semesters.length === 0 ? (
             <div className="text-center py-6 text-muted-foreground border-2 border-dashed rounded-lg">
                <p>No semesters defined for this course.</p>
             </div>
        ) : (
            <Accordion type="single" collapsible className="w-full" defaultValue={`semester-${course.semesters[0].id}`}>
                {course.semesters.map(semester => (
                    <AccordionItem value={`semester-${semester.id}`} key={semester.id}>
                        <AccordionPrimitive.Header className="flex items-center">
                            <AccordionPrimitive.Trigger className="flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline text-lg [&[data-state=open]>svg]:rotate-180">
                                Semester {semester.semesterNumber}
                                <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200" />
                            </AccordionPrimitive.Trigger>
                            <Button
                                variant="ghost"
                                size="icon"
                                className="h-8 w-8 shrink-0 text-destructive hover:bg-destructive/10 hover:text-destructive ml-2"
                                onClick={() => setDeletingSemester(semester)}
                            >
                                <Trash2 className="h-4 w-4"/>
                                <span className="sr-only">Delete Semester {semester.semesterNumber}</span>
                            </Button>
                        </AccordionPrimitive.Header>
                        <AccordionContent>
                           <div className="pl-4 space-y-4">
                                <div className="flex justify-end">
                                    <Button size="sm" onClick={() => handleOpenSubjectDialog(semester.id)}>
                                        <PlusCircle className="mr-2 h-4 w-4" /> Add Subject
                                    </Button>
                                </div>
                               {semester.subjects.length === 0 ? (
                                   <p className="text-sm text-muted-foreground text-center py-4">No subjects in this semester.</p>
                               ) : (
                                    <Table>
                                        <TableHeader>
                                            <TableRow>
                                                <TableHead>Subject Name</TableHead>
                                                <TableHead>Credits</TableHead>
                                                <TableHead className="text-right">Actions</TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                        {semester.subjects.map(subject => (
                                            <TableRow key={subject.id}>
                                                <TableCell>{subject.name}</TableCell>
                                                <TableCell>{subject.credits}</TableCell>
                                                <TableCell className="text-right">
                                                    <Button variant="ghost" size="icon" className="h-8 w-8" onClick={() => handleOpenSubjectDialog(semester.id, subject)}>
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="ghost" size="icon" className="h-8 w-8 text-destructive hover:text-destructive" onClick={() => setDeletingSubject({subject, semesterId: semester.id})}>
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                        </TableBody>
                                    </Table>
                               )}
                           </div>
                        </AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
        )}

      <div className="mt-8 flex justify-end">
        <Button onClick={handleSaveChanges} disabled={isSaving || isLoading} className="min-w-[150px] bg-accent hover:bg-accent/90">
            {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Save className="mr-2 h-4 w-4" />}
            Save Curriculum
        </Button>
      </div>

      <SubjectDialog 
        open={isSubjectDialogOpen}
        onOpenChange={setIsSubjectDialogOpen}
        subject={editingSubject?.subject || null}
        onSave={handleSaveSubject}
      />

      <AlertDialog open={!!deletingSemester} onOpenChange={() => setDeletingSemester(null)}>
        <AlertDialogContent>
          <AlertDialogHeader><AlertDialogTitle>Delete Semester?</AlertDialogTitle><AlertDialogDescription>Are you sure you want to delete Semester {deletingSemester?.semesterNumber} and all its subjects? This cannot be undone until saved.</AlertDialogDescription></AlertDialogHeader>
          <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={handleDeleteSemester} className="bg-destructive hover:bg-destructive/90">Delete</AlertDialogAction></AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      <AlertDialog open={!!deletingSubject} onOpenChange={() => setDeletingSubject(null)}>
        <AlertDialogContent>
          <AlertDialogHeader><AlertDialogTitle>Delete Subject?</AlertDialogTitle><AlertDialogDescription>Are you sure you want to delete the subject "{deletingSubject?.subject.name}"? This cannot be undone until saved.</AlertDialogDescription></AlertDialogHeader>
          <AlertDialogFooter><AlertDialogCancel>Cancel</AlertDialogCancel><AlertDialogAction onClick={handleDeleteSubject} className="bg-destructive hover:bg-destructive/90">Delete</AlertDialogAction></AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
}
