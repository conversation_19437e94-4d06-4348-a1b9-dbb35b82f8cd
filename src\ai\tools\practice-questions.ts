
'use server';
/**
 * @fileOverview Genkit tool to generate practice exam questions for a course subject.
 */

import { ai } from '@/ai/genkit';
import { getCourses } from '@/actions/courseActions';
import { z } from 'zod';
import type { Unit } from '@/types';

// Schema for the output of the question generation prompt
const GeneratedQuestionsSchema = z.object({
    questions: z.array(z.object({
        type: z.string().describe("The type of question (e.g., 'Multiple Choice', 'Short Answer', 'Essay')."),
        question: z.string().describe("The question text."),
        options: z.array(z.string()).optional().describe("A list of options for multiple choice questions."),
        answer: z.string().describe("The correct answer or a model answer.")
    }))
});

// The prompt that does the actual question generation
const questionGeneratorPrompt = ai.definePrompt({
    name: 'questionGeneratorPrompt',
    input: { schema: z.object({ subjectName: z.string(), curriculum: z.any() }) },
    output: { schema: GeneratedQuestionsSchema },
    prompt: `You are an expert educator and exam creator for a university. Your task is to generate a set of probable exam questions based on the provided curriculum for the subject "{{subjectName}}".

The curriculum is provided as a JSON object containing units and study materials. Use this information to create relevant, high-quality questions that test a student's understanding of the key concepts.

Create a mix of question types, including Multiple Choice, Short Answer, and Essay questions. For each question, provide a correct answer.

Curriculum for {{subjectName}}:
{{{json curriculum}}}
`,
    // Lower safety settings for this specific generative task to avoid false positives on academic content
    config: {
        safetySettings: [
            {
                category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
                threshold: 'BLOCK_NONE',
            },
        ],
    },
});


// The tool that will be exposed to the main FAQ agent
export const generatePracticeQuestionsTool = ai.defineTool(
  {
    name: 'generatePracticeQuestions',
    description: 'Generates a list of probable exam questions for a specific subject within a course. Use this when a user asks for practice questions, sample questions, or help studying for an exam for a particular subject.',
    inputSchema: z.object({
      courseIdentifier: z.string().describe('The name or code of the course, e.g., "Bachelor of Computer Applications" or "BCA".'),
      subjectName: z.string().describe('The name of the subject for which to generate questions, e.g., "Data Structures".')
    }),
    outputSchema: z.union([
      GeneratedQuestionsSchema,
      z.object({ error: z.string() })
    ]),
  },
  async ({ courseIdentifier, subjectName }) => {
    try {
        const allCourses = await getCourses();
        const normalizedCourseIdentifier = courseIdentifier.toLowerCase().trim();
        const normalizedSubjectName = subjectName.toLowerCase().trim();
        
        const course = allCourses.find(c => 
            c.name.toLowerCase().includes(normalizedCourseIdentifier) || 
            c.code.toLowerCase() === normalizedCourseIdentifier
        );

        if (!course) {
          return { error: `Course "${courseIdentifier}" not found.` };
        }

        let targetSubject: { name: string, units: Unit[] | undefined } | null = null;

        for (const semester of course.semesters || []) {
            const foundSubject = semester.subjects.find(s => s.name.toLowerCase().trim() === normalizedSubjectName);
            if (foundSubject) {
                targetSubject = { name: foundSubject.name, units: foundSubject.units };
                break;
            }
        }
        
        if (!targetSubject) {
            return { error: `Subject "${subjectName}" not found in the course "${course.name}".` };
        }

        if (!targetSubject.units || targetSubject.units.length === 0) {
            return { error: `The subject "${targetSubject.name}" does not have any curriculum (units or materials) defined, so I cannot generate questions.` };
        }

        // Call the specialized prompt to generate the questions
        const { output } = await questionGeneratorPrompt({
            subjectName: targetSubject.name,
            curriculum: targetSubject.units
        });

        if (!output) {
            return { error: `I was unable to generate questions for "${targetSubject.name}". Please try again.` };
        }

        return output;

    } catch (e: any) {
        console.error("Error in generatePracticeQuestionsTool:", e);
        return { error: `An internal error occurred while generating practice questions.` };
    }
  }
);
