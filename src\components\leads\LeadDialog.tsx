
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import type { Lead, Course } from '@/types';
import { useToast } from '@/hooks/use-toast';
import React, { useMemo } from 'react';

const leadStatusEnum = z.enum(['New', 'Contacted', 'Qualified', 'Lost']);

const leadFormSchema = z.object({
  name: z.string().min(2, { message: 'Name must be at least 2 characters.' }),
  email: z.string().email({ message: 'Please enter a valid email address.' }),
  phone: z.string().regex(/^[6-9]\d{9}$/, { message: 'Please enter a valid 10-digit Indian mobile number.' }),
  source: z.string().min(2, { message: 'Source must be at least 2 characters.' }),
  status: leadStatusEnum,
  desiredCourse: z.string().optional(),
  notes: z.string().optional(),
});

type LeadFormValues = z.infer<typeof leadFormSchema>;

interface LeadDialogProps {
  lead?: Lead | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (leadData: Lead | Omit<Lead, 'id' | 'inquiryDate' | 'status'>) => Promise<void>;
  formType: 'create' | 'edit';
  courses: Course[];
}

export function LeadDialog({ lead, open, onOpenChange, onSave, formType, courses }: LeadDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  const form = useForm<LeadFormValues>({
    resolver: zodResolver(leadFormSchema),
    defaultValues: formType === 'edit' && lead
      ? { ...lead, phone: lead.phone || '', notes: lead.notes || '', desiredCourse: lead.desiredCourse || '' }
      : {
          name: '',
          email: '',
          phone: '',
          source: '',
          status: 'New',
          desiredCourse: '',
          notes: '',
        },
  });

  const groupedCourses = useMemo(() => {
    return courses.reduce<Record<string, Course[]>>((acc, course) => {
      const groupName = `${course.name} (${course.code})`;
      if (!acc[groupName]) {
        acc[groupName] = [];
      }
      acc[groupName].push(course);
      return acc;
    }, {});
  }, [courses]);

  React.useEffect(() => {
    if (open) {
      if (formType === 'edit' && lead) {
        form.reset({ ...lead, phone: lead.phone || '', notes: lead.notes || '', desiredCourse: lead.desiredCourse || '' });
      } else {
        form.reset({
          name: '',
          email: '',
          phone: '',
          source: '',
          status: 'New',
          desiredCourse: '',
          notes: '',
        });
      }
    }
  }, [lead, form, open, formType]);

  async function onSubmit(data: LeadFormValues) {
    setIsSubmitting(true);
    try {
      const dataToSave = { ...data, desiredCourse: data.desiredCourse || undefined };
      if (formType === 'edit' && lead) {
        await onSave({ ...lead, ...dataToSave }); // Pass full lead object with id
      } else {
        await onSave(dataToSave); // Pass Omit<...> for creation
      }
      toast({
        title: formType === 'edit' ? 'Lead Updated!' : 'Lead Created!',
        description: `The lead "${data.name}" has been successfully ${formType === 'edit' ? 'updated' : 'created'}.`,
      });
      onOpenChange(false); // Close dialog on success
    } catch (error) {
      toast({
        title: 'Error',
        description: `Could not ${formType === 'edit' ? 'update' : 'create'} lead. Please try again.`,
        variant: 'destructive',
      });
      console.error("Error saving lead:", error);
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle className="font-headline text-2xl">{formType === 'edit' ? 'Edit Lead' : 'Add New Lead'}</DialogTitle>
          <DialogDescription>
            {formType === 'edit' ? 'Update the details for this lead.' : 'Fill in the details for the new lead.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., John Doe" {...field} disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="e.g., <EMAIL>" {...field} disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Mobile Number</FormLabel>
                    <FormControl>
                      <Input type="tel" placeholder="e.g., 9876543210" {...field} disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
             <FormField
                control={form.control}
                name="desiredCourse"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Desired Course (Optional)</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(value === '_none_' ? '' : value)}
                      value={field.value || '_none_'}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a course if applicable" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="_none_">
                          <span className="text-muted-foreground">-- Not Specified --</span>
                        </SelectItem>
                        {Object.entries(groupedCourses).map(([groupName, coursesInGroup]) => (
                            <SelectGroup key={groupName}>
                                <SelectLabel>{groupName}</SelectLabel>
                                {coursesInGroup.map(course => {
                                if (course.specializations && course.specializations.length > 0) {
                                    return course.specializations.map(spec => (
                                    <SelectItem key={`${course.id}-${spec}`} value={course.id}>
                                        {spec}
                                    </SelectItem>
                                    ));
                                }
                                // Fallback for courses with no specializations
                                return (
                                    <SelectItem key={course.id} value={course.id}>
                                    General
                                    </SelectItem>
                                );
                                })}
                            </SelectGroup>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="source"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Source</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Website, Referral" {...field} disabled={isSubmitting} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value} disabled={isSubmitting}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {leadStatusEnum.options.map(statusValue => (
                          <SelectItem key={statusValue} value={statusValue}>{statusValue}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Any relevant notes about this lead..." {...field} rows={3} disabled={isSubmitting} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline" disabled={isSubmitting}>Cancel</Button>
              </DialogClose>
              <Button type="submit" className="bg-primary hover:bg-primary/90 text-primary-foreground" disabled={isSubmitting}>
                {isSubmitting ? 'Saving...' : (formType === 'edit' ? 'Save Changes' : 'Create Lead')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
