"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/actions/feeActions.ts":
/*!***********************************!*\
  !*** ./src/actions/feeActions.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStudentFee: () => (/* binding */ createStudentFee),\n/* harmony export */   deleteStudentFee: () => (/* binding */ deleteStudentFee),\n/* harmony export */   generateFeeForEnrolledStudents: () => (/* binding */ generateFeeForEnrolledStudents),\n/* harmony export */   getAllPaymentTransactions: () => (/* binding */ getAllPaymentTransactions),\n/* harmony export */   getCourseFeeStructureByCourseId: () => (/* binding */ getCourseFeeStructureByCourseId),\n/* harmony export */   getFinancialReportData: () => (/* binding */ getFinancialReportData),\n/* harmony export */   getPaymentTransactionsByFeeId: () => (/* binding */ getPaymentTransactionsByFeeId),\n/* harmony export */   getStudentFeeById: () => (/* binding */ getStudentFeeById),\n/* harmony export */   getStudentFees: () => (/* binding */ getStudentFees),\n/* harmony export */   getStudentFeesByStudentId: () => (/* binding */ getStudentFeesByStudentId),\n/* harmony export */   recordManualPayment: () => (/* binding */ recordManualPayment),\n/* harmony export */   saveCourseFeeStructure: () => (/* binding */ saveCourseFeeStructure),\n/* harmony export */   sendOverdueFeeReminders: () => (/* binding */ sendOverdueFeeReminders),\n/* harmony export */   updateFeeStatus: () => (/* binding */ updateFeeStatus)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"405cf8f7c4e72f6c7f444d4121d009189229704272\":\"getAllPaymentTransactions\",\"4084747f2b3d3e6f8a34b2b1e66fc6881813f91e1e\":\"getStudentFees\",\"4088d3fa787ce1ff911d20ed3a5eb5378830c34413\":\"sendOverdueFeeReminders\",\"60393f4bad5ac5ecf434901a5741906ea5e704d1a6\":\"getStudentFeeById\",\"603f0bbb3f7a11423b9f6897bd79a1b845241eea5c\":\"getFinancialReportData\",\"6054b892a1b827022f2d39d3450589bba818c5008f\":\"deleteStudentFee\",\"605e5d0b3411c92d9ec955de6538418f0b8f954a3a\":\"recordManualPayment\",\"6088ea1393fc123e6c96beb7d0c7724a5df7bb77f4\":\"getStudentFeesByStudentId\",\"60bd254a66a3acb53316ed35e3e29079322e426fa6\":\"getPaymentTransactionsByFeeId\",\"60d3bcb85b778293f22509672180b38c14fd866766\":\"getCourseFeeStructureByCourseId\",\"60e8493f4a51ac63b9b1122b1605d98455f0109264\":\"createStudentFee\",\"7045a5a974d6e47d04d18c460bb4f9b0dcfa45b1d7\":\"saveCourseFeeStructure\",\"70956bf70df1d1287604bac1d71b60c3ca82567e14\":\"generateFeeForEnrolledStudents\",\"70acfcc4abd0806ef05136d24879ff898816331d31\":\"updateFeeStatus\"} */ \nvar getStudentFees = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4084747f2b3d3e6f8a34b2b1e66fc6881813f91e1e\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFees\");\nvar getStudentFeeById = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60393f4bad5ac5ecf434901a5741906ea5e704d1a6\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFeeById\");\nvar getStudentFeesByStudentId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"6088ea1393fc123e6c96beb7d0c7724a5df7bb77f4\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFeesByStudentId\");\nvar createStudentFee = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60e8493f4a51ac63b9b1122b1605d98455f0109264\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createStudentFee\");\nvar recordManualPayment = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"605e5d0b3411c92d9ec955de6538418f0b8f954a3a\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"recordManualPayment\");\nvar updateFeeStatus = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"70acfcc4abd0806ef05136d24879ff898816331d31\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"updateFeeStatus\");\nvar deleteStudentFee = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"6054b892a1b827022f2d39d3450589bba818c5008f\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteStudentFee\");\nvar sendOverdueFeeReminders = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4088d3fa787ce1ff911d20ed3a5eb5378830c34413\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"sendOverdueFeeReminders\");\nvar generateFeeForEnrolledStudents = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"70956bf70df1d1287604bac1d71b60c3ca82567e14\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"generateFeeForEnrolledStudents\");\nvar getFinancialReportData = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"603f0bbb3f7a11423b9f6897bd79a1b845241eea5c\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getFinancialReportData\");\nvar getPaymentTransactionsByFeeId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60bd254a66a3acb53316ed35e3e29079322e426fa6\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getPaymentTransactionsByFeeId\");\nvar getAllPaymentTransactions = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"405cf8f7c4e72f6c7f444d4121d009189229704272\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAllPaymentTransactions\");\nvar getCourseFeeStructureByCourseId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60d3bcb85b778293f22509672180b38c14fd866766\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getCourseFeeStructureByCourseId\");\nvar saveCourseFeeStructure = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7045a5a974d6e47d04d18c460bb4f9b0dcfa45b1d7\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"saveCourseFeeStructure\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/actions/feeActions.ts\n"));

/***/ })

});