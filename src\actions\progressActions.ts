
'use server';

import { db } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';
import type { StudentCourseProgress } from '@/types';
import { revalidatePath } from 'next/cache';

const PROGRESS_COLLECTION = 'studentCourseProgress';

/**
 * Fetches the progress for a specific student in a specific course.
 * @param studentId The UID of the student.
 * @param courseId The ID of the course.
 * @param idToken The Firebase ID token of the calling user.
 * @returns The student's progress object or null if not found.
 */
export async function getStudentProgressForCourse(studentId: string, courseId: string, idToken: string): Promise<StudentCourseProgress | null> {
    const { uid, token } = await requireAuth(idToken, ['student', 'admin']);

    // A student can only view their own progress. Admins can view any.
    if (token.role === 'student' && uid !== studentId) {
        throw new Error("Forbidden: You can only view your own progress.");
    }

    const progressQuery = db.collection(PROGRESS_COLLECTION)
                            .where('studentId', '==', studentId)
                            .where('courseId', '==', courseId)
                            .limit(1);
    
    const snapshot = await progressQuery.get();

    if (snapshot.empty) {
        return null;
    }

    const doc = snapshot.docs[0];
    const data = doc.data();

    return JSON.parse(JSON.stringify({
        id: doc.id,
        ...data
    } as StudentCourseProgress));
}

/**
 * Updates or creates a student's progress for a course.
 * @param studentId The UID of the student.
 * @param courseId The ID of the course.
 * @param completedUnits An array of completed unit IDs.
 * @param idToken The Firebase ID token of the calling user.
 * @returns The updated progress object.
 */
export async function updateStudentProgress(studentId: string, courseId: string, completedUnits: string[], idToken: string): Promise<StudentCourseProgress> {
    const { uid, token } = await requireAuth(idToken, ['student']);

    // A student can only update their own progress.
    if (uid !== studentId) {
        throw new Error("Forbidden: You can only update your own progress.");
    }

    const progressQuery = db.collection(PROGRESS_COLLECTION)
                            .where('studentId', '==', studentId)
                            .where('courseId', '==', courseId)
                            .limit(1);

    const snapshot = await progressQuery.get();

    const progressData: Omit<StudentCourseProgress, 'id'> = {
        studentId,
        courseId,
        completedUnits,
    };

    if (snapshot.empty) {
        // Create new progress document
        const docRef = await db.collection(PROGRESS_COLLECTION).add(progressData);
        revalidatePath(`/my-courses/${courseId}`);
        return JSON.parse(JSON.stringify({ id: docRef.id, ...progressData }));
    } else {
        // Update existing progress document
        const docRef = snapshot.docs[0].ref;
        await docRef.update({ completedUnits });
        revalidatePath(`/my-courses/${courseId}`);
        return JSON.parse(JSON.stringify({ id: docRef.id, ...progressData }));
    }
}

/**
 * Fetches all progress records for a given student across all their courses.
 * @param studentId The UID of the student.
 * @param idToken The Firebase ID token of the calling user.
 * @returns An array of student progress objects.
 */
export async function getStudentProgressForAllCourses(studentId: string, idToken: string): Promise<StudentCourseProgress[]> {
    const { uid, token } = await requireAuth(idToken, ['student', 'admin']);

    if (token.role === 'student' && uid !== studentId) {
        throw new Error("Forbidden: You can only view your own progress.");
    }

    const progressQuery = db.collection(PROGRESS_COLLECTION)
                            .where('studentId', '==', studentId);
    
    const snapshot = await progressQuery.get();

    if (snapshot.empty) {
        return [];
    }

    const progressList: StudentCourseProgress[] = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
    } as StudentCourseProgress));

    return JSON.parse(JSON.stringify(progressList));
}
