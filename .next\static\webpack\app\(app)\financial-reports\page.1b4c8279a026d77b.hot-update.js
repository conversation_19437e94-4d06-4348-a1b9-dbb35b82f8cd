"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/actions/feeActions.ts":
/*!***********************************!*\
  !*** ./src/actions/feeActions.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStudentFee: () => (/* binding */ createStudentFee),\n/* harmony export */   deleteStudentFee: () => (/* binding */ deleteStudentFee),\n/* harmony export */   generateFeeForEnrolledStudents: () => (/* binding */ generateFeeForEnrolledStudents),\n/* harmony export */   getAllPaymentTransactions: () => (/* binding */ getAllPaymentTransactions),\n/* harmony export */   getCourseFeeStructureByCourseId: () => (/* binding */ getCourseFeeStructureByCourseId),\n/* harmony export */   getFinancialReportData: () => (/* binding */ getFinancialReportData),\n/* harmony export */   getPaymentTransactionsByFeeId: () => (/* binding */ getPaymentTransactionsByFeeId),\n/* harmony export */   getStudentFeeById: () => (/* binding */ getStudentFeeById),\n/* harmony export */   getStudentFees: () => (/* binding */ getStudentFees),\n/* harmony export */   getStudentFeesByStudentId: () => (/* binding */ getStudentFeesByStudentId),\n/* harmony export */   recordManualPayment: () => (/* binding */ recordManualPayment),\n/* harmony export */   saveCourseFeeStructure: () => (/* binding */ saveCourseFeeStructure),\n/* harmony export */   sendOverdueFeeReminders: () => (/* binding */ sendOverdueFeeReminders),\n/* harmony export */   updateFeeStatus: () => (/* binding */ updateFeeStatus)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"405cf8f7c4e72f6c7f444d4121d009189229704272\":\"getAllPaymentTransactions\",\"4084747f2b3d3e6f8a34b2b1e66fc6881813f91e1e\":\"getStudentFees\",\"4088d3fa787ce1ff911d20ed3a5eb5378830c34413\":\"sendOverdueFeeReminders\",\"60393f4bad5ac5ecf434901a5741906ea5e704d1a6\":\"getStudentFeeById\",\"603f0bbb3f7a11423b9f6897bd79a1b845241eea5c\":\"getFinancialReportData\",\"6054b892a1b827022f2d39d3450589bba818c5008f\":\"deleteStudentFee\",\"605e5d0b3411c92d9ec955de6538418f0b8f954a3a\":\"recordManualPayment\",\"6088ea1393fc123e6c96beb7d0c7724a5df7bb77f4\":\"getStudentFeesByStudentId\",\"60bd254a66a3acb53316ed35e3e29079322e426fa6\":\"getPaymentTransactionsByFeeId\",\"60d3bcb85b778293f22509672180b38c14fd866766\":\"getCourseFeeStructureByCourseId\",\"60e8493f4a51ac63b9b1122b1605d98455f0109264\":\"createStudentFee\",\"7045a5a974d6e47d04d18c460bb4f9b0dcfa45b1d7\":\"saveCourseFeeStructure\",\"70956bf70df1d1287604bac1d71b60c3ca82567e14\":\"generateFeeForEnrolledStudents\",\"70acfcc4abd0806ef05136d24879ff898816331d31\":\"updateFeeStatus\"} */ \nvar getStudentFees = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4084747f2b3d3e6f8a34b2b1e66fc6881813f91e1e\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFees\");\nvar getStudentFeeById = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60393f4bad5ac5ecf434901a5741906ea5e704d1a6\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFeeById\");\nvar getStudentFeesByStudentId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"6088ea1393fc123e6c96beb7d0c7724a5df7bb77f4\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFeesByStudentId\");\nvar createStudentFee = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60e8493f4a51ac63b9b1122b1605d98455f0109264\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createStudentFee\");\nvar recordManualPayment = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"605e5d0b3411c92d9ec955de6538418f0b8f954a3a\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"recordManualPayment\");\nvar updateFeeStatus = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"70acfcc4abd0806ef05136d24879ff898816331d31\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"updateFeeStatus\");\nvar deleteStudentFee = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"6054b892a1b827022f2d39d3450589bba818c5008f\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteStudentFee\");\nvar sendOverdueFeeReminders = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4088d3fa787ce1ff911d20ed3a5eb5378830c34413\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"sendOverdueFeeReminders\");\nvar generateFeeForEnrolledStudents = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"70956bf70df1d1287604bac1d71b60c3ca82567e14\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"generateFeeForEnrolledStudents\");\nvar getFinancialReportData = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"603f0bbb3f7a11423b9f6897bd79a1b845241eea5c\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getFinancialReportData\");\nvar getPaymentTransactionsByFeeId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60bd254a66a3acb53316ed35e3e29079322e426fa6\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getPaymentTransactionsByFeeId\");\nvar getAllPaymentTransactions = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"405cf8f7c4e72f6c7f444d4121d009189229704272\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getAllPaymentTransactions\");\nvar getCourseFeeStructureByCourseId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60d3bcb85b778293f22509672180b38c14fd866766\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getCourseFeeStructureByCourseId\");\nvar saveCourseFeeStructure = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7045a5a974d6e47d04d18c460bb4f9b0dcfa45b1d7\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"saveCourseFeeStructure\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/actions/feeActions.ts\n"));

/***/ })

});