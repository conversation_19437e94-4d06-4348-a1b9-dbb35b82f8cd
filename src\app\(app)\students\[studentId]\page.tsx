
'use client';

import { useState, useEffect, use<PERSON><PERSON>back, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { getStudentApplicationDetails, updateStudentDetails, type UpdateStudentDetailsData } from '@/actions/studentActions';
import { getStudentFeesByStudentId } from '@/actions/feeActions';
import { getCourses } from '@/actions/courseActions';
import { getStudentProgressForAllCourses } from '@/actions/progressActions';
import type { StudentApplication, Course, StudentFee, FeeStatus, StudentCourseProgress } from '@/types';
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON><PERSON>, User, <PERSON><PERSON><PERSON><PERSON>, DollarSign, <PERSON><PERSON>hart2, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Book<PERSON><PERSON>, Edit } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { ApplicationDetailsDialog } from '@/components/applications/ApplicationDetailsDialog';
import { StudentEditDialog } from '@/components/students/StudentEditDialog';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Progress } from '@/components/ui/progress';

const statusVariantMap: Record<FeeStatus, 'default' | 'secondary' | 'outline' | 'destructive'> = {
  Pending: 'default',
  'Partially Paid': 'secondary',
  Paid: 'outline',
  Overdue: 'destructive',
};


export default function StudentDetailPage() {
  const params = useParams();
  const studentId = params.studentId as string;
  const router = useRouter();

  const [application, setApplication] = useState<StudentApplication | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [fees, setFees] = useState<StudentFee[]>([]);
  const [progress, setProgress] = useState<StudentCourseProgress[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const { toast } = useToast();
  const { user } = useAuth();

  const fetchStudentData = useCallback(async () => {
    if (!studentId) return;
    setIsLoading(true);
    setError(null);
    try {
      if (!auth.currentUser) throw new Error("Authentication required.");
      const idToken = await auth.currentUser.getIdToken();

      const [appData, coursesData, feesData, progressData] = await Promise.all([
        getStudentApplicationDetails(studentId, idToken),
        getCourses(),
        getStudentFeesByStudentId(studentId, idToken),
        getStudentProgressForAllCourses(studentId, idToken),
      ]);

      if (!appData) {
        throw new Error("Student not found or application data is missing.");
      }
      
      setApplication(appData);
      setCourses(coursesData);
      setFees(feesData);
      setProgress(progressData);

    } catch (e: any) {
      console.error("Failed to fetch student details:", e);
      setError(e.message || "An unexpected error occurred.");
      toast({ title: "Error", description: e.message || "Could not load student data.", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [studentId, toast]);

  useEffect(() => {
    fetchStudentData();
  }, [fetchStudentData]);
  
  const course = useMemo(() => {
    if (!application || !courses) return null;
    return courses.find(c => c.id === application.desiredCourse);
  }, [application, courses]);

  const courseProgress = useMemo(() => {
    if (!course || !progress.length) return null;
    return progress.find(p => p.courseId === course.id);
  }, [course, progress]);

  const progressPercentage = useMemo(() => {
    if (!course || !courseProgress) return 0;
    const totalUnits = course.semesters.reduce((semTotal, sem) => 
        semTotal + sem.subjects.reduce((subTotal, sub) => 
            subTotal + (sub.units?.length || 0), 0), 0);
    const completedUnitsCount = courseProgress.completedUnits.length;
    return totalUnits > 0 ? Math.round((completedUnitsCount / totalUnits) * 100) : 0;
  }, [course, courseProgress]);
  
  const handleUpdateStudent = async (data: UpdateStudentDetailsData) => {
    if (!auth.currentUser) {
        toast({ title: 'Error', description: 'You must be logged in.', variant: 'destructive' });
        return;
    }
    const idToken = await auth.currentUser.getIdToken();
    try {
        const updatedApplication = await updateStudentDetails(data, idToken);
        setApplication(updatedApplication);
        toast({ title: 'Success', description: 'Student details updated successfully.' });
        setIsEditDialogOpen(false);
    } catch (err: any) {
        toast({ title: 'Update Failed', description: err.message || 'Could not update student details.', variant: 'destructive' });
    }
  };


  if (isLoading) {
    return (
      <div className="space-y-6">
        <Skeleton className="h-9 w-40" />
        <Card>
            <CardHeader><Skeleton className="h-8 w-64" /></CardHeader>
            <CardContent><Skeleton className="h-4 w-full" /><Skeleton className="h-4 w-3/4" /></CardContent>
        </Card>
        <div className="grid md:grid-cols-2 gap-6">
            <Card><CardHeader><Skeleton className="h-6 w-32"/></CardHeader><CardContent><Skeleton className="h-20 w-full" /></CardContent></Card>
            <Card><CardHeader><Skeleton className="h-6 w-32"/></CardHeader><CardContent><Skeleton className="h-20 w-full" /></CardContent></Card>
        </div>
      </div>
    );
  }

  if (error) {
     return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center p-4">
        <AlertTriangle className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive mb-2">Failed to Load Student</h1>
        <p className="text-muted-foreground mb-6">{error}</p>
        <Button asChild variant="outline">
          <Link href="/students"><ArrowLeft className="mr-2 h-4 w-4" /> Back to Students List</Link>
        </Button>
      </div>
    );
  }

  if (!application) return null;

  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center gap-4">
                <Button asChild variant="outline" size="icon" className="h-9 w-9">
                    <Link href="/students"><ArrowLeft className="h-5 w-5" /></Link>
                </Button>
                <div>
                    <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">{application.fullName}</h1>
                    <p className="text-muted-foreground">{application.email} - Student ID: {application.userId}</p>
                </div>
            </div>
        </div>
        
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="application">Application</TabsTrigger>
            <TabsTrigger value="fees">Fee Records</TabsTrigger>
            <TabsTrigger value="progress">Academic Progress</TabsTrigger>
          </TabsList>
          <TabsContent value="overview" className="mt-4">
              <Card>
                  <CardHeader>
                      <CardTitle className="font-headline flex items-center gap-2"><User className="h-6 w-6 text-primary"/> Student Overview</CardTitle>
                  </CardHeader>
                  <CardContent className="grid md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                          <h3 className="font-semibold">Personal Information</h3>
                          <p className="text-sm"><span className="text-muted-foreground">Father:</span> {application.fatherName}</p>
                          <p className="text-sm"><span className="text-muted-foreground">Mother:</span> {application.motherName}</p>
                          <p className="text-sm"><span className="text-muted-foreground">DOB:</span> {format(new Date(application.dateOfBirth.replace(/-/g, '/')), 'PPP')}</p>
                          <p className="text-sm"><span className="text-muted-foreground">Mobile:</span> {application.mobileNumber}</p>
                      </div>
                      <div className="space-y-4">
                          <h3 className="font-semibold">Enrollment Details</h3>
                          <p className="text-sm"><span className="text-muted-foreground">Course:</span> {course?.name || 'N/A'}</p>
                          <p className="text-sm"><span className="text-muted-foreground">Course Code:</span> {course?.code || 'N/A'}</p>
                          <p className="text-sm"><span className="text-muted-foreground">Admission Date:</span> {format(new Date(application.applicationDate.replace(/-/g, '/')), 'PPP')}</p>
                          {courseProgress && (
                              <div>
                                  <p className="text-sm text-muted-foreground mb-1">Course Progress: {progressPercentage}%</p>
                                  <Progress value={progressPercentage} className="h-2" />
                              </div>
                          )}
                      </div>
                  </CardContent>
              </Card>
          </TabsContent>
          <TabsContent value="application" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle className="font-headline flex items-center gap-2"><FileText className="h-6 w-6 text-primary" /> Application Management</CardTitle>
                <CardDescription>View the original submitted application or edit the student's personal details.</CardDescription>
              </CardHeader>
              <CardContent className="flex flex-wrap gap-4">
                <Button onClick={() => setIsDetailsDialogOpen(true)}>
                  View Full Original Application
                </Button>
                {user?.role === 'admin' && (
                    <Button onClick={() => setIsEditDialogOpen(true)} variant="secondary">
                        <Edit className="mr-2 h-4 w-4"/>
                        Edit Student Details
                    </Button>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="fees" className="mt-4">
              <Card>
                  <CardHeader>
                      <CardTitle className="font-headline flex items-center gap-2"><DollarSign className="h-6 w-6 text-primary" /> Fee Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                      {fees.length > 0 ? (
                        <div className="rounded-lg border overflow-hidden">
                          <table className="w-full text-sm">
                              <thead><tr className="border-b"><th className="p-3 text-left">Description</th><th className="p-3 text-left">Due Date</th><th className="p-3 text-left">Status</th><th className="p-3 text-right">Amount Due</th><th className="p-3 text-right">Amount Paid</th></tr></thead>
                              <tbody>
                                  {fees.map(fee => (
                                      <tr key={fee.id} className="border-b">
                                          <td className="p-3">{fee.feeDescription}</td>
                                          <td className="p-3">{format(new Date(fee.dueDate), 'PP')}</td>
                                          <td className="p-3"><Badge variant={statusVariantMap[fee.status]}>{fee.status}</Badge></td>
                                          <td className="p-3 text-right">₹{fee.amountDue.toFixed(2)}</td>
                                          <td className="p-3 text-right text-green-600">₹{fee.amountPaid.toFixed(2)}</td>
                                      </tr>
                                  ))}
                              </tbody>
                          </table>
                        </div>
                      ): <p className="text-muted-foreground">No fee records found for this student.</p>}
                  </CardContent>
              </Card>
          </TabsContent>
          <TabsContent value="progress" className="mt-4">
              <Card>
                  <CardHeader>
                      <CardTitle className="font-headline flex items-center gap-2"><BarChart2 className="h-6 w-6 text-primary" /> Academic Progress</CardTitle>
                  </CardHeader>
                  <CardContent>
                      {!course ? (
                          <p className="text-muted-foreground">Could not load course curriculum.</p>
                      ) : !courseProgress ? (
                          <p className="text-muted-foreground">No progress has been recorded for this student yet.</p>
                      ) : (
                          <div className="space-y-4">
                              <div className="flex items-center gap-4">
                                  {course.id && (
                                      <Link href={`/my-courses/${course.id}`} className="font-semibold text-primary hover:underline">{course.name}</Link>
                                  )}
                                  <div className="w-full">
                                      <Progress value={progressPercentage} className="h-3"/>
                                      <p className="text-xs text-muted-foreground text-right mt-1">{progressPercentage}% complete</p>
                                  </div>
                              </div>
                              <h4 className="font-semibold">Completed Units:</h4>
                              <ul className="list-disc list-inside text-sm text-muted-foreground columns-2">
                                  {course.semesters.flatMap(s => s.subjects.flatMap(sub => sub.units || [])).filter(u => courseProgress.completedUnits.includes(u.id)).map(unit => (
                                      <li key={unit.id}>{unit.name}</li>
                                  ))}
                              </ul>
                          </div>
                      )}
                  </CardContent>
              </Card>
          </TabsContent>
        </Tabs>
      </div>

      <ApplicationDetailsDialog
        application={application}
        courses={courses}
        open={isDetailsDialogOpen}
        onOpenChange={setIsDetailsDialogOpen}
      />
      
      <StudentEditDialog
        student={application}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
        onSave={handleUpdateStudent}
      />
    </>
  );
}
