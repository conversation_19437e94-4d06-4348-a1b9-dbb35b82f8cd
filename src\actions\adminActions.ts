'use server';

import { adminAuth } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';

/**
 * Revokes the refresh tokens for all users in Firebase Authentication.
 * This effectively forces all users to log in again.
 * This is a sensitive operation and should only be exposed to system administrators.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns An object with the total number of users whose sessions were revoked.
 */
export async function forceLogoutAllUsers(idToken: string): Promise<{ revokedUsersCount: number }> {
  // Ensure only an admin can perform this action
  await requireAuth(idToken, ['admin']);

  if (!adminAuth) {
    throw new Error('Admin SDK not initialized. Check server logs.');
  }

  let pageToken: string | undefined;
  let revokedUsersCount = 0;

  try {
    do {
      const listUsersResult = await adminAuth.listUsers(1000, pageToken);
      const uidsToRevoke = listUsersResult.users.map((userRecord) => userRecord.uid);
      
      if (uidsToRevoke.length > 0) {
        // Revoke tokens for the current batch of users
        const revokePromises = uidsToRevoke.map(uid => 
          adminAuth.revokeRefreshTokens(uid)
            .then(() => {
              console.log(`Successfully revoked tokens for user: ${uid}`);
              revokedUsersCount++;
            })
            .catch(error => {
              console.error(`Failed to revoke tokens for user: ${uid}`, error);
              // Continue even if one fails
            })
        );
        await Promise.all(revokePromises);
      }
      
      pageToken = listUsersResult.pageToken;
    } while (pageToken);

    console.log(`Successfully forced logout for ${revokedUsersCount} users.`);
    return { revokedUsersCount };

  } catch (error) {
    console.error('Error while forcing logout for all users:', error);
    throw new Error('An unexpected error occurred while revoking user sessions.');
  }
}
