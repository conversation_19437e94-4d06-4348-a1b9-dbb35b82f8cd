
# EduLite Setup Guide

This guide explains how to configure essential third-party services for the EduLite application, including user roles and payment processing.

## 1. Managing User Roles (Admin & Accountant)

This section explains how to create new user accounts in Firebase and assign them 'admin' or 'accountant' roles for the EduLite application. These roles are critical for controlling access to different features.

**Critical Note: Setting Custom Claims is MANDATORY**

You **cannot** set Firebase Custom User Claims directly through the Firebase Console (the web dashboard). Custom claims **must** be set programmatically using the Firebase Admin SDK or the Firebase CLI, as detailed below.

If a user does not have a valid `role` custom claim, they will default to a 'student' role, lacking access to administrative functionalities.

### Prerequisites

1.  **Firebase Project:** Your EduLite Firebase project should be set up.
2.  **Firebase User Accounts:** The users you want to assign roles to must already have user accounts in Firebase Authentication. You can create these directly in the Firebase Console (Authentication > Users > Add user).
3.  **Tooling:** You'll need the **Firebase CLI** installed and authenticated. ([Install Firebase CLI](https://firebase.google.com/docs/cli#install_the_firebase_cli))

### Using the Firebase CLI

This method is convenient for manually assigning roles.

#### Step 1: Get the User's UID

Find the Unique User ID (UID) of the Firebase user in your Firebase Console under Authentication > Users.

#### Step 2: Set Custom User Claims

Open your terminal and use the following command, replacing `<uid>` with the user's actual UID.

*   **To assign an 'admin' role:**
    ```bash
    npx firebase auth:set-custom-user-claims <uid> '{ "role": "admin" }'
    ```
*   **To assign an 'accountant' role:**
    ```bash
    npx firebase auth:set-custom-user-claims <uid> '{ "role": "accountant" }'
    ```

**Important:** The key `role` is what the EduLite application expects. Valid roles are "admin", "accountant", and "student". The user may need to sign out and sign back in for the changes to take effect.
