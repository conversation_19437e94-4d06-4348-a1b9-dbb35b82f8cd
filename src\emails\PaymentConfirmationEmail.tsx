
import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
  Section,
} from '@react-email/components';

interface PaymentConfirmationEmailProps {
  studentName: string;
  courseName: string;
  feeDescription: string;
  amountPaid: number;
  paymentId: string;
  paymentDate: string;
}

const PaymentConfirmationEmail: React.FC<PaymentConfirmationEmailProps> = ({
  studentName,
  courseName,
  feeDescription,
  amountPaid,
  paymentId,
  paymentDate,
}) => {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9002';
  
  return (
    <Html>
      <Head />
      <Preview>Payment Successful for {courseName}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={heading}>Payment Successful!</Heading>
          <Text style={paragraph}>Dear {studentName},</Text>
          <Text style={paragraph}>
            We have successfully received your payment for your enrollment in the <strong>{courseName}</strong> program. Thank you for your promptness.
          </Text>
          <Section style={detailsSection}>
            <Text style={detailText}><strong>Fee Item:</strong> {feeDescription}</Text>
            <Text style={detailText}><strong>Amount Paid:</strong> ₹{amountPaid.toFixed(2)}</Text>
            <Text style={detailText}><strong>Payment Date:</strong> {new Date(paymentDate).toLocaleDateString()}</Text>
            <Text style={detailText}><strong>Transaction ID:</strong> {paymentId}</Text>
          </Section>
          <Text style={paragraph}>
            You can view your updated fee status and receipt by logging into your student portal.
          </Text>
          <Button
            style={button}
            href={`${baseUrl}/my-fees`}
          >
            Go to My Fees
          </Button>
          <Text style={footer}>
            This is an automated notification from the EduLite system. For any queries, please contact our support team.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default PaymentConfirmationEmail;

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  border: '1px solid #f0f0f0',
  borderRadius: '4px',
};

const heading = {
  fontSize: '28px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  color: '#38a169', // Success green
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#525f7f',
  padding: '0 40px',
};

const detailsSection = {
  padding: '20px',
  margin: '20px 40px',
  backgroundColor: '#e6fffa',
  border: '1px solid #38a169',
  borderRadius: '4px',
};

const detailText = {
  fontSize: '14px',
  lineHeight: '1.5',
  color: '#2f855a',
  margin: '0 0 10px 0',
};

const button = {
  backgroundColor: '#009688',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '200px',
  padding: '12px',
  margin: '20px auto',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  textAlign: 'center' as const,
  marginTop: '20px',
  padding: '0 40px',
};
