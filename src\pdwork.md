# EduLite - Project Status & Roadmap

This document provides a high-level overview of the EduLite project's current status and outlines the next steps toward a production-ready application. After a comprehensive review, the core MVP features are now complete and stable.

## I. Critical Next Steps (MVP Blockers) - COMPLETED

The application's foundation is now strong and the initial MVP blockers have been resolved.

1.  **Transition from Mock Data to Firestore Database**: `(Completed)`
    *   **Status**: All server actions in `src/actions/*.ts` have been rewritten to perform CRUD operations against a live Firebase Firestore database. All mock data has been removed.

2.  **Secure All Server Actions**: `(Completed)`
    *   **Status**: All sensitive server actions are protected by the `requireAuth` function. The system relies exclusively on Firebase Custom Claims for role-based access control, which is the secure, production-ready approach.

---

## II. Feature Status & Enhancements

This section details the features that are now functionally complete and running on the live database.

*   **User Authentication & Roles**: `(Completed)`
    *   Handles sign-up and sign-in with email/password.
    *   Robust role management ('admin', 'student', 'accountant') using Firebase Custom Claims.
    *   Correctly redirects users based on their role and application status.

*   **Application Process (`/apply`)**: `(Completed)`
    *   New users can create an account and submit a detailed application in a single, streamlined flow.
    *   The form includes comprehensive fields and validation.
    *   Includes a dedicated success page after submission.

*   **Course Management (`/courses`)**: `(Completed)`
    *   Admins can create, update, and delete courses.
    *   Includes a **Curriculum Manager** to define a full course hierarchy: Semesters -> Subjects -> Units -> Study Materials.
    *   Includes a **Fee Structure Manager** to define all costs associated with a course.

*   **Application Management (`/applications`)**: `(Completed)`
    *   Admins and Accountants can view and filter all student applications.
    *   Admins can change an application's status and delete applications.
    *   **Automation**: Accepting an application automatically generates the correct fee records for the student based on the course's fee structure.

*   **Fee Management (`/fee-management`)**: `(Completed)`
    *   Accountants and Admins can view and manage all student fee records.
    *   Supports recording full or partial payments.
    *   Provides a link to a **printable HTML receipt** for each fee record.
    *   Includes a manually-triggered process to **send overdue fee reminders** via email.

*   **Financial Reports**: `(Completed)`
    *   A dedicated page for generating advanced, filterable financial reports (by date, course, status) is now available, including summary statistics and export options.

*   **Student Portal (`/my-courses`)**: `(Completed)`
    *   Students have a personalized dashboard showing only their enrolled courses.
    *   They can view detailed course information, including the full curriculum and study material links.
    *   Displays a summary of their fee status and course progress for each course.
    *   Student academic progress (completed units) is now saved to the database.

*   **Lead Management (`/dashboard`)**: `(Completed)`
    *   Admins can view, create, edit, delete, and filter leads.
    *   The dashboard includes statistics and a chart summarizing lead statuses.

*   **Notifications**: `(Completed)`
    *   An email notification system is active using Resend for key events.
    *   A real-time, in-app notification center (bell icon) has been implemented to alert users to important updates.

*   **AI Assistant (`/ai-assistant`)**: `(Completed)`
    *   A sophisticated, personalized AI assistant is live for students, capable of answering questions about courses, fees, and progress by using Genkit tools to look up live data.

*   **Settings Page (`/settings`)**: `(Completed)`
    *   Users can update their display name and change their password.
    *   Theme toggling (Light/Dark/System) is functional.
    *   Admins can manage system-wide notification settings and run diagnostic checks for the database and admin roles.
    
*   **UI/UX Polish**: `(Completed)`
    *   Skeleton loaders have been added to key pages for smoother perceived performance.
    *   The UI is responsive and includes various user feedback mechanisms like toasts and dialogs.

---

## III. Future Work (Post-MVP)

With the core application now stable and on a live database, these features would be the next logical enhancements.

*   **Advanced Academic Progress Tracking**: The current progress system is based on completed units. A major enhancement would be to also track student grades, assignment submissions, and overall academic performance within each course.
*   **PDF Receipt Generation**: The current system provides a printable HTML receipt. A future enhancement would be to generate and attach downloadable PDF receipts.
*   **Automated Notifications**: Fully automate the overdue fee reminder process to run on a schedule (e.g., daily), which would require infrastructure setup for scheduled jobs.
*   **Global Error Handling & Accessibility**:
    *   Enhance global error handling to provide more specific and helpful feedback to users.
    *   Conduct a thorough accessibility review (A11y) to ensure compliance and usability for all users.
