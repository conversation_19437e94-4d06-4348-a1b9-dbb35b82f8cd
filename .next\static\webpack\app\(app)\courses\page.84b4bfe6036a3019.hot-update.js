"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/courses/page",{

/***/ "(app-pages-browser)/./src/actions/feeActions.ts":
/*!***********************************!*\
  !*** ./src/actions/feeActions.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createStudentFee: () => (/* binding */ createStudentFee),\n/* harmony export */   deleteStudentFee: () => (/* binding */ deleteStudentFee),\n/* harmony export */   generateFeeForEnrolledStudents: () => (/* binding */ generateFeeForEnrolledStudents),\n/* harmony export */   getCourseFeeStructureByCourseId: () => (/* binding */ getCourseFeeStructureByCourseId),\n/* harmony export */   getFinancialReportData: () => (/* binding */ getFinancialReportData),\n/* harmony export */   getStudentFeeById: () => (/* binding */ getStudentFeeById),\n/* harmony export */   getStudentFees: () => (/* binding */ getStudentFees),\n/* harmony export */   getStudentFeesByStudentId: () => (/* binding */ getStudentFeesByStudentId),\n/* harmony export */   recordManualPayment: () => (/* binding */ recordManualPayment),\n/* harmony export */   saveCourseFeeStructure: () => (/* binding */ saveCourseFeeStructure),\n/* harmony export */   sendOverdueFeeReminders: () => (/* binding */ sendOverdueFeeReminders),\n/* harmony export */   updateFeeStatus: () => (/* binding */ updateFeeStatus)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"4084747f2b3d3e6f8a34b2b1e66fc6881813f91e1e\":\"getStudentFees\",\"4088d3fa787ce1ff911d20ed3a5eb5378830c34413\":\"sendOverdueFeeReminders\",\"60393f4bad5ac5ecf434901a5741906ea5e704d1a6\":\"getStudentFeeById\",\"603f0bbb3f7a11423b9f6897bd79a1b845241eea5c\":\"getFinancialReportData\",\"6054b892a1b827022f2d39d3450589bba818c5008f\":\"deleteStudentFee\",\"605e5d0b3411c92d9ec955de6538418f0b8f954a3a\":\"recordManualPayment\",\"6088ea1393fc123e6c96beb7d0c7724a5df7bb77f4\":\"getStudentFeesByStudentId\",\"60d3bcb85b778293f22509672180b38c14fd866766\":\"getCourseFeeStructureByCourseId\",\"60e8493f4a51ac63b9b1122b1605d98455f0109264\":\"createStudentFee\",\"7045a5a974d6e47d04d18c460bb4f9b0dcfa45b1d7\":\"saveCourseFeeStructure\",\"70956bf70df1d1287604bac1d71b60c3ca82567e14\":\"generateFeeForEnrolledStudents\",\"70acfcc4abd0806ef05136d24879ff898816331d31\":\"updateFeeStatus\"} */ \nvar getStudentFees = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4084747f2b3d3e6f8a34b2b1e66fc6881813f91e1e\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFees\");\nvar getStudentFeeById = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60393f4bad5ac5ecf434901a5741906ea5e704d1a6\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFeeById\");\nvar getStudentFeesByStudentId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"6088ea1393fc123e6c96beb7d0c7724a5df7bb77f4\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getStudentFeesByStudentId\");\nvar createStudentFee = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60e8493f4a51ac63b9b1122b1605d98455f0109264\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createStudentFee\");\nvar recordManualPayment = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"605e5d0b3411c92d9ec955de6538418f0b8f954a3a\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"recordManualPayment\");\nvar updateFeeStatus = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"70acfcc4abd0806ef05136d24879ff898816331d31\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"updateFeeStatus\");\nvar deleteStudentFee = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"6054b892a1b827022f2d39d3450589bba818c5008f\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteStudentFee\");\nvar sendOverdueFeeReminders = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"4088d3fa787ce1ff911d20ed3a5eb5378830c34413\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"sendOverdueFeeReminders\");\nvar generateFeeForEnrolledStudents = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"70956bf70df1d1287604bac1d71b60c3ca82567e14\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"generateFeeForEnrolledStudents\");\nvar getFinancialReportData = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"603f0bbb3f7a11423b9f6897bd79a1b845241eea5c\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getFinancialReportData\");\nvar getCourseFeeStructureByCourseId = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"60d3bcb85b778293f22509672180b38c14fd866766\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getCourseFeeStructureByCourseId\");\nvar saveCourseFeeStructure = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"7045a5a974d6e47d04d18c460bb4f9b0dcfa45b1d7\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"saveCourseFeeStructure\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/actions/feeActions.ts\n"));

/***/ })

});