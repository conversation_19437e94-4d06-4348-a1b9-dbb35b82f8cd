"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/layout",{

/***/ "(app-pages-browser)/./src/components/navigation/UserProfileSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/navigation/UserProfileSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProfileSection: () => (/* binding */ UserProfileSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronUp_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronUp,LogOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronUp_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronUp,LogOut!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_notifications_NotificationBell__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/notifications/NotificationBell */ \"(app-pages-browser)/./src/components/notifications/NotificationBell.tsx\");\n/* __next_internal_client_entry_do_not_use__ UserProfileSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction UserProfileSection() {\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    if (!user) return null;\n    const getInitials = (name)=>{\n        if (!name) return 'U';\n        return name.split(' ').map((n)=>n[0]).join('').toUpperCase();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 px-2 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications_NotificationBell__WEBPACK_IMPORTED_MODULE_5__.NotificationBell, {}, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Notifications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuItem, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_4__.SidebarMenuButton, {\n                                size: \"lg\",\n                                className: \"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                        className: \"h-8 w-8 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                src: \"https://placehold.co/100x100.png?text=\".concat(getInitials(user.name)),\n                                                alt: user.name || 'User'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                className: \"rounded-lg\",\n                                                children: getInitials(user.name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid flex-1 text-left text-sm leading-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-semibold\",\n                                                children: user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate text-xs text-muted-foreground\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronUp_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"ml-auto size-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                            className: \"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\",\n                            side: \"bottom\",\n                            align: \"end\",\n                            sideOffset: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuLabel, {\n                                    className: \"p-0 font-normal\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-1 py-1.5 text-left text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                                className: \"h-8 w-8 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                        src: \"https://placehold.co/100x100.png?text=\".concat(getInitials(user.name)),\n                                                        alt: user.name || 'User'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                        className: \"rounded-lg\",\n                                                        children: getInitials(user.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid flex-1 text-left text-sm leading-tight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"truncate font-semibold\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"truncate text-xs text-muted-foreground\",\n                                                        children: user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuItem, {\n                                    onClick: logout,\n                                    className: \"text-destructive focus:bg-destructive/10 focus:text-destructive\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronUp_LogOut_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                            lineNumber: 96,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Log out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(UserProfileSection, \"SlSPRKmTohGnoLiiApupaRii2Oc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_1__.useAuth\n    ];\n});\n_c = UserProfileSection;\nvar _c;\n$RefreshReg$(_c, \"UserProfileSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/UserProfileSection.tsx\n"));

/***/ })

});