
# EduLite Setup Guide

This guide explains how to configure essential third-party services for the EduLite application, including user roles and payment processing.

## 1. Managing User Roles (Admin & Accountant)

This section explains how to create new user accounts in Firebase and assign them 'admin' or 'accountant' roles for the EduLite application. These roles are critical for controlling access to different features.

**Critical Note: Setting Custom Claims is MANDATORY**

You **cannot** set Firebase Custom User Claims directly through the Firebase Console (the web dashboard). Custom claims **must** be set programmatically using the Firebase Admin SDK or the Firebase CLI, as detailed below.

If a user does not have a valid `role` custom claim, they will default to a 'student' role, lacking access to administrative functionalities.

### Prerequisites

1.  **Firebase Project:** Your EduLite Firebase project should be set up.
2.  **Firebase User Accounts:** The users you want to assign roles to must already have user accounts in Firebase Authentication. You can create these directly in the Firebase Console (Authentication > Users > Add user).
3.  **Tooling:** You'll need the **Firebase CLI** installed and authenticated. ([Install Firebase CLI](https://firebase.google.com/docs/cli#install_the_firebase_cli))

### Using the Firebase CLI

This method is convenient for manually assigning roles.

#### Step 1: Get the User's UID

Find the Unique User ID (UID) of the Firebase user in your Firebase Console under Authentication > Users.

#### Step 2: Set Custom User Claims

Open your terminal and use the following command, replacing `<uid>` with the user's actual UID.

*   **To assign an 'admin' role:**
    ```bash
    npx firebase auth:set-custom-user-claims <uid> '{ "role": "admin" }'
    ```
*   **To assign an 'accountant' role:**
    ```bash
    npx firebase auth:set-custom-user-claims <uid> '{ "role": "accountant" }'
    ```

**Important:** The key `role` is what the EduLite application expects. Valid roles are "admin", "accountant", and "student". The user may need to sign out and sign back in for the changes to take effect.

---

## 2. Setting Up Razorpay for Payments

The application uses Razorpay's **Quick Pay Button** for a simplified payment process. This requires configuration in both the Razorpay Dashboard and your `.env` file.

### Step 1: Create a Razorpay Payment Button

1.  Log in to your **Razorpay Dashboard**.
2.  Navigate to **Payment Button** from the left sidebar.
3.  Click **Create Payment Button**.
4.  **Button Type**: Select **Pay with Custom Amount**. This is crucial as each student's fee amount will be different.
5.  **Button Details**:
    *   **Button Label**: Give it a name like "Pay EduLite Fee".
    *   **Amount Field Label**: "Amount" is fine.
    *   **Description (Optional)**: You can add a short description.
6.  **Custom Fields**:
    *   Click **Add New Field**.
    *   **Field Type**: `Hidden`
    *   **Field Label**: `fee_id` (must be exactly this, all lowercase)
    *   Click **Add**.
    *   Repeat to add another `Hidden` field with the label `student_id`.
7.  **Button Theme**: Customize the color to match the app's theme (e.g., `#3F51B5` - Deep Blue).
8.  Click **Create Payment Button**.

After creation, Razorpay will show you the new button. In the URL or code snippet, find the **Button ID**. It will start with `pl_`. Copy this ID.

### Step 2: Update Your `.env` File

Open the `.env` file in your project and update the following variables:

```env
# Get these from Razorpay Dashboard -> Settings -> API Keys
NEXT_PUBLIC_RAZORPAY_KEY_ID=rzp_test_YourKeyIdHere
RAZORPAY_KEY_SECRET=YourKeySecretHere

# The ID you copied in Step 1
NEXT_PUBLIC_RAZORPAY_PAYMENT_BUTTON_ID=pl_YourPaymentButtonIdHere

# A secure, random string you create for the webhook
RAZORPAY_WEBHOOK_SECRET=YourSecureRandomStringHere
```

### Step 3: Set Up the Webhook in Razorpay

The webhook is essential for Razorpay to notify your app when a payment is successful.

1.  In your **Razorpay Dashboard**, go to **Settings > Webhooks**.
2.  Click **Add New Webhook**.
3.  **Webhook URL**: Enter the URL of your deployed application followed by `/api/webhooks/razorpay`.
    *   Example: `https://your-app-name.web.app/api/webhooks/razorpay`
4.  **Secret**: Paste the same secure, random string you created for `RAZORPAY_WEBHOOK_SECRET` in your `.env` file.
5.  **Active Events**: Check the box for **`payment.captured`**. This is the only event the app needs.
6.  Click **Create Webhook**.

Once these steps are complete, your application will be fully configured to process payments through Razorpay.
