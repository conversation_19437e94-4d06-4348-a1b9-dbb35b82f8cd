
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Logo } from '@/components/navigation/Logo';
import Link from 'next/link';
import { ApplyPageClient } from '@/components/apply/ApplyPageClient';

export default function ApplyPage() {

  return (
    <div className="min-h-screen bg-background py-8 px-4 flex flex-col items-center">
      <div className="mb-8">
        <Logo size="lg" />
      </div>
      <Card className="w-full max-w-2xl shadow-xl">
        <CardHeader className="text-center">
          <CardTitle className="font-headline text-3xl text-primary">Apply to EduLite</CardTitle>
          <CardDescription>Fill out the form below to start your journey with us.</CardDescription>
        </CardHeader>
        <CardContent>
          <ApplyPageClient />
        </CardContent>
      </Card>
      <p className="mt-8 text-center text-sm text-muted-foreground">
        Already have an account?{' '}
        <Link href="/login" className="font-medium text-accent hover:underline">
          Sign In
        </Link>
      </p>
    </div>
  );
}
