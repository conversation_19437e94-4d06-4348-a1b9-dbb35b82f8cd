"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/actions/courseActions.ts":
/*!**************************************!*\
  !*** ./src/actions/courseActions.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCourse: () => (/* binding */ createCourse),\n/* harmony export */   deleteCourse: () => (/* binding */ deleteCourse),\n/* harmony export */   getCourseById: () => (/* binding */ getCourseById),\n/* harmony export */   getCourses: () => (/* binding */ getCourses),\n/* harmony export */   updateCourse: () => (/* binding */ updateCourse)\n/* harmony export */ });\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! private-next-rsc-action-client-wrapper */ \"(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/action-client-wrapper.js\");\n/* harmony import */ var private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_action_entry_do_not_use__ {\"00020dc3a8b2e855915e627d7fd6224e5d3203a36e\":\"getCourses\",\"401bea3e0808dd50147007da2aab5635a5954f2db7\":\"getCourseById\",\"603e5a9218889caa8f30659bec17c81a8d749669cd\":\"createCourse\",\"603e8d8d34daf6d5435c21b783780d3862c35b85d4\":\"deleteCourse\",\"605f41b03a45413902bd8ed183e97034b4ea42f1f8\":\"updateCourse\"} */ \nvar getCourses = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"00020dc3a8b2e855915e627d7fd6224e5d3203a36e\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getCourses\");\nvar getCourseById = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"401bea3e0808dd50147007da2aab5635a5954f2db7\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"getCourseById\");\nvar createCourse = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"603e5a9218889caa8f30659bec17c81a8d749669cd\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"createCourse\");\nvar updateCourse = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"605f41b03a45413902bd8ed183e97034b4ea42f1f8\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"updateCourse\");\nvar deleteCourse = /*#__PURE__*/ (0,private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.createServerReference)(\"603e8d8d34daf6d5435c21b783780d3862c35b85d4\", private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.callServer, void 0, private_next_rsc_action_client_wrapper__WEBPACK_IMPORTED_MODULE_0__.findSourceMapURL, \"deleteCourse\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/actions/courseActions.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(app)/financial-reports/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FinancialReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _actions_feeActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/actions/feeActions */ \"(app-pages-browser)/./src/actions/feeActions.ts\");\n/* harmony import */ var _actions_courseActions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/courseActions */ \"(app-pages-browser)/./src/actions/courseActions.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter-x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/piggy-bank.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-bar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst feeStatuses = [\n    'Pending',\n    'Paid',\n    'Partially Paid',\n    'Overdue'\n];\nconst statusVariantMap = {\n    Pending: 'default',\n    'Partially Paid': 'secondary',\n    Paid: 'outline',\n    Overdue: 'destructive'\n};\nfunction FinancialReportsPage() {\n    _s();\n    const { user, isLoading: isAuthLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined); // Start with no date filter to show all records\n    const [courseId, setCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [paymentTransactions, setPaymentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Group courses by name and code, similar to apply page\n    const groupedCourses = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FinancialReportsPage.useMemo[groupedCourses]\": ()=>{\n            return courses.reduce({\n                \"FinancialReportsPage.useMemo[groupedCourses]\": (acc, course)=>{\n                    const groupName = \"\".concat(course.name, \" (\").concat(course.code, \")\");\n                    if (!acc[groupName]) {\n                        acc[groupName] = [];\n                    }\n                    acc[groupName].push(course);\n                    return acc;\n                }\n            }[\"FinancialReportsPage.useMemo[groupedCourses]\"], {});\n        }\n    }[\"FinancialReportsPage.useMemo[groupedCourses]\"], [\n        courses\n    ]);\n    const fetchReportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FinancialReportsPage.useCallback[fetchReportData]\": async ()=>{\n            if (!user || !_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser) return;\n            setIsLoading(true);\n            try {\n                const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser.getIdToken();\n                const filters = {\n                    startDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, 'yyyy-MM-dd') : undefined,\n                    endDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.to) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, 'yyyy-MM-dd') : undefined,\n                    courseId: courseId || undefined,\n                    status: status || undefined\n                };\n                const [data, transactions] = await Promise.all([\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getFinancialReportData)(filters, idToken),\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getAllPaymentTransactions)(idToken)\n                ]);\n                setReportData(data);\n                setPaymentTransactions(transactions);\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"Could not fetch report data.\";\n                toast({\n                    title: \"Error\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"FinancialReportsPage.useCallback[fetchReportData]\"], [\n        user,\n        dateRange,\n        courseId,\n        status,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            async function fetchInitialCourses() {\n                try {\n                    if (!user) return;\n                    const idToken = await user.getIdToken();\n                    const fetchedCourses = await (0,_actions_courseActions__WEBPACK_IMPORTED_MODULE_3__.getCourses)(idToken);\n                    setCourses(fetchedCourses);\n                } catch (error) {\n                    toast({\n                        title: \"Error\",\n                        description: \"Could not load courses for filtering.\",\n                        variant: \"destructive\"\n                    });\n                }\n            }\n            fetchInitialCourses();\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        user,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            if (!isAuthLoading && user) {\n                fetchReportData();\n            }\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        fetchReportData,\n        isAuthLoading,\n        user\n    ]);\n    const clearFilters = ()=>{\n        setDateRange(undefined); // Clear date range to show all records\n        setCourseId('');\n        setStatus('');\n        // Fetch data immediately after clearing filters\n        setTimeout(()=>fetchReportData(), 100);\n    };\n    const summaryStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FinancialReportsPage.useMemo[summaryStats]\": ()=>{\n            const totalDue = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalDue\": (sum, fee)=>sum + fee.amountDue\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalDue\"], 0);\n            const totalPaid = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalPaid\": (sum, fee)=>sum + fee.amountPaid\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalPaid\"], 0);\n            const totalOutstanding = totalDue - totalPaid;\n            return {\n                totalDue,\n                totalPaid,\n                totalOutstanding,\n                recordCount: reportData.length\n            };\n        }\n    }[\"FinancialReportsPage.useMemo[summaryStats]\"], [\n        reportData\n    ]);\n    const exportToCSV = ()=>{\n        if (reportData.length === 0) {\n            toast({\n                title: \"No data to export\",\n                variant: \"default\"\n            });\n            return;\n        }\n        const headers = [\n            'ID',\n            'Student Name',\n            'Course Name',\n            'Fee Description',\n            'Amount Due',\n            'Amount Paid',\n            'Remaining',\n            'Due Date',\n            'Status',\n            'Last Payment Date'\n        ];\n        const csvRows = [\n            headers.join(',')\n        ];\n        for (const fee of reportData){\n            const remaining = fee.amountDue - fee.amountPaid;\n            const values = [\n                fee.id,\n                '\"'.concat(fee.studentName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.courseName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.feeDescription.replace(/\"/g, '\"\"'), '\"'),\n                fee.amountDue.toFixed(2),\n                fee.amountPaid.toFixed(2),\n                remaining.toFixed(2),\n                fee.dueDate,\n                fee.status,\n                fee.lastPaymentDate || ''\n            ];\n            csvRows.push(values.join(','));\n        }\n        const csvString = csvRows.join('\\n');\n        const blob = new Blob([\n            csvString\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"financial_report_\".concat(new Date().toISOString().split('T')[0], \".csv\"));\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                        children: \"Financial Reports\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Generate and view financial reports based on various filters.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 160,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                            children: \"Report Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Due Date Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"justify-start text-left font-normal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 176,\n                                                            columnNumber: 33\n                                                        }, this),\n                                                        (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? dateRange.to ? \"\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\"), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, \"LLL dd, y\")) : (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\") : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Pick a date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                className: \"w-auto p-0\",\n                                                align: \"start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                    mode: \"range\",\n                                                    selected: dateRange,\n                                                    onSelect: setDateRange,\n                                                    initialFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: courseId || '_all_',\n                                        onValueChange: (value)=>setCourseId(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Courses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 196,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Courses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    Object.keys(groupedCourses).length > 0 ? Object.entries(groupedCourses).map((param)=>{\n                                                        let [groupName, coursesInGroup] = param;\n                                                        if (coursesInGroup.length === 1) {\n                                                            const course = coursesInGroup[0];\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                value: course.id,\n                                                                children: groupName\n                                                            }, course.id, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                                lineNumber: 204,\n                                                                columnNumber: 37\n                                                            }, this);\n                                                        } else {\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectGroup, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectLabel, {\n                                                                        children: groupName\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 39\n                                                                    }, this),\n                                                                    coursesInGroup.map((course)=>{\n                                                                        var _course_specializations;\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                                            value: course.id,\n                                                                            children: ((_course_specializations = course.specializations) === null || _course_specializations === void 0 ? void 0 : _course_specializations[0]) || 'General'\n                                                                        }, course.id, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                                            lineNumber: 213,\n                                                                            columnNumber: 41\n                                                                        }, this);\n                                                                    })\n                                                                ]\n                                                            }, groupName, true, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 37\n                                                            }, this);\n                                                        }\n                                                    }) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"no-courses\",\n                                                        disabled: true,\n                                                        children: \"No courses available\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 222,\n                                                        columnNumber: 31\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: status || '_all_',\n                                        onValueChange: (value)=>setStatus(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    feeStatuses.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                            value: s,\n                                                            className: \"capitalize\",\n                                                            children: s\n                                                        }, s, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 51\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: fetchReportData,\n                                        disabled: isLoading,\n                                        className: \"w-full\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 38\n                                        }, this) : 'Apply Filters'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: clearFilters,\n                                        disabled: isLoading,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 167,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 163,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Collected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalPaid.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Outstanding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-destructive\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalOutstanding.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 14\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Filtered Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: summaryStats.recordCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 268,\n                        columnNumber: 14\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        children: \"Filtered Fee Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                        children: [\n                                            \"A detailed list of fee records matching the selected filters. Found \",\n                                            reportData.length,\n                                            \" records.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                onClick: exportToCSV,\n                                disabled: isLoading || reportData.length === 0,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 22\n                        }, this) : reportData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No records match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 305,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 306,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Due Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 309,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Due\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Paid\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 303,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableBody, {\n                                        children: reportData.map((fee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: fee.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.courseName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 318,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.feeDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(fee.dueDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                            variant: statusVariantMap[fee.status],\n                                                            className: \"capitalize\",\n                                                            children: fee.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 321,\n                                                            columnNumber: 52\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountDue.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 322,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountPaid.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, fee.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 301,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 279,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Payment Transaction History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 25\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                            variant: \"outline\",\n                                            children: [\n                                                paymentTransactions.length,\n                                                \" transactions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 336,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                children: \"Detailed history of all payment transactions recorded in the system.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 348,\n                            columnNumber: 22\n                        }, this) : paymentTransactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No payment transactions found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Payment Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 361,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Payment Method\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Transaction ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Recorded By\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Notes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableBody, {\n                                        children: paymentTransactions.map((transaction)=>{\n                                            var _reportData_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(transaction.paymentDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: transaction.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: ((_reportData_find = reportData.find((fee)=>fee.id === transaction.feeId)) === null || _reportData_find === void 0 ? void 0 : _reportData_find.feeDescription) || 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 377,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: transaction.paymentMethod\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 381,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            transaction.amount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.transactionId || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.recordedBy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.notes || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, transaction.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 37\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 334,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, this);\n}\n_s(FinancialReportsPage, \"gy0FAx+S5dFAFmtZDqTbDG6Nw7Y=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = FinancialReportsPage;\nvar _c;\n$RefreshReg$(_c, \"FinancialReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvKGFwcCkvZmluYW5jaWFsLXJlcG9ydHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBR3lFO0FBRWdCO0FBQ3BDO0FBQ0o7QUFDWDtBQUNPO0FBRW9EO0FBQ2pEO0FBQ2tDO0FBQzlCO0FBQzZFO0FBQzNCO0FBQ3hEO0FBQ007QUFDaUY7QUFFMUY7QUFFM0MsTUFBTTRDLGNBQTJCO0lBQUM7SUFBVztJQUFRO0lBQWtCO0NBQVU7QUFFakYsTUFBTUMsbUJBQTJGO0lBQy9GQyxTQUFTO0lBQ1Qsa0JBQWtCO0lBQ2xCQyxNQUFNO0lBQ05DLFNBQVM7QUFDWDtBQUVlLFNBQVNDOztJQUN0QixNQUFNLEVBQUVDLElBQUksRUFBRUMsV0FBV0MsYUFBYSxFQUFFLEdBQUc1Qyw4REFBT0E7SUFDbEQsTUFBTSxFQUFFNkMsS0FBSyxFQUFFLEdBQUczQywwREFBUUE7SUFFMUIsTUFBTSxDQUFDNEMsV0FBV0MsYUFBYSxHQUFHdEQsK0NBQVFBLENBQXdCdUQsWUFBWSxnREFBZ0Q7SUFDOUgsTUFBTSxDQUFDQyxVQUFVQyxZQUFZLEdBQUd6RCwrQ0FBUUEsQ0FBUztJQUNqRCxNQUFNLENBQUMwRCxRQUFRQyxVQUFVLEdBQUczRCwrQ0FBUUEsQ0FBaUI7SUFFckQsTUFBTSxDQUFDNEQsU0FBU0MsV0FBVyxHQUFHN0QsK0NBQVFBLENBQVcsRUFBRTtJQUNuRCxNQUFNLENBQUM4RCxZQUFZQyxjQUFjLEdBQUcvRCwrQ0FBUUEsQ0FBZSxFQUFFO0lBQzdELE1BQU0sQ0FBQ2dFLHFCQUFxQkMsdUJBQXVCLEdBQUdqRSwrQ0FBUUEsQ0FBdUIsRUFBRTtJQUN2RixNQUFNLENBQUNrRCxXQUFXZ0IsYUFBYSxHQUFHbEUsK0NBQVFBLENBQUM7SUFFM0Msd0RBQXdEO0lBQ3hELE1BQU1tRSxpQkFBaUJqRSw4Q0FBT0E7d0RBQUM7WUFDN0IsT0FBTzBELFFBQVFRLE1BQU07Z0VBQTJCLENBQUNDLEtBQUtDO29CQUNwRCxNQUFNQyxZQUFZLEdBQW1CRCxPQUFoQkEsT0FBT0UsSUFBSSxFQUFDLE1BQWdCLE9BQVpGLE9BQU9HLElBQUksRUFBQztvQkFDakQsSUFBSSxDQUFDSixHQUFHLENBQUNFLFVBQVUsRUFBRTt3QkFDbkJGLEdBQUcsQ0FBQ0UsVUFBVSxHQUFHLEVBQUU7b0JBQ3JCO29CQUNBRixHQUFHLENBQUNFLFVBQVUsQ0FBQ0csSUFBSSxDQUFDSjtvQkFDcEIsT0FBT0Q7Z0JBQ1Q7K0RBQUcsQ0FBQztRQUNOO3VEQUFHO1FBQUNUO0tBQVE7SUFFWixNQUFNZSxrQkFBa0J4RSxrREFBV0E7NkRBQUM7WUFDbEMsSUFBSSxDQUFDOEMsUUFBUSxDQUFDekMsK0NBQUlBLENBQUNvRSxXQUFXLEVBQUU7WUFDaENWLGFBQWE7WUFDYixJQUFJO2dCQUNGLE1BQU1XLFVBQVUsTUFBTXJFLCtDQUFJQSxDQUFDb0UsV0FBVyxDQUFDRSxVQUFVO2dCQUNqRCxNQUFNQyxVQUFVO29CQUNkQyxXQUFXM0IsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXNEIsSUFBSSxJQUFHdkMsK0VBQU1BLENBQUNXLFVBQVU0QixJQUFJLEVBQUUsZ0JBQWdCMUI7b0JBQ3BFMkIsU0FBUzdCLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVzhCLEVBQUUsSUFBR3pDLCtFQUFNQSxDQUFDVyxVQUFVOEIsRUFBRSxFQUFFLGdCQUFnQjVCO29CQUM5REMsVUFBVUEsWUFBWUQ7b0JBQ3RCRyxRQUFRQSxVQUFVSDtnQkFDcEI7Z0JBQ0EsTUFBTSxDQUFDNkIsTUFBTUMsYUFBYSxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztvQkFDN0NuRiwyRUFBc0JBLENBQUMyRSxTQUFTRjtvQkFDaEN4RSw4RUFBeUJBLENBQUN3RTtpQkFDM0I7Z0JBQ0RkLGNBQWNxQjtnQkFDZG5CLHVCQUF1Qm9CO1lBQ3pCLEVBQUUsT0FBT0csT0FBTztnQkFDZCxNQUFNQyxlQUFlRCxpQkFBaUJFLFFBQVFGLE1BQU1HLE9BQU8sR0FBRztnQkFDOUR2QyxNQUFNO29CQUFFd0MsT0FBTztvQkFBU0MsYUFBYUo7b0JBQWNLLFNBQVM7Z0JBQWM7WUFDNUUsU0FBVTtnQkFDUjVCLGFBQWE7WUFDZjtRQUNGOzREQUFHO1FBQUNqQjtRQUFNSTtRQUFXRztRQUFVRTtRQUFRTjtLQUFNO0lBRTdDbkQsZ0RBQVNBOzBDQUFDO1lBQ1IsZUFBZThGO2dCQUNiLElBQUk7b0JBQ0YsSUFBSSxDQUFDOUMsTUFBTTtvQkFDWCxNQUFNNEIsVUFBVSxNQUFNNUIsS0FBSzZCLFVBQVU7b0JBQ3JDLE1BQU1rQixpQkFBaUIsTUFBTTFGLGtFQUFVQSxDQUFDdUU7b0JBQ3hDaEIsV0FBV21DO2dCQUNiLEVBQUUsT0FBT1IsT0FBTztvQkFDZHBDLE1BQU07d0JBQUV3QyxPQUFPO3dCQUFTQyxhQUFhO3dCQUF5Q0MsU0FBUztvQkFBYztnQkFDdkc7WUFDRjtZQUNBQztRQUNGO3lDQUFHO1FBQUM5QztRQUFNRztLQUFNO0lBRWhCbkQsZ0RBQVNBOzBDQUFDO1lBQ1IsSUFBSSxDQUFDa0QsaUJBQWlCRixNQUFNO2dCQUN4QjBCO1lBQ0o7UUFDRjt5Q0FBRztRQUFDQTtRQUFpQnhCO1FBQWVGO0tBQUs7SUFFekMsTUFBTWdELGVBQWU7UUFDbkIzQyxhQUFhQyxZQUFZLHVDQUF1QztRQUNoRUUsWUFBWTtRQUNaRSxVQUFVO1FBQ1YsZ0RBQWdEO1FBQ2hEdUMsV0FBVyxJQUFNdkIsbUJBQW1CO0lBQ3RDO0lBRUEsTUFBTXdCLGVBQWVqRyw4Q0FBT0E7c0RBQUM7WUFDM0IsTUFBTWtHLFdBQVd0QyxXQUFXTSxNQUFNO3VFQUFDLENBQUNpQyxLQUFLQyxNQUFRRCxNQUFNQyxJQUFJQyxTQUFTO3NFQUFFO1lBQ3RFLE1BQU1DLFlBQVkxQyxXQUFXTSxNQUFNO3dFQUFDLENBQUNpQyxLQUFLQyxNQUFRRCxNQUFNQyxJQUFJRyxVQUFVO3VFQUFFO1lBQ3hFLE1BQU1DLG1CQUFtQk4sV0FBV0k7WUFDcEMsT0FBTztnQkFBRUo7Z0JBQVVJO2dCQUFXRTtnQkFBa0JDLGFBQWE3QyxXQUFXOEMsTUFBTTtZQUFDO1FBQ2pGO3FEQUFHO1FBQUM5QztLQUFXO0lBRWYsTUFBTStDLGNBQWM7UUFDakIsSUFBSS9DLFdBQVc4QyxNQUFNLEtBQUssR0FBRztZQUMxQnhELE1BQU07Z0JBQUV3QyxPQUFPO2dCQUFxQkUsU0FBUztZQUFVO1lBQ3ZEO1FBQ0o7UUFDQSxNQUFNZ0IsVUFBVTtZQUFDO1lBQU07WUFBZ0I7WUFBZTtZQUFtQjtZQUFjO1lBQWU7WUFBYTtZQUFZO1lBQVU7U0FBb0I7UUFDN0osTUFBTUMsVUFBVTtZQUFDRCxRQUFRRSxJQUFJLENBQUM7U0FBSztRQUVuQyxLQUFLLE1BQU1WLE9BQU94QyxXQUFZO1lBQzFCLE1BQU1tRCxZQUFZWCxJQUFJQyxTQUFTLEdBQUdELElBQUlHLFVBQVU7WUFDaEQsTUFBTVMsU0FBUztnQkFDWFosSUFBSWEsRUFBRTtnQkFDTCxJQUF1QyxPQUFwQ2IsSUFBSWMsV0FBVyxDQUFDQyxPQUFPLENBQUMsTUFBTSxPQUFNO2dCQUN2QyxJQUFzQyxPQUFuQ2YsSUFBSWdCLFVBQVUsQ0FBQ0QsT0FBTyxDQUFDLE1BQU0sT0FBTTtnQkFDdEMsSUFBMEMsT0FBdkNmLElBQUlpQixjQUFjLENBQUNGLE9BQU8sQ0FBQyxNQUFNLE9BQU07Z0JBQzNDZixJQUFJQyxTQUFTLENBQUNpQixPQUFPLENBQUM7Z0JBQ3RCbEIsSUFBSUcsVUFBVSxDQUFDZSxPQUFPLENBQUM7Z0JBQ3ZCUCxVQUFVTyxPQUFPLENBQUM7Z0JBQ2xCbEIsSUFBSW1CLE9BQU87Z0JBQ1huQixJQUFJNUMsTUFBTTtnQkFDVjRDLElBQUlvQixlQUFlLElBQUk7YUFDMUI7WUFDRFgsUUFBUXJDLElBQUksQ0FBQ3dDLE9BQU9GLElBQUksQ0FBQztRQUM3QjtRQUVBLE1BQU1XLFlBQVlaLFFBQVFDLElBQUksQ0FBQztRQUMvQixNQUFNWSxPQUFPLElBQUlDLEtBQUs7WUFBQ0Y7U0FBVSxFQUFFO1lBQUVHLE1BQU07UUFBMEI7UUFDckUsTUFBTUMsTUFBTUMsSUFBSUMsZUFBZSxDQUFDTDtRQUNoQyxNQUFNTSxPQUFPQyxTQUFTQyxhQUFhLENBQUM7UUFDcENGLEtBQUtHLFlBQVksQ0FBQyxRQUFRTjtRQUMxQkcsS0FBS0csWUFBWSxDQUFDLFlBQVksb0JBQTJELE9BQXZDLElBQUlDLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUM7UUFDekZMLFNBQVNNLElBQUksQ0FBQ0MsV0FBVyxDQUFDUjtRQUMxQkEsS0FBS1MsS0FBSztRQUNWUixTQUFTTSxJQUFJLENBQUNHLFdBQVcsQ0FBQ1Y7UUFDMUJGLElBQUlhLGVBQWUsQ0FBQ2Q7SUFDdEI7SUFHQSxxQkFDRSw4REFBQ2U7UUFBSUMsV0FBVTs7MEJBQ1gsOERBQUNEOztrQ0FDRyw4REFBQ0U7d0JBQUdELFdBQVU7a0NBQStEOzs7Ozs7a0NBQzdFLDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBd0I7Ozs7Ozs7Ozs7OzswQkFHekMsOERBQUNySSxxREFBSUE7O2tDQUNELDhEQUFDRywyREFBVUE7a0NBQ1AsNEVBQUNDLDBEQUFTQTtzQ0FBQzs7Ozs7Ozs7Ozs7a0NBRWYsOERBQUNILDREQUFXQTt3QkFBQ29JLFdBQVU7OzBDQUNuQiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNYLDhEQUFDRzt3Q0FBTUgsV0FBVTtrREFBc0I7Ozs7OztrREFDdkMsOERBQUMvSCwyREFBT0E7OzBEQUNKLDhEQUFDRSxrRUFBY0E7Z0RBQUNpSSxPQUFPOzBEQUNuQiw0RUFBQ3BJLHlEQUFNQTtvREFDSCtFLFNBQVM7b0RBQ1RpRCxXQUFVOztzRUFFViw4REFBQzVHLDRKQUFZQTs0REFBQzRHLFdBQVU7Ozs7Ozt3REFDdkIxRixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVc0QixJQUFJLElBQ1o1QixVQUFVOEIsRUFBRSxHQUNSLEdBQTRDekMsT0FBekNBLCtFQUFNQSxDQUFDVyxVQUFVNEIsSUFBSSxFQUFFLGNBQWEsT0FBdUMsT0FBbEN2QywrRUFBTUEsQ0FBQ1csVUFBVThCLEVBQUUsRUFBRSxnQkFFakV6QywrRUFBTUEsQ0FBQ1csVUFBVTRCLElBQUksRUFBRSw2QkFHM0IsOERBQUNtRTtzRUFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7MERBSWxCLDhEQUFDbkksa0VBQWNBO2dEQUFDOEgsV0FBVTtnREFBYU0sT0FBTTswREFDekMsNEVBQUNsSSw4REFBUUE7b0RBQUNtSSxNQUFLO29EQUFRQyxVQUFVbEc7b0RBQVdtRyxVQUFVbEc7b0RBQWNtRyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJM0YsOERBQUNYO2dDQUFJQyxXQUFVOztrREFDWiw4REFBQ0c7d0NBQU1ILFdBQVU7a0RBQXNCOzs7Ozs7a0RBQ3ZDLDhEQUFDM0gsMERBQU1BO3dDQUFDc0ksT0FBT2xHLFlBQVk7d0NBQVNtRyxlQUFlLENBQUNELFFBQVVqRyxZQUFZaUcsVUFBVSxVQUFVLEtBQUtBOzswREFDL0YsOERBQUNuSSxpRUFBYUE7MERBQUMsNEVBQUNDLCtEQUFXQTtvREFBQ29JLGFBQVk7Ozs7Ozs7Ozs7OzBEQUN4Qyw4REFBQ3ZJLGlFQUFhQTs7a0VBQ1YsOERBQUNDLDhEQUFVQTt3REFBQ29JLE9BQU07a0VBQVE7Ozs7OztvREFDekJHLE9BQU9DLElBQUksQ0FBQzNGLGdCQUFnQnlDLE1BQU0sR0FBRyxJQUNwQ2lELE9BQU9FLE9BQU8sQ0FBQzVGLGdCQUFnQjZGLEdBQUcsQ0FBQzs0REFBQyxDQUFDekYsV0FBVzBGLGVBQWU7d0RBQzdELElBQUlBLGVBQWVyRCxNQUFNLEtBQUssR0FBRzs0REFDL0IsTUFBTXRDLFNBQVMyRixjQUFjLENBQUMsRUFBRTs0REFDaEMscUJBQ0UsOERBQUMzSSw4REFBVUE7Z0VBQWlCb0ksT0FBT3BGLE9BQU82QyxFQUFFOzBFQUN6QzVDOytEQURjRCxPQUFPNkMsRUFBRTs7Ozs7d0RBSTlCLE9BQU87NERBQ0wscUJBQ0UsOERBQUMxRiwrREFBV0E7O2tGQUNWLDhEQUFDQywrREFBV0E7a0ZBQUU2Qzs7Ozs7O29FQUNiMEYsZUFBZUQsR0FBRyxDQUFDMUYsQ0FBQUE7NEVBRWZBOzZGQURILDhEQUFDaEQsOERBQVVBOzRFQUFpQm9JLE9BQU9wRixPQUFPNkMsRUFBRTtzRkFDekM3QyxFQUFBQSwwQkFBQUEsT0FBTzRGLGVBQWUsY0FBdEI1Riw4Q0FBQUEsdUJBQXdCLENBQUMsRUFBRSxLQUFJOzJFQURqQkEsT0FBTzZDLEVBQUU7Ozs7Ozs7K0RBSFo1Qzs7Ozs7d0RBU3RCO29EQUNGLG1CQUVBLDhEQUFDakQsOERBQVVBO3dEQUFDb0ksT0FBTTt3REFBYVMsUUFBUTtrRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBDQUtyRCw4REFBQ3JCO2dDQUFJQyxXQUFVOztrREFDWiw4REFBQ0c7d0NBQU1ILFdBQVU7a0RBQXNCOzs7Ozs7a0RBQ3ZDLDhEQUFDM0gsMERBQU1BO3dDQUFDc0ksT0FBT2hHLFVBQVU7d0NBQVNpRyxlQUFlLENBQUNELFFBQWtCL0YsVUFBVStGLFVBQVUsVUFBVSxLQUFLQTs7MERBQ25HLDhEQUFDbkksaUVBQWFBOzBEQUFDLDRFQUFDQywrREFBV0E7b0RBQUNvSSxhQUFZOzs7Ozs7Ozs7OzswREFDeEMsOERBQUN2SSxpRUFBYUE7O2tFQUNWLDhEQUFDQyw4REFBVUE7d0RBQUNvSSxPQUFNO2tFQUFROzs7Ozs7b0RBQ3pCL0csWUFBWXFILEdBQUcsQ0FBQ0ksQ0FBQUEsa0JBQUssOERBQUM5SSw4REFBVUE7NERBQVNvSSxPQUFPVTs0REFBR3JCLFdBQVU7c0VBQWNxQjsyREFBckNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJbEQsOERBQUN0QjtnQ0FBSUMsV0FBVTs7a0RBQ1osOERBQUNoSSx5REFBTUE7d0NBQUNzSixTQUFTMUY7d0NBQWlCd0YsVUFBVWpIO3dDQUFXNkYsV0FBVTtrREFDNUQ3RiwwQkFBWSw4REFBQ2QsNEpBQU9BOzRDQUFDMkcsV0FBVTs7Ozs7bURBQWlDOzs7Ozs7a0RBRXBFLDhEQUFDaEkseURBQU1BO3dDQUFDc0osU0FBU3BFO3dDQUFja0UsVUFBVWpIO3dDQUFXNEMsU0FBUTt3Q0FBUXdFLE1BQUs7OzBEQUN0RSw4REFBQ2hJLDRKQUFPQTtnREFBQ3lHLFdBQVU7Ozs7OzswREFDbkIsOERBQUNLO2dEQUFLTCxXQUFVOzBEQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTFDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ1gsOERBQUNySSxxREFBSUE7OzBDQUNELDhEQUFDRywyREFBVUE7Z0NBQUNrSSxXQUFVOztrREFDbEIsOERBQUNqSSwwREFBU0E7d0NBQUNpSSxXQUFVO2tEQUFzQjs7Ozs7O2tEQUMzQyw4REFBQ3hHLDRKQUFTQTt3Q0FBQ3dHLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FFekIsOERBQUNwSSw0REFBV0E7MENBQ1B1QywwQkFBWSw4REFBQ2hCLDhEQUFRQTtvQ0FBQzZHLFdBQVU7Ozs7O3lEQUFlLDhEQUFDRDtvQ0FBSUMsV0FBVTs7d0NBQXFCO3dDQUFFNUMsYUFBYUssU0FBUyxDQUFDZ0IsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBRzVILDhEQUFDOUcscURBQUlBOzswQ0FDRiw4REFBQ0csMkRBQVVBO2dDQUFDa0ksV0FBVTs7a0RBQ2xCLDhEQUFDakksMERBQVNBO3dDQUFDaUksV0FBVTtrREFBc0I7Ozs7OztrREFDM0MsOERBQUN2Ryw0SkFBZ0JBO3dDQUFDdUcsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUVoQyw4REFBQ3BJLDREQUFXQTswQ0FDUHVDLDBCQUFZLDhEQUFDaEIsOERBQVFBO29DQUFDNkcsV0FBVTs7Ozs7eURBQWUsOERBQUNEO29DQUFJQyxXQUFVOzt3Q0FBc0M7d0NBQUU1QyxhQUFhTyxnQkFBZ0IsQ0FBQ2MsT0FBTyxDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBR3BKLDhEQUFDOUcscURBQUlBOzswQ0FDRiw4REFBQ0csMkRBQVVBO2dDQUFDa0ksV0FBVTs7a0RBQ2xCLDhEQUFDakksMERBQVNBO3dDQUFDaUksV0FBVTtrREFBc0I7Ozs7OztrREFDM0MsOERBQUN0Ryw0SkFBa0JBO3dDQUFDc0csV0FBVTs7Ozs7Ozs7Ozs7OzBDQUVsQyw4REFBQ3BJLDREQUFXQTswQ0FDUHVDLDBCQUFZLDhEQUFDaEIsOERBQVFBO29DQUFDNkcsV0FBVTs7Ozs7eURBQWUsOERBQUNEO29DQUFJQyxXQUFVOzhDQUFzQjVDLGFBQWFRLFdBQVc7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQUt6SCw4REFBQ2pHLHFEQUFJQTs7a0NBQ0QsOERBQUNHLDJEQUFVQTt3QkFBQ2tJLFdBQVU7OzBDQUNsQiw4REFBQ0Q7O2tEQUNHLDhEQUFDaEksMERBQVNBO2tEQUFDOzs7Ozs7a0RBQ1gsOERBQUNGLGdFQUFlQTs7NENBQUM7NENBQ3VEa0QsV0FBVzhDLE1BQU07NENBQUM7Ozs7Ozs7Ozs7Ozs7MENBRzdGLDhEQUFDN0YseURBQU1BO2dDQUFDc0osU0FBU3hEO2dDQUFhc0QsVUFBVWpILGFBQWFZLFdBQVc4QyxNQUFNLEtBQUs7Z0NBQUdkLFNBQVE7O2tEQUNuRiw4REFBQ3pELDRKQUFRQTt3Q0FBQzBHLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7Ozs7a0NBRzdDLDhEQUFDcEksNERBQVdBO2tDQUNQdUMsMEJBQ0ksOERBQUM0Rjs0QkFBSUMsV0FBVTtzQ0FDWDttQ0FBSXdCLE1BQU07NkJBQUcsQ0FBQ1AsR0FBRyxDQUFDLENBQUNRLEdBQUdDLGtCQUFNLDhEQUFDdkksOERBQVFBO29DQUFTNkcsV0FBVTttQ0FBYjBCOzs7Ozs7Ozs7bUNBRWhEM0csV0FBVzhDLE1BQU0sS0FBSyxrQkFDdEIsOERBQUNrQzs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQ0U7MENBQUU7Ozs7Ozs7Ozs7aURBR1AsOERBQUNIOzRCQUFJQyxXQUFVO3NDQUNYLDRFQUFDcEgsd0RBQUtBOztrREFDRiw4REFBQ0ksOERBQVdBO2tEQUNSLDRFQUFDQywyREFBUUE7OzhEQUNMLDhEQUFDRiw0REFBU0E7OERBQUM7Ozs7Ozs4REFDWCw4REFBQ0EsNERBQVNBOzhEQUFDOzs7Ozs7OERBQ1gsOERBQUNBLDREQUFTQTs4REFBQzs7Ozs7OzhEQUNYLDhEQUFDQSw0REFBU0E7OERBQUM7Ozs7Ozs4REFDWCw4REFBQ0EsNERBQVNBOzhEQUFDOzs7Ozs7OERBQ1gsOERBQUNBLDREQUFTQTtvREFBQ2lILFdBQVU7OERBQWE7Ozs7Ozs4REFDbEMsOERBQUNqSCw0REFBU0E7b0RBQUNpSCxXQUFVOzhEQUFhOzs7Ozs7Ozs7Ozs7Ozs7OztrREFHMUMsOERBQUNuSCw0REFBU0E7a0RBQ0xrQyxXQUFXa0csR0FBRyxDQUFDMUQsQ0FBQUEsb0JBQ1osOERBQUN0RSwyREFBUUE7O2tFQUNMLDhEQUFDSCw0REFBU0E7d0RBQUNrSCxXQUFVO2tFQUFlekMsSUFBSWMsV0FBVzs7Ozs7O2tFQUNuRCw4REFBQ3ZGLDREQUFTQTtrRUFBRXlFLElBQUlnQixVQUFVOzs7Ozs7a0VBQzFCLDhEQUFDekYsNERBQVNBO2tFQUFFeUUsSUFBSWlCLGNBQWM7Ozs7OztrRUFDOUIsOERBQUMxRiw0REFBU0E7a0VBQUVhLCtFQUFNQSxDQUFDLElBQUk0RixLQUFLaEMsSUFBSW1CLE9BQU8sR0FBRzs7Ozs7O2tFQUMxQyw4REFBQzVGLDREQUFTQTtrRUFBQyw0RUFBQ0ksd0RBQUtBOzREQUFDNkQsU0FBU2xELGdCQUFnQixDQUFDMEQsSUFBSTVDLE1BQU0sQ0FBQzs0REFBRXFGLFdBQVU7c0VBQWN6QyxJQUFJNUMsTUFBTTs7Ozs7Ozs7Ozs7a0VBQzNGLDhEQUFDN0IsNERBQVNBO3dEQUFDa0gsV0FBVTs7NERBQWE7NERBQUV6QyxJQUFJQyxTQUFTLENBQUNpQixPQUFPLENBQUM7Ozs7Ozs7a0VBQzFELDhEQUFDM0YsNERBQVNBO3dEQUFDa0gsV0FBVTs7NERBQTBDOzREQUFFekMsSUFBSUcsVUFBVSxDQUFDZSxPQUFPLENBQUM7Ozs7Ozs7OytDQVA3RWxCLElBQUlhLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQWtCakQsOERBQUN6RyxxREFBSUE7O2tDQUNELDhEQUFDRywyREFBVUE7OzBDQUNQLDhEQUFDQywwREFBU0E7Z0NBQUNpSSxXQUFVOzBDQUNqQiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDSztzREFBSzs7Ozs7O3NEQUNOLDhEQUFDbkgsd0RBQUtBOzRDQUFDNkQsU0FBUTs7Z0RBQVc5QixvQkFBb0I0QyxNQUFNO2dEQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7MENBRzdELDhEQUFDaEcsZ0VBQWVBOzBDQUFDOzs7Ozs7Ozs7Ozs7a0NBSXJCLDhEQUFDRCw0REFBV0E7a0NBQ1B1QywwQkFDSSw4REFBQzRGOzRCQUFJQyxXQUFVO3NDQUNYO21DQUFJd0IsTUFBTTs2QkFBRyxDQUFDUCxHQUFHLENBQUMsQ0FBQ1EsR0FBR0Msa0JBQU0sOERBQUN2SSw4REFBUUE7b0NBQVM2RyxXQUFVO21DQUFiMEI7Ozs7Ozs7OzttQ0FFaER6RyxvQkFBb0I0QyxNQUFNLEtBQUssa0JBQy9CLDhEQUFDa0M7NEJBQUlDLFdBQVU7c0NBQ1gsNEVBQUNFOzBDQUFFOzs7Ozs7Ozs7O2lEQUdQLDhEQUFDSDs0QkFBSUMsV0FBVTtzQ0FDWCw0RUFBQ3BILHdEQUFLQTs7a0RBQ0YsOERBQUNJLDhEQUFXQTtrREFDUiw0RUFBQ0MsMkRBQVFBOzs4REFDTCw4REFBQ0YsNERBQVNBOzhEQUFDOzs7Ozs7OERBQ1gsOERBQUNBLDREQUFTQTs4REFBQzs7Ozs7OzhEQUNYLDhEQUFDQSw0REFBU0E7OERBQUM7Ozs7Ozs4REFDWCw4REFBQ0EsNERBQVNBOzhEQUFDOzs7Ozs7OERBQ1gsOERBQUNBLDREQUFTQTtvREFBQ2lILFdBQVU7OERBQWE7Ozs7Ozs4REFDbEMsOERBQUNqSCw0REFBU0E7OERBQUM7Ozs7Ozs4REFDWCw4REFBQ0EsNERBQVNBOzhEQUFDOzs7Ozs7OERBQ1gsOERBQUNBLDREQUFTQTs4REFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0RBR25CLDhEQUFDRiw0REFBU0E7a0RBQ0xvQyxvQkFBb0JnRyxHQUFHLENBQUNVLENBQUFBO2dEQU9aNUc7aUVBTlQsOERBQUM5QiwyREFBUUE7O2tFQUNMLDhEQUFDSCw0REFBU0E7d0RBQUNrSCxXQUFVO2tFQUNoQnJHLCtFQUFNQSxDQUFDLElBQUk0RixLQUFLb0MsWUFBWUMsV0FBVyxHQUFHOzs7Ozs7a0VBRS9DLDhEQUFDOUksNERBQVNBO2tFQUFFNkksWUFBWXRELFdBQVc7Ozs7OztrRUFDbkMsOERBQUN2Riw0REFBU0E7a0VBQ0xpQyxFQUFBQSxtQkFBQUEsV0FBVzhHLElBQUksQ0FBQ3RFLENBQUFBLE1BQU9BLElBQUlhLEVBQUUsS0FBS3VELFlBQVlHLEtBQUssZUFBbkQvRyx1Q0FBQUEsaUJBQXNEeUQsY0FBYyxLQUFJOzs7Ozs7a0VBRTdFLDhEQUFDMUYsNERBQVNBO2tFQUNOLDRFQUFDSSx3REFBS0E7NERBQUM2RCxTQUFRO3NFQUFXNEUsWUFBWUksYUFBYTs7Ozs7Ozs7Ozs7a0VBRXZELDhEQUFDakosNERBQVNBO3dEQUFDa0gsV0FBVTs7NERBQTBDOzREQUN6RDJCLFlBQVlLLE1BQU0sQ0FBQ3ZELE9BQU8sQ0FBQzs7Ozs7OztrRUFFakMsOERBQUMzRiw0REFBU0E7d0RBQUNrSCxXQUFVO2tFQUNoQjJCLFlBQVlNLGFBQWEsSUFBSTs7Ozs7O2tFQUVsQyw4REFBQ25KLDREQUFTQTt3REFBQ2tILFdBQVU7a0VBQ2hCMkIsWUFBWU8sVUFBVTs7Ozs7O2tFQUUzQiw4REFBQ3BKLDREQUFTQTt3REFBQ2tILFdBQVU7a0VBQ2hCMkIsWUFBWVEsS0FBSyxJQUFJOzs7Ozs7OytDQXJCZlIsWUFBWXZELEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWlDakU7R0FwWHdCbkU7O1FBQ3FCekMsMERBQU9BO1FBQ2hDRSxzREFBUUE7OztLQUZKdUMiLCJzb3VyY2VzIjpbIkM6XFxDb2RlXFxlZHVsaXRlXFxzcmNcXGFwcFxcKGFwcClcXGZpbmFuY2lhbC1yZXBvcnRzXFxwYWdlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcbid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHR5cGUgeyBTdHVkZW50RmVlLCBDb3Vyc2UsIEZlZVN0YXR1cywgUGF5bWVudFRyYW5zYWN0aW9uIH0gZnJvbSAnQC90eXBlcyc7XG5pbXBvcnQgeyBnZXRGaW5hbmNpYWxSZXBvcnREYXRhLCBnZXRBbGxQYXltZW50VHJhbnNhY3Rpb25zIH0gZnJvbSAnQC9hY3Rpb25zL2ZlZUFjdGlvbnMnO1xuaW1wb3J0IHsgZ2V0Q291cnNlcyB9IGZyb20gJ0AvYWN0aW9ucy9jb3Vyc2VBY3Rpb25zJztcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL0F1dGhDb250ZXh0JztcbmltcG9ydCB7IGF1dGggfSBmcm9tICdAL2xpYi9maXJlYmFzZSc7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0JztcblxuaW1wb3J0IHsgQ2FyZCwgQ2FyZENvbnRlbnQsIENhcmREZXNjcmlwdGlvbiwgQ2FyZEhlYWRlciwgQ2FyZFRpdGxlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2NhcmQnO1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbic7XG5pbXBvcnQgeyBQb3BvdmVyLCBQb3BvdmVyQ29udGVudCwgUG9wb3ZlclRyaWdnZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvcG9wb3Zlcic7XG5pbXBvcnQgeyBDYWxlbmRhciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9jYWxlbmRhcic7XG5pbXBvcnQgeyBTZWxlY3QsIFNlbGVjdENvbnRlbnQsIFNlbGVjdEl0ZW0sIFNlbGVjdFRyaWdnZXIsIFNlbGVjdFZhbHVlLCBTZWxlY3RHcm91cCwgU2VsZWN0TGFiZWwgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2VsZWN0JztcbmltcG9ydCB7IFRhYmxlLCBUYWJsZUJvZHksIFRhYmxlQ2VsbCwgVGFibGVIZWFkLCBUYWJsZUhlYWRlciwgVGFibGVSb3cgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvdGFibGUnO1xuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYmFkZ2UnO1xuaW1wb3J0IHsgU2tlbGV0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc2tlbGV0b24nO1xuaW1wb3J0IHsgQ2FsZW5kYXIgYXMgQ2FsZW5kYXJJY29uLCBMb2FkZXIyLCBEb3dubG9hZCwgRmlsdGVyWCwgUGlnZ3lCYW5rLCBDaXJjbGVEb2xsYXJTaWduLCBCYXJDaGFydEhvcml6b250YWwgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xuaW1wb3J0IHsgRGF0ZVJhbmdlIH0gZnJvbSAncmVhY3QtZGF5LXBpY2tlcic7XG5pbXBvcnQgeyBmb3JtYXQsIGFkZERheXMgfSBmcm9tICdkYXRlLWZucyc7XG5cbmNvbnN0IGZlZVN0YXR1c2VzOiBGZWVTdGF0dXNbXSA9IFsnUGVuZGluZycsICdQYWlkJywgJ1BhcnRpYWxseSBQYWlkJywgJ092ZXJkdWUnXTtcblxuY29uc3Qgc3RhdHVzVmFyaWFudE1hcDogUmVjb3JkPEZlZVN0YXR1cywgJ2RlZmF1bHQnIHwgJ3NlY29uZGFyeScgfCAnb3V0bGluZScgfCAnZGVzdHJ1Y3RpdmUnPiA9IHtcbiAgUGVuZGluZzogJ2RlZmF1bHQnLFxuICAnUGFydGlhbGx5IFBhaWQnOiAnc2Vjb25kYXJ5JyxcbiAgUGFpZDogJ291dGxpbmUnLFxuICBPdmVyZHVlOiAnZGVzdHJ1Y3RpdmUnLFxufTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRmluYW5jaWFsUmVwb3J0c1BhZ2UoKSB7XG4gIGNvbnN0IHsgdXNlciwgaXNMb2FkaW5nOiBpc0F1dGhMb2FkaW5nIH0gPSB1c2VBdXRoKCk7XG4gIGNvbnN0IHsgdG9hc3QgfSA9IHVzZVRvYXN0KCk7XG5cbiAgY29uc3QgW2RhdGVSYW5nZSwgc2V0RGF0ZVJhbmdlXSA9IHVzZVN0YXRlPERhdGVSYW5nZSB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTsgLy8gU3RhcnQgd2l0aCBubyBkYXRlIGZpbHRlciB0byBzaG93IGFsbCByZWNvcmRzXG4gIGNvbnN0IFtjb3Vyc2VJZCwgc2V0Q291cnNlSWRdID0gdXNlU3RhdGU8c3RyaW5nPignJyk7XG4gIGNvbnN0IFtzdGF0dXMsIHNldFN0YXR1c10gPSB1c2VTdGF0ZTxGZWVTdGF0dXMgfCAnJz4oJycpO1xuXG4gIGNvbnN0IFtjb3Vyc2VzLCBzZXRDb3Vyc2VzXSA9IHVzZVN0YXRlPENvdXJzZVtdPihbXSk7XG4gIGNvbnN0IFtyZXBvcnREYXRhLCBzZXRSZXBvcnREYXRhXSA9IHVzZVN0YXRlPFN0dWRlbnRGZWVbXT4oW10pO1xuICBjb25zdCBbcGF5bWVudFRyYW5zYWN0aW9ucywgc2V0UGF5bWVudFRyYW5zYWN0aW9uc10gPSB1c2VTdGF0ZTxQYXltZW50VHJhbnNhY3Rpb25bXT4oW10pO1xuICBjb25zdCBbaXNMb2FkaW5nLCBzZXRJc0xvYWRpbmddID0gdXNlU3RhdGUodHJ1ZSk7XG5cbiAgLy8gR3JvdXAgY291cnNlcyBieSBuYW1lIGFuZCBjb2RlLCBzaW1pbGFyIHRvIGFwcGx5IHBhZ2VcbiAgY29uc3QgZ3JvdXBlZENvdXJzZXMgPSB1c2VNZW1vKCgpID0+IHtcbiAgICByZXR1cm4gY291cnNlcy5yZWR1Y2U8UmVjb3JkPHN0cmluZywgQ291cnNlW10+PigoYWNjLCBjb3Vyc2UpID0+IHtcbiAgICAgIGNvbnN0IGdyb3VwTmFtZSA9IGAke2NvdXJzZS5uYW1lfSAoJHtjb3Vyc2UuY29kZX0pYDtcbiAgICAgIGlmICghYWNjW2dyb3VwTmFtZV0pIHtcbiAgICAgICAgYWNjW2dyb3VwTmFtZV0gPSBbXTtcbiAgICAgIH1cbiAgICAgIGFjY1tncm91cE5hbWVdLnB1c2goY291cnNlKTtcbiAgICAgIHJldHVybiBhY2M7XG4gICAgfSwge30pO1xuICB9LCBbY291cnNlc10pO1xuXG4gIGNvbnN0IGZldGNoUmVwb3J0RGF0YSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXVzZXIgfHwgIWF1dGguY3VycmVudFVzZXIpIHJldHVybjtcbiAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IGlkVG9rZW4gPSBhd2FpdCBhdXRoLmN1cnJlbnRVc2VyLmdldElkVG9rZW4oKTtcbiAgICAgIGNvbnN0IGZpbHRlcnMgPSB7XG4gICAgICAgIHN0YXJ0RGF0ZTogZGF0ZVJhbmdlPy5mcm9tID8gZm9ybWF0KGRhdGVSYW5nZS5mcm9tLCAneXl5eS1NTS1kZCcpIDogdW5kZWZpbmVkLFxuICAgICAgICBlbmREYXRlOiBkYXRlUmFuZ2U/LnRvID8gZm9ybWF0KGRhdGVSYW5nZS50bywgJ3l5eXktTU0tZGQnKSA6IHVuZGVmaW5lZCxcbiAgICAgICAgY291cnNlSWQ6IGNvdXJzZUlkIHx8IHVuZGVmaW5lZCxcbiAgICAgICAgc3RhdHVzOiBzdGF0dXMgfHwgdW5kZWZpbmVkLFxuICAgICAgfTtcbiAgICAgIGNvbnN0IFtkYXRhLCB0cmFuc2FjdGlvbnNdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBnZXRGaW5hbmNpYWxSZXBvcnREYXRhKGZpbHRlcnMsIGlkVG9rZW4pLFxuICAgICAgICBnZXRBbGxQYXltZW50VHJhbnNhY3Rpb25zKGlkVG9rZW4pXG4gICAgICBdKTtcbiAgICAgIHNldFJlcG9ydERhdGEoZGF0YSk7XG4gICAgICBzZXRQYXltZW50VHJhbnNhY3Rpb25zKHRyYW5zYWN0aW9ucyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogXCJDb3VsZCBub3QgZmV0Y2ggcmVwb3J0IGRhdGEuXCI7XG4gICAgICB0b2FzdCh7IHRpdGxlOiBcIkVycm9yXCIsIGRlc2NyaXB0aW9uOiBlcnJvck1lc3NhZ2UsIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIiB9KTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFt1c2VyLCBkYXRlUmFuZ2UsIGNvdXJzZUlkLCBzdGF0dXMsIHRvYXN0XSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBhc3luYyBmdW5jdGlvbiBmZXRjaEluaXRpYWxDb3Vyc2VzKCkge1xuICAgICAgdHJ5IHtcbiAgICAgICAgaWYgKCF1c2VyKSByZXR1cm47XG4gICAgICAgIGNvbnN0IGlkVG9rZW4gPSBhd2FpdCB1c2VyLmdldElkVG9rZW4oKTtcbiAgICAgICAgY29uc3QgZmV0Y2hlZENvdXJzZXMgPSBhd2FpdCBnZXRDb3Vyc2VzKGlkVG9rZW4pO1xuICAgICAgICBzZXRDb3Vyc2VzKGZldGNoZWRDb3Vyc2VzKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIHRvYXN0KHsgdGl0bGU6IFwiRXJyb3JcIiwgZGVzY3JpcHRpb246IFwiQ291bGQgbm90IGxvYWQgY291cnNlcyBmb3IgZmlsdGVyaW5nLlwiLCB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIgfSk7XG4gICAgICB9XG4gICAgfVxuICAgIGZldGNoSW5pdGlhbENvdXJzZXMoKTtcbiAgfSwgW3VzZXIsIHRvYXN0XSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWlzQXV0aExvYWRpbmcgJiYgdXNlcikge1xuICAgICAgICBmZXRjaFJlcG9ydERhdGEoKTtcbiAgICB9XG4gIH0sIFtmZXRjaFJlcG9ydERhdGEsIGlzQXV0aExvYWRpbmcsIHVzZXJdKTtcbiAgXG4gIGNvbnN0IGNsZWFyRmlsdGVycyA9ICgpID0+IHtcbiAgICBzZXREYXRlUmFuZ2UodW5kZWZpbmVkKTsgLy8gQ2xlYXIgZGF0ZSByYW5nZSB0byBzaG93IGFsbCByZWNvcmRzXG4gICAgc2V0Q291cnNlSWQoJycpO1xuICAgIHNldFN0YXR1cygnJyk7XG4gICAgLy8gRmV0Y2ggZGF0YSBpbW1lZGlhdGVseSBhZnRlciBjbGVhcmluZyBmaWx0ZXJzXG4gICAgc2V0VGltZW91dCgoKSA9PiBmZXRjaFJlcG9ydERhdGEoKSwgMTAwKTtcbiAgfTtcbiAgXG4gIGNvbnN0IHN1bW1hcnlTdGF0cyA9IHVzZU1lbW8oKCkgPT4ge1xuICAgIGNvbnN0IHRvdGFsRHVlID0gcmVwb3J0RGF0YS5yZWR1Y2UoKHN1bSwgZmVlKSA9PiBzdW0gKyBmZWUuYW1vdW50RHVlLCAwKTtcbiAgICBjb25zdCB0b3RhbFBhaWQgPSByZXBvcnREYXRhLnJlZHVjZSgoc3VtLCBmZWUpID0+IHN1bSArIGZlZS5hbW91bnRQYWlkLCAwKTtcbiAgICBjb25zdCB0b3RhbE91dHN0YW5kaW5nID0gdG90YWxEdWUgLSB0b3RhbFBhaWQ7XG4gICAgcmV0dXJuIHsgdG90YWxEdWUsIHRvdGFsUGFpZCwgdG90YWxPdXRzdGFuZGluZywgcmVjb3JkQ291bnQ6IHJlcG9ydERhdGEubGVuZ3RoIH07XG4gIH0sIFtyZXBvcnREYXRhXSk7XG5cbiAgY29uc3QgZXhwb3J0VG9DU1YgPSAoKSA9PiB7XG4gICAgIGlmIChyZXBvcnREYXRhLmxlbmd0aCA9PT0gMCkge1xuICAgICAgICB0b2FzdCh7IHRpdGxlOiBcIk5vIGRhdGEgdG8gZXhwb3J0XCIsIHZhcmlhbnQ6IFwiZGVmYXVsdFwiIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgfVxuICAgIGNvbnN0IGhlYWRlcnMgPSBbJ0lEJywgJ1N0dWRlbnQgTmFtZScsICdDb3Vyc2UgTmFtZScsICdGZWUgRGVzY3JpcHRpb24nLCAnQW1vdW50IER1ZScsICdBbW91bnQgUGFpZCcsICdSZW1haW5pbmcnLCAnRHVlIERhdGUnLCAnU3RhdHVzJywgJ0xhc3QgUGF5bWVudCBEYXRlJ107XG4gICAgY29uc3QgY3N2Um93cyA9IFtoZWFkZXJzLmpvaW4oJywnKV07XG5cbiAgICBmb3IgKGNvbnN0IGZlZSBvZiByZXBvcnREYXRhKSB7XG4gICAgICAgIGNvbnN0IHJlbWFpbmluZyA9IGZlZS5hbW91bnREdWUgLSBmZWUuYW1vdW50UGFpZDtcbiAgICAgICAgY29uc3QgdmFsdWVzID0gW1xuICAgICAgICAgICAgZmVlLmlkLFxuICAgICAgICAgICAgYFwiJHtmZWUuc3R1ZGVudE5hbWUucmVwbGFjZSgvXCIvZywgJ1wiXCInKX1cImAsXG4gICAgICAgICAgICBgXCIke2ZlZS5jb3Vyc2VOYW1lLnJlcGxhY2UoL1wiL2csICdcIlwiJyl9XCJgLFxuICAgICAgICAgICAgYFwiJHtmZWUuZmVlRGVzY3JpcHRpb24ucmVwbGFjZSgvXCIvZywgJ1wiXCInKX1cImAsXG4gICAgICAgICAgICBmZWUuYW1vdW50RHVlLnRvRml4ZWQoMiksXG4gICAgICAgICAgICBmZWUuYW1vdW50UGFpZC50b0ZpeGVkKDIpLFxuICAgICAgICAgICAgcmVtYWluaW5nLnRvRml4ZWQoMiksXG4gICAgICAgICAgICBmZWUuZHVlRGF0ZSxcbiAgICAgICAgICAgIGZlZS5zdGF0dXMsXG4gICAgICAgICAgICBmZWUubGFzdFBheW1lbnREYXRlIHx8ICcnXG4gICAgICAgIF07XG4gICAgICAgIGNzdlJvd3MucHVzaCh2YWx1ZXMuam9pbignLCcpKTtcbiAgICB9XG5cbiAgICBjb25zdCBjc3ZTdHJpbmcgPSBjc3ZSb3dzLmpvaW4oJ1xcbicpO1xuICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbY3N2U3RyaW5nXSwgeyB0eXBlOiAndGV4dC9jc3Y7Y2hhcnNldD11dGYtODsnIH0pO1xuICAgIGNvbnN0IHVybCA9IFVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYik7XG4gICAgY29uc3QgbGluayA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKTtcbiAgICBsaW5rLnNldEF0dHJpYnV0ZSgnaHJlZicsIHVybCk7XG4gICAgbGluay5zZXRBdHRyaWJ1dGUoJ2Rvd25sb2FkJywgYGZpbmFuY2lhbF9yZXBvcnRfJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc3BsaXQoJ1QnKVswXX0uY3N2YCk7XG4gICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChsaW5rKTtcbiAgICBsaW5rLmNsaWNrKCk7XG4gICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKTtcbiAgICBVUkwucmV2b2tlT2JqZWN0VVJMKHVybCk7XG4gIH07XG5cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1oZWFkbGluZSBmb250LWJvbGQgdHJhY2tpbmctdGlnaHQgdGV4dC1wcmltYXJ5XCI+RmluYW5jaWFsIFJlcG9ydHM8L2gxPlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+R2VuZXJhdGUgYW5kIHZpZXcgZmluYW5jaWFsIHJlcG9ydHMgYmFzZWQgb24gdmFyaW91cyBmaWx0ZXJzLjwvcD5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlJlcG9ydCBGaWx0ZXJzPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNCBpdGVtcy1lbmRcIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZ2FwLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5EdWUgRGF0ZSBSYW5nZTwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFBvcG92ZXJUcmlnZ2VyIGFzQ2hpbGQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PXtcIm91dGxpbmVcIn1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwianVzdGlmeS1zdGFydCB0ZXh0LWxlZnQgZm9udC1ub3JtYWxcIlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPENhbGVuZGFySWNvbiBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZGF0ZVJhbmdlPy5mcm9tID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0ZVJhbmdlLnRvID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGAke2Zvcm1hdChkYXRlUmFuZ2UuZnJvbSwgXCJMTEwgZGQsIHlcIil9IC0gJHtmb3JtYXQoZGF0ZVJhbmdlLnRvLCBcIkxMTCBkZCwgeVwiKX1gXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApIDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZvcm1hdChkYXRlUmFuZ2UuZnJvbSwgXCJMTEwgZGQsIHlcIilcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIClcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuPlBpY2sgYSBkYXRlPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9Qb3BvdmVyVHJpZ2dlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxQb3BvdmVyQ29udGVudCBjbGFzc05hbWU9XCJ3LWF1dG8gcC0wXCIgYWxpZ249XCJzdGFydFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxDYWxlbmRhciBtb2RlPVwicmFuZ2VcIiBzZWxlY3RlZD17ZGF0ZVJhbmdlfSBvblNlbGVjdD17c2V0RGF0ZVJhbmdlfSBpbml0aWFsRm9jdXMgLz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvUG9wb3ZlckNvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIDwvUG9wb3Zlcj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+Q291cnNlPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17Y291cnNlSWQgfHwgJ19hbGxfJ30gb25WYWx1ZUNoYW5nZT17KHZhbHVlKSA9PiBzZXRDb3Vyc2VJZCh2YWx1ZSA9PT0gJ19hbGxfJyA/ICcnIDogdmFsdWUpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyPjxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIkFsbCBDb3Vyc2VzXCIgLz48L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIl9hbGxfXCI+QWxsIENvdXJzZXM8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAge09iamVjdC5rZXlzKGdyb3VwZWRDb3Vyc2VzKS5sZW5ndGggPiAwID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgT2JqZWN0LmVudHJpZXMoZ3JvdXBlZENvdXJzZXMpLm1hcCgoW2dyb3VwTmFtZSwgY291cnNlc0luR3JvdXBdKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmIChjb3Vyc2VzSW5Hcm91cC5sZW5ndGggPT09IDEpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBjb3Vyc2UgPSBjb3Vyc2VzSW5Hcm91cFswXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXtjb3Vyc2UuaWR9IHZhbHVlPXtjb3Vyc2UuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Z3JvdXBOYW1lfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICk7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RHcm91cCBrZXk9e2dyb3VwTmFtZX0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RMYWJlbD57Z3JvdXBOYW1lfTwvU2VsZWN0TGFiZWw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtjb3Vyc2VzSW5Hcm91cC5tYXAoY291cnNlID0+IChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSBrZXk9e2NvdXJzZS5pZH0gdmFsdWU9e2NvdXJzZS5pZH0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7Y291cnNlLnNwZWNpYWxpemF0aW9ucz8uWzBdIHx8ICdHZW5lcmFsJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RHcm91cD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSB2YWx1ZT1cIm5vLWNvdXJzZXNcIiBkaXNhYmxlZD5ObyBjb3Vyc2VzIGF2YWlsYWJsZTwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlN0YXR1czwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxTZWxlY3QgdmFsdWU9e3N0YXR1cyB8fCAnX2FsbF8nfSBvblZhbHVlQ2hhbmdlPXsodmFsdWU6IHN0cmluZykgPT4gc2V0U3RhdHVzKHZhbHVlID09PSAnX2FsbF8nID8gJycgOiB2YWx1ZSBhcyBGZWVTdGF0dXMpfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxTZWxlY3RUcmlnZ2VyPjxTZWxlY3RWYWx1ZSBwbGFjZWhvbGRlcj1cIkFsbCBTdGF0dXNlc1wiIC8+PC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0gdmFsdWU9XCJfYWxsX1wiPkFsbCBTdGF0dXNlczwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB7ZmVlU3RhdHVzZXMubWFwKHMgPT4gPFNlbGVjdEl0ZW0ga2V5PXtzfSB2YWx1ZT17c30gY2xhc3NOYW1lPVwiY2FwaXRhbGl6ZVwiPntzfTwvU2VsZWN0SXRlbT4pfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2ZldGNoUmVwb3J0RGF0YX0gZGlzYWJsZWQ9e2lzTG9hZGluZ30gY2xhc3NOYW1lPVwidy1mdWxsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gPExvYWRlcjIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00IGFuaW1hdGUtc3BpblwiIC8+IDogJ0FwcGx5IEZpbHRlcnMnfVxuICAgICAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgIDxCdXR0b24gb25DbGljaz17Y2xlYXJGaWx0ZXJzfSBkaXNhYmxlZD17aXNMb2FkaW5nfSB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEZpbHRlclggY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJzci1vbmx5XCI+Q2xlYXIgRmlsdGVyczwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBnYXAtNCBtZDpncmlkLWNvbHMtM1wiPlxuICAgICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlRvdGFsIENvbGxlY3RlZDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICAgICAgICA8UGlnZ3lCYW5rIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gPFNrZWxldG9uIGNsYXNzTmFtZT1cImgtOCB3LTMyXCIvPiA6IDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkXCI+4oK5e3N1bW1hcnlTdGF0cy50b3RhbFBhaWQudG9GaXhlZCgyKX08L2Rpdj59XG4gICAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+VG90YWwgT3V0c3RhbmRpbmc8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgPENpcmNsZURvbGxhclNpZ24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC04IHctMzJcIi8+IDogPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1kZXN0cnVjdGl2ZVwiPuKCuXtzdW1tYXJ5U3RhdHMudG90YWxPdXRzdGFuZGluZy50b0ZpeGVkKDIpfTwvZGl2Pn1cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBzcGFjZS15LTAgcGItMlwiPlxuICAgICAgICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5GaWx0ZXJlZCBSZWNvcmRzPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgICAgIDxCYXJDaGFydEhvcml6b250YWwgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyA8U2tlbGV0b24gY2xhc3NOYW1lPVwiaC04IHctMTZcIi8+IDogPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57c3VtbWFyeVN0YXRzLnJlY29yZENvdW50fTwvZGl2Pn1cbiAgICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgICAgIDxDYXJkVGl0bGU+RmlsdGVyZWQgRmVlIFJlY29yZHM8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICAgICAgPENhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgICAgICAgQSBkZXRhaWxlZCBsaXN0IG9mIGZlZSByZWNvcmRzIG1hdGNoaW5nIHRoZSBzZWxlY3RlZCBmaWx0ZXJzLiBGb3VuZCB7cmVwb3J0RGF0YS5sZW5ndGh9IHJlY29yZHMuXG4gICAgICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICA8QnV0dG9uIG9uQ2xpY2s9e2V4cG9ydFRvQ1NWfSBkaXNhYmxlZD17aXNMb2FkaW5nIHx8IHJlcG9ydERhdGEubGVuZ3RoID09PSAwfSB2YXJpYW50PVwib3V0bGluZVwiPlxuICAgICAgICAgICAgICAgICAgICA8RG93bmxvYWQgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz4gRXhwb3J0IENTVlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge1suLi5BcnJheSg1KV0ubWFwKChfLCBpKSA9PiA8U2tlbGV0b24ga2V5PXtpfSBjbGFzc05hbWU9XCJoLTEwIHctZnVsbFwiIC8+KX1cbiAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiByZXBvcnREYXRhLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LW11dGVkLWZvcmVncm91bmQgcHktMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwPk5vIHJlY29yZHMgbWF0Y2ggdGhlIGN1cnJlbnQgZmlsdGVycy48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicm91bmRlZC1tZCBib3JkZXJcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZVJvdz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+U3R1ZGVudDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5Db3Vyc2U8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+RmVlIEl0ZW08L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+RHVlIERhdGU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+U3RhdHVzPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5BbW91bnQgRHVlPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5BbW91bnQgUGFpZDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkZXI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3JlcG9ydERhdGEubWFwKGZlZSA9PiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVSb3cga2V5PXtmZWUuaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57ZmVlLnN0dWRlbnROYW1lfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+e2ZlZS5jb3Vyc2VOYW1lfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+e2ZlZS5mZWVEZXNjcmlwdGlvbn08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPntmb3JtYXQobmV3IERhdGUoZmVlLmR1ZURhdGUpLCAnUFAnKX08L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPjxCYWRnZSB2YXJpYW50PXtzdGF0dXNWYXJpYW50TWFwW2ZlZS5zdGF0dXNdfSBjbGFzc05hbWU9XCJjYXBpdGFsaXplXCI+e2ZlZS5zdGF0dXN9PC9CYWRnZT48L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj7igrl7ZmVlLmFtb3VudER1ZS50b0ZpeGVkKDIpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tNzAwXCI+4oK5e2ZlZS5hbW91bnRQYWlkLnRvRml4ZWQoMil9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQm9keT5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGU+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIFBheW1lbnQgVHJhbnNhY3Rpb25zIFNlY3Rpb24gKi99XG4gICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4+UGF5bWVudCBUcmFuc2FjdGlvbiBIaXN0b3J5PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgPEJhZGdlIHZhcmlhbnQ9XCJvdXRsaW5lXCI+e3BheW1lbnRUcmFuc2FjdGlvbnMubGVuZ3RofSB0cmFuc2FjdGlvbnM8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Q2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgICAgICAgICBEZXRhaWxlZCBoaXN0b3J5IG9mIGFsbCBwYXltZW50IHRyYW5zYWN0aW9ucyByZWNvcmRlZCBpbiB0aGUgc3lzdGVtLlxuICAgICAgICAgICAgICAgIDwvQ2FyZERlc2NyaXB0aW9uPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICAgIHtpc0xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge1suLi5BcnJheSg1KV0ubWFwKChfLCBpKSA9PiA8U2tlbGV0b24ga2V5PXtpfSBjbGFzc05hbWU9XCJoLTEwIHctZnVsbFwiIC8+KX1cbiAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkgOiBwYXltZW50VHJhbnNhY3Rpb25zLmxlbmd0aCA9PT0gMCA/IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciB0ZXh0LW11dGVkLWZvcmVncm91bmQgcHktMTBcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwPk5vIHBheW1lbnQgdHJhbnNhY3Rpb25zIGZvdW5kLjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyb3VuZGVkLW1kIGJvcmRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5QYXltZW50IERhdGU8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+U3R1ZGVudDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5GZWUgSXRlbTwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD5QYXltZW50IE1ldGhvZDwvVGFibGVIZWFkPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZCBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+QW1vdW50PC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlRyYW5zYWN0aW9uIElEPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPlJlY29yZGVkIEJ5PC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPk5vdGVzPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZUhlYWRlcj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVCb2R5PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cGF5bWVudFRyYW5zYWN0aW9ucy5tYXAodHJhbnNhY3Rpb24gPT4gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPFRhYmxlUm93IGtleT17dHJhbnNhY3Rpb24uaWR9PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge2Zvcm1hdChuZXcgRGF0ZSh0cmFuc2FjdGlvbi5wYXltZW50RGF0ZSksICdQUCcpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+e3RyYW5zYWN0aW9uLnN0dWRlbnROYW1lfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHtyZXBvcnREYXRhLmZpbmQoZmVlID0+IGZlZS5pZCA9PT0gdHJhbnNhY3Rpb24uZmVlSWQpPy5mZWVEZXNjcmlwdGlvbiB8fCAnTi9BJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8QmFkZ2UgdmFyaWFudD1cIm91dGxpbmVcIj57dHJhbnNhY3Rpb24ucGF5bWVudE1ldGhvZH08L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwidGV4dC1yaWdodCBmb250LXNlbWlib2xkIHRleHQtZ3JlZW4tNzAwXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIOKCuXt0cmFuc2FjdGlvbi5hbW91bnQudG9GaXhlZCgyKX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFuc2FjdGlvbi50cmFuc2FjdGlvbklkIHx8ICctJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0cmFuc2FjdGlvbi5yZWNvcmRlZEJ5fVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGwgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAge3RyYW5zYWN0aW9uLm5vdGVzIHx8ICctJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVSb3c+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvVGFibGVCb2R5PlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UYWJsZT5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlTWVtbyIsInVzZUNhbGxiYWNrIiwiZ2V0RmluYW5jaWFsUmVwb3J0RGF0YSIsImdldEFsbFBheW1lbnRUcmFuc2FjdGlvbnMiLCJnZXRDb3Vyc2VzIiwidXNlQXV0aCIsImF1dGgiLCJ1c2VUb2FzdCIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJCdXR0b24iLCJQb3BvdmVyIiwiUG9wb3ZlckNvbnRlbnQiLCJQb3BvdmVyVHJpZ2dlciIsIkNhbGVuZGFyIiwiU2VsZWN0IiwiU2VsZWN0Q29udGVudCIsIlNlbGVjdEl0ZW0iLCJTZWxlY3RUcmlnZ2VyIiwiU2VsZWN0VmFsdWUiLCJTZWxlY3RHcm91cCIsIlNlbGVjdExhYmVsIiwiVGFibGUiLCJUYWJsZUJvZHkiLCJUYWJsZUNlbGwiLCJUYWJsZUhlYWQiLCJUYWJsZUhlYWRlciIsIlRhYmxlUm93IiwiQmFkZ2UiLCJTa2VsZXRvbiIsIkNhbGVuZGFySWNvbiIsIkxvYWRlcjIiLCJEb3dubG9hZCIsIkZpbHRlclgiLCJQaWdneUJhbmsiLCJDaXJjbGVEb2xsYXJTaWduIiwiQmFyQ2hhcnRIb3Jpem9udGFsIiwiZm9ybWF0IiwiZmVlU3RhdHVzZXMiLCJzdGF0dXNWYXJpYW50TWFwIiwiUGVuZGluZyIsIlBhaWQiLCJPdmVyZHVlIiwiRmluYW5jaWFsUmVwb3J0c1BhZ2UiLCJ1c2VyIiwiaXNMb2FkaW5nIiwiaXNBdXRoTG9hZGluZyIsInRvYXN0IiwiZGF0ZVJhbmdlIiwic2V0RGF0ZVJhbmdlIiwidW5kZWZpbmVkIiwiY291cnNlSWQiLCJzZXRDb3Vyc2VJZCIsInN0YXR1cyIsInNldFN0YXR1cyIsImNvdXJzZXMiLCJzZXRDb3Vyc2VzIiwicmVwb3J0RGF0YSIsInNldFJlcG9ydERhdGEiLCJwYXltZW50VHJhbnNhY3Rpb25zIiwic2V0UGF5bWVudFRyYW5zYWN0aW9ucyIsInNldElzTG9hZGluZyIsImdyb3VwZWRDb3Vyc2VzIiwicmVkdWNlIiwiYWNjIiwiY291cnNlIiwiZ3JvdXBOYW1lIiwibmFtZSIsImNvZGUiLCJwdXNoIiwiZmV0Y2hSZXBvcnREYXRhIiwiY3VycmVudFVzZXIiLCJpZFRva2VuIiwiZ2V0SWRUb2tlbiIsImZpbHRlcnMiLCJzdGFydERhdGUiLCJmcm9tIiwiZW5kRGF0ZSIsInRvIiwiZGF0YSIsInRyYW5zYWN0aW9ucyIsIlByb21pc2UiLCJhbGwiLCJlcnJvciIsImVycm9yTWVzc2FnZSIsIkVycm9yIiwibWVzc2FnZSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJ2YXJpYW50IiwiZmV0Y2hJbml0aWFsQ291cnNlcyIsImZldGNoZWRDb3Vyc2VzIiwiY2xlYXJGaWx0ZXJzIiwic2V0VGltZW91dCIsInN1bW1hcnlTdGF0cyIsInRvdGFsRHVlIiwic3VtIiwiZmVlIiwiYW1vdW50RHVlIiwidG90YWxQYWlkIiwiYW1vdW50UGFpZCIsInRvdGFsT3V0c3RhbmRpbmciLCJyZWNvcmRDb3VudCIsImxlbmd0aCIsImV4cG9ydFRvQ1NWIiwiaGVhZGVycyIsImNzdlJvd3MiLCJqb2luIiwicmVtYWluaW5nIiwidmFsdWVzIiwiaWQiLCJzdHVkZW50TmFtZSIsInJlcGxhY2UiLCJjb3Vyc2VOYW1lIiwiZmVlRGVzY3JpcHRpb24iLCJ0b0ZpeGVkIiwiZHVlRGF0ZSIsImxhc3RQYXltZW50RGF0ZSIsImNzdlN0cmluZyIsImJsb2IiLCJCbG9iIiwidHlwZSIsInVybCIsIlVSTCIsImNyZWF0ZU9iamVjdFVSTCIsImxpbmsiLCJkb2N1bWVudCIsImNyZWF0ZUVsZW1lbnQiLCJzZXRBdHRyaWJ1dGUiLCJEYXRlIiwidG9JU09TdHJpbmciLCJzcGxpdCIsImJvZHkiLCJhcHBlbmRDaGlsZCIsImNsaWNrIiwicmVtb3ZlQ2hpbGQiLCJyZXZva2VPYmplY3RVUkwiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsInAiLCJsYWJlbCIsImFzQ2hpbGQiLCJzcGFuIiwiYWxpZ24iLCJtb2RlIiwic2VsZWN0ZWQiLCJvblNlbGVjdCIsImluaXRpYWxGb2N1cyIsInZhbHVlIiwib25WYWx1ZUNoYW5nZSIsInBsYWNlaG9sZGVyIiwiT2JqZWN0Iiwia2V5cyIsImVudHJpZXMiLCJtYXAiLCJjb3Vyc2VzSW5Hcm91cCIsInNwZWNpYWxpemF0aW9ucyIsImRpc2FibGVkIiwicyIsIm9uQ2xpY2siLCJzaXplIiwiQXJyYXkiLCJfIiwiaSIsInRyYW5zYWN0aW9uIiwicGF5bWVudERhdGUiLCJmaW5kIiwiZmVlSWQiLCJwYXltZW50TWV0aG9kIiwiYW1vdW50IiwidHJhbnNhY3Rpb25JZCIsInJlY29yZGVkQnkiLCJub3RlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx\n"));

/***/ })

});