
'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { LayoutDashboard, BookOpen, Users, Bot, FileText, Settings, DollarSign, AreaChart, UserCheck, type LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useAuth } from '@/contexts/AuthContext';
import { SidebarMenu, SidebarMenuItem, SidebarMenuButton } from '@/components/ui/sidebar';

interface NavItem {
  href: string;
  label: string;
  icon: LucideIcon;
  roles: Array<'admin' | 'student' | 'accountant'>;
}

const navItems: NavItem[] = [
  { href: '/dashboard', label: 'Dashboard', icon: LayoutDashboard, roles: ['admin'] },
  { href: '/applications', label: 'Applications', icon: FileText, roles: ['admin', 'accountant'] },
  { href: '/students', label: 'Students', icon: User<PERSON>he<PERSON>, roles: ['admin', 'accountant']},
  { href: '/fee-management', label: 'Fee Management', icon: DollarSign, roles: ['admin', 'accountant'] }, 
  { href: '/financial-reports', label: 'Financial Reports', icon: AreaChart, roles: ['admin', 'accountant'] },
  { href: '/courses', label: 'Manage Courses', icon: BookOpen, roles: ['admin'] },
  { href: '/my-courses', label: 'My Courses', icon: BookOpen, roles: ['student'] },
  { href: '/my-fees', label: 'My Fees', icon: DollarSign, roles: ['student'] },
  { href: '/ai-assistant', label: 'AI Assistant', icon: Bot, roles: ['student'] },
  { href: '/settings', label: 'Settings', icon: Settings, roles: ['admin', 'student', 'accountant'] },
];

export function NavLinks() {
  const pathname = usePathname();
  const { user } = useAuth();

  if (!user) return null;

  const filteredNavItems = navItems.filter(item => item.roles.includes(user.role));

  return (
    <SidebarMenu>
      {filteredNavItems.map((item) => {
        const isActive = pathname === item.href || (item.href !== '/' && pathname.startsWith(item.href));
        return (
          <SidebarMenuItem key={item.href}>
            <Link href={item.href}>
              <SidebarMenuButton
                isActive={isActive}
                className={cn(
                  'w-full justify-start',
                  isActive && 'bg-sidebar-accent text-sidebar-accent-foreground',
                )}
                tooltip={item.label}
              >
                <item.icon className="h-5 w-5" />
                <span className="truncate">{item.label}</span>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        );
      })}
    </SidebarMenu>
  );
}
