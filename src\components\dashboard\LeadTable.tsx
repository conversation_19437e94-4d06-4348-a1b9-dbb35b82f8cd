
'use client';

import React, { useState } from 'react';
import type { Course, Lead } from '@/types';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Eye, Edit2, Trash2, MoreHorizontal, Loader2, FileText } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { LeadDialog } from '@/components/leads/LeadDialog';
import { useToast } from '@/hooks/use-toast';
import { updateLead, deleteLead } from '@/actions/leadActions';
import { auth } from '@/lib/firebase';
import { Skeleton } from '@/components/ui/skeleton';

const statusVariantMap: Record<Lead['status'], 'default' | 'secondary' | 'outline' | 'destructive'> = {
  New: 'default',
  Contacted: 'secondary',
  Qualified: 'outline',
  Lost: 'destructive',
};

interface LeadTableProps {
  leads: Lead[];
  totalLeadsCount: number;
  courses: Course[];
  isLoading: boolean;
  onLeadUpdated: () => void;
  onLeadDeleted: () => void;
}

export const LeadTable = React.memo(function LeadTable({ leads, totalLeadsCount, courses, isLoading, onLeadUpdated, onLeadDeleted }: LeadTableProps) {
  const { toast } = useToast();
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [isLeadDialogOpen, setIsLeadDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [leadToDelete, setLeadToDelete] = useState<Lead | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);


  const handleEditLead = (lead: Lead) => {
    setSelectedLead(lead);
    setIsLeadDialogOpen(true);
  };

  const handleSaveLead = async (leadData: Lead | Omit<Lead, 'id' | 'inquiryDate' | 'status'>) => {
    if ('id' in leadData) { // Editing existing lead
      try {
        const idToken = await auth.currentUser?.getIdToken();
        if (!idToken) throw new Error("Authentication token not available.");
        const result = await updateLead(leadData as Lead, idToken);
        if (result) {
          onLeadUpdated(); // Trigger refresh on dashboard
        } else {
          throw new Error("Failed to update lead");
        }
      } catch (error) {
        console.error("Error updating lead from LeadTable:", error);
        toast({
          title: "Error",
          description: "Could not update lead. Please try again.",
          variant: "destructive",
        });
      }
    }
    // Creation is handled by the dashboard page directly
  };
  
  const openDeleteConfirmDialog = (lead: Lead) => {
    setLeadToDelete(lead);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirmed = async () => {
    if (!leadToDelete) return;
    setIsSubmitting(true);
    try {
      const idToken = await auth.currentUser?.getIdToken();
      if (!idToken) throw new Error("Authentication token not available.");
      const result = await deleteLead(leadToDelete.id, idToken);
      if (result.success) {
        toast({
          title: "Lead Deleted",
          description: `Lead "${leadToDelete.name}" has been successfully deleted.`,
        });
        onLeadDeleted(); // Trigger refresh
      } else {
        throw new Error("Deletion failed on server.");
      }
    } catch (error) {
      console.error("Failed to delete lead:", error);
      toast({
        title: "Error",
        description: "Could not delete lead. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
      setIsDeleteDialogOpen(false);
      setLeadToDelete(null);
    }
  };

  const getCourseName = (courseId?: string) => {
    if (!courseId) return <span className="text-muted-foreground italic">Not specified</span>;
    return courses.find(c => c.id === courseId)?.name || 'Unknown Course';
  };

  if (isLoading) {
    return (
      <div className="rounded-lg border shadow-sm overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead><Skeleton className="h-5 w-24" /></TableHead>
              <TableHead><Skeleton className="h-5 w-32" /></TableHead>
              <TableHead><Skeleton className="h-5 w-40" /></TableHead>
              <TableHead><Skeleton className="h-5 w-28" /></TableHead>
              <TableHead><Skeleton className="h-5 w-24" /></TableHead>
              <TableHead><Skeleton className="h-5 w-20" /></TableHead>
              <TableHead className="text-right"><Skeleton className="h-5 w-16" /></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[...Array(5)].map((_, i) => (
              <TableRow key={i}>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                <TableCell><Skeleton className="h-5 w-28" /></TableCell>
                <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                <TableCell><Skeleton className="h-6 w-20 rounded-full" /></TableCell>
                <TableCell className="text-right"><Skeleton className="h-8 w-8" /></TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    );
  }

  return (
    <>
      {leads.length > 0 ? (
        <div className="rounded-lg border shadow-sm overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Desired Course</TableHead>
                <TableHead>Inquiry Date</TableHead>
                <TableHead>Source</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {leads.map((lead) => (
                <TableRow key={lead.id}>
                  <TableCell className="font-medium">{lead.name}</TableCell>
                  <TableCell>{lead.email}</TableCell>
                  <TableCell>{getCourseName(lead.desiredCourse)}</TableCell>
                  <TableCell>{lead.inquiryDate}</TableCell>
                  <TableCell>{lead.source}</TableCell>
                  <TableCell>
                    <Badge variant={statusVariantMap[lead.status] || 'default'}>{lead.status}</Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => alert('View Details - Not implemented yet.')}>
                          <Eye className="mr-2 h-4 w-4" /> View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditLead(lead)}>
                          <Edit2 className="mr-2 h-4 w-4" /> Edit Lead
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => openDeleteConfirmDialog(lead)} 
                          className="text-destructive focus:text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" /> Delete Lead
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center text-muted-foreground border-2 border-dashed rounded-lg p-10">
            <FileText className="mx-auto h-10 w-10 mb-3" />
            <p className="font-semibold text-lg">
                {totalLeadsCount > 0 ? 'No Leads Match Filter' : 'No Leads Found'}
            </p>
            <p className="text-sm mt-1">
                {totalLeadsCount > 0 ? 'Try selecting a different status.' : 'Add a new lead to get started.'}
            </p>
        </div>
      )}

      {selectedLead && (
        <LeadDialog
          lead={selectedLead}
          courses={courses}
          open={isLeadDialogOpen && selectedLead?.id !== undefined} // only open for edit if lead is selected
          onOpenChange={setIsLeadDialogOpen}
          onSave={handleSaveLead}
          formType="edit"
        />
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the lead "{leadToDelete?.name}".
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirmed} className="bg-destructive hover:bg-destructive/90" disabled={isSubmitting}>
              {isSubmitting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
});
