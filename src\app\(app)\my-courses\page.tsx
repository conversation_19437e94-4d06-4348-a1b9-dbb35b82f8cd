
'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BookOpenText, Loader2, Info, CalendarDays } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { getApplications } from '@/actions/applicationActions';
import { getCourses } from '@/actions/courseActions';
import { getStudentProgressForAllCourses } from '@/actions/progressActions';
import type { Course, StudentApplication, StudentCourseProgress } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { auth } from '@/lib/firebase';
import { Skeleton } from '@/components/ui/skeleton';

interface EnrolledCourse extends Course {
  progress: number;
}

export default function MyCoursesPage() {
  const { user, isLoading: authLoading } = useAuth();
  const [enrolledCourses, setEnrolledCourses] = useState<EnrolledCourse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  const fetchEnrolledCourses = useCallback(async () => {
    if (!user || user.role !== 'student') {
      setIsLoading(false);
      setEnrolledCourses([]);
      return;
    }
    setIsLoading(true);
    try {
      if (!auth.currentUser) throw new Error("Authentication required.");
      const idToken = await auth.currentUser.getIdToken();

      const [applications, allCourses, allProgressData] = await Promise.all([
        getApplications(idToken),
        getCourses(),
        getStudentProgressForAllCourses(user.uid, idToken)
      ]);

      const userAcceptedApplications = applications.filter(
        app => app.status === 'Accepted'
      );
      
      const coursesMap = new Map(allCourses.map(course => [course.id, course]));
      const progressMap = new Map(allProgressData.map(p => [p.courseId, p]));
      
      const currentEnrolledCourses = userAcceptedApplications.reduce((acc, app) => {
        const course = coursesMap.get(app.desiredCourse);
        if (course) {
          // Calculate unit-based progress
          const totalUnits = course.semesters.reduce((semTotal, sem) => 
            semTotal + sem.subjects.reduce((subTotal, sub) => 
                subTotal + (sub.units?.length || 0), 0), 0);
          
          const courseProgress = progressMap.get(course.id);
          const completedUnitsCount = courseProgress?.completedUnits?.length || 0;
          const progressPercentage = totalUnits > 0 ? Math.round((completedUnitsCount / totalUnits) * 100) : 0;

          acc.push({
            ...course,
            progress: progressPercentage,
          });
        }
        return acc;
      }, [] as EnrolledCourse[]);

      setEnrolledCourses(currentEnrolledCourses);

    } catch (error) {
      console.error("Failed to fetch enrolled courses:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Could not fetch your course data. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user, toast]);

  useEffect(() => {
    if (!authLoading) { 
      fetchEnrolledCourses();
    }
  }, [authLoading, fetchEnrolledCourses]);

  if (isLoading || authLoading) {
    return (
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">My Enrolled Courses</h1>
          <p className="text-muted-foreground">Track your academic progress and access course materials.</p>
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="shadow-lg overflow-hidden flex flex-col">
              <Skeleton className="h-48 w-full" />
              <CardContent className="p-4 flex-grow space-y-3">
                <Skeleton className="h-4 w-3/5" />
                <Skeleton className="h-4 w-4/5" />
                <div className="space-y-2 pt-2">
                  <div className="flex justify-between"><Skeleton className="h-4 w-1/4" /><Skeleton className="h-4 w-1/4" /></div>
                  <Skeleton className="h-2 w-full" />
                </div>
              </CardContent>
              <CardFooter className="p-4 border-t">
                <Skeleton className="h-9 w-full rounded-md" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }
  
  if (!user || user.role !== 'student') {
     return (
        <div className="space-y-6 text-center py-10">
            <Info className="mx-auto h-12 w-12 text-primary" />
            <h1 className="text-2xl font-headline font-bold text-primary">Access Denied</h1>
            <p className="text-muted-foreground">This page is for enrolled students only.</p>
        </div>
     );
  }


  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">My Enrolled Courses</h1>
        <p className="text-muted-foreground">Track your academic progress and access course materials.</p>
      </div>

      {enrolledCourses.length === 0 ? (
         <Card className="shadow-lg">
          <CardHeader>
            <CardTitle className="font-headline text-xl">No Courses Enrolled</CardTitle>
          </CardHeader>
          <CardContent className="text-center text-muted-foreground py-10">
            <BookOpenText className="mx-auto h-16 w-16 mb-4 text-primary" />
            <p className="text-lg">You are not currently enrolled in any courses.</p>
            <p>If you have recently applied, please wait for your application to be accepted.</p>
            <Button asChild className="mt-4 bg-accent hover:bg-accent/90 text-accent-foreground">
              <Link href="/apply">Apply for a Course</Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {enrolledCourses.map(course => (
            <Card key={course.id} className="shadow-lg hover:shadow-xl transition-shadow duration-300 overflow-hidden flex flex-col">
              <Link href={`/my-courses/${course.id}`} className="block group">
                <div className="relative h-48 w-full">
                   <Image 
                      src={`https://placehold.co/600x240.png`} 
                      alt={course.name} 
                      layout="fill" 
                      objectFit="cover"
                      data-ai-hint="education online class"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 p-4">
                      <h2 className="text-xl font-headline font-semibold text-white group-hover:underline">{course.name}</h2>
                      <p className="text-sm text-gray-200">{course.code}</p>
                    </div>
                </div>
              </Link>
              <CardContent className="p-4 flex-grow space-y-3">
                <p className="text-sm text-muted-foreground">Instructor: {course.instructor}</p>
                 {(course.startDate || course.endDate) && (
                  <div className="space-y-1 text-xs text-muted-foreground">
                    {course.startDate && (
                      <div className="flex items-center">
                        <CalendarDays className="h-3.5 w-3.5 mr-1.5 text-accent" />
                        <span>Starts: {format(new Date(course.startDate), "MMM d, yyyy")}</span>
                      </div>
                    )}
                    {course.endDate && (
                      <div className="flex items-center">
                        <CalendarDays className="h-3.5 w-3.5 mr-1.5 text-accent" />
                        <span>Ends: {format(new Date(course.endDate), "MMM d, yyyy")}</span>
                      </div>
                    )}
                  </div>
                )}
                {course.progress !== undefined && (
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="font-medium text-muted-foreground">Progress</span>
                      <span className="font-semibold text-primary">{course.progress}%</span>
                    </div>
                    <Progress value={course.progress} className="h-2" />
                  </div>
                )}
              </CardContent>
              <CardFooter className="p-4 border-t">
                <Button asChild variant="outline" className="w-full">
                    <Link href={`/my-courses/${course.id}`}>View Course Details</Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}
