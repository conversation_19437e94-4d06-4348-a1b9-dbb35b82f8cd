"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/layout",{

/***/ "(app-pages-browser)/./src/components/navigation/UserProfileSection.tsx":
/*!**********************************************************!*\
  !*** ./src/components/navigation/UserProfileSection.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProfileSection: () => (/* binding */ UserProfileSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronUp,LayoutDashboard,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronUp,LayoutDashboard,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronUp,LayoutDashboard,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/layout-dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronUp,LayoutDashboard,LogOut,Settings!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_notifications_NotificationBell__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/notifications/NotificationBell */ \"(app-pages-browser)/./src/components/notifications/NotificationBell.tsx\");\n/* __next_internal_client_entry_do_not_use__ UserProfileSection auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction UserProfileSection() {\n    _s();\n    const { user, logout } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    if (!user) return null;\n    const getInitials = (name)=>{\n        if (!name) return 'U';\n        return name.split(' ').map((n)=>n[0]).join('').toUpperCase();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenuItem, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 px-2 py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_notifications_NotificationBell__WEBPACK_IMPORTED_MODULE_6__.NotificationBell, {}, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm\",\n                            children: \"Notifications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenuItem, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_5__.SidebarMenuButton, {\n                                size: \"lg\",\n                                className: \"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                        className: \"h-8 w-8 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarImage, {\n                                                src: \"https://placehold.co/100x100.png?text=\".concat(getInitials(user.name)),\n                                                alt: user.name || 'User'\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 49,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                className: \"rounded-lg\",\n                                                children: getInitials(user.name)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 53,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid flex-1 text-left text-sm leading-tight\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate font-semibold\",\n                                                children: user.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 58,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"truncate text-xs text-muted-foreground\",\n                                                children: user.email\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"ml-auto size-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                            className: \"w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg\",\n                            side: \"bottom\",\n                            align: \"end\",\n                            sideOffset: 4,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuLabel, {\n                                    className: \"p-0 font-normal\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 px-1 py-1.5 text-left text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.Avatar, {\n                                                className: \"h-8 w-8 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarImage, {\n                                                        src: \"https://placehold.co/100x100.png?text=\".concat(getInitials(user.name)),\n                                                        alt: user.name || 'User'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 75,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_3__.AvatarFallback, {\n                                                        className: \"rounded-lg\",\n                                                        children: getInitials(user.name)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 79,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 74,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid flex-1 text-left text-sm leading-tight\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"truncate font-semibold\",\n                                                        children: user.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 84,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"truncate text-xs text-muted-foreground\",\n                                                        children: user.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                        lineNumber: 85,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/settings\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 94,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Settings\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, this),\n                                user.role === 'admin' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/dashboard\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 101,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Admin Panel\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                                lineNumber: 102,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    onClick: logout,\n                                    className: \"text-destructive focus:bg-destructive/10 focus:text-destructive\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronUp_LayoutDashboard_LogOut_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Log out\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                    lineNumber: 42,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n                lineNumber: 41,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\components\\\\navigation\\\\UserProfileSection.tsx\",\n        lineNumber: 34,\n        columnNumber: 5\n    }, this);\n}\n_s(UserProfileSection, \"SlSPRKmTohGnoLiiApupaRii2Oc=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = UserProfileSection;\nvar _c;\n$RefreshReg$(_c, \"UserProfileSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/navigation/UserProfileSection.tsx\n"));

/***/ })

});