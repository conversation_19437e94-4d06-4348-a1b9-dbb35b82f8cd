# **App Name**: CampusFlow

## Core Features:

- Lead Management Dashboard: Centralized dashboard for admin to view student inquiries and manage leads.
- Online Application Form: Form to gather essential student data. Dynamically creates student records.
- User Authentication: Secure login portal for admins and students.  Role-based access control.
- Course Management: Catalog for admins to define courses, their curriculum, and instructors.
- Course Enrolment Management: Automate student enrolment with status notifications and track their academic progress
- Fee Management System: Basic system for tracking payment amounts, without complex payment scheduling or invoice generation.
- AI assistant: An AI tool assists in answering frequently asked questions from the dashboard about student support and other information about the institution.

## Style Guidelines:

- Primary color: Deep Blue (#3F51B5) to evoke trust and reliability.
- Background color: Light Gray (#ECEFF1), providing a clean and modern backdrop.
- Accent color: Teal (#009688) to draw attention to key interactive elements.
- Headline font: 'Space Grotesk' sans-serif for titles. Body font: 'Inter' sans-serif for content. 
- Consistent set of simple, line-based icons to aid navigation.
- Clean, grid-based layout for organizing information. Ample white space.
- Subtle transitions and animations on navigation and form submissions.