
'use client';

import { ApplicationForm } from '@/components/apply/ApplicationForm';
import { getCourses } from '@/actions/courseActions';
import { getSystemSettings } from '@/actions/settingsActions';
import type { Course, SystemSettings } from '@/types';
import { AlertCircle, Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export function ApplyPageClient() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const [coursesData, settingsData] = await Promise.all([
          getCourses(),
          getSystemSettings(),
        ]);
        setCourses(coursesData);
        setSettings(settingsData);
      } catch (e: any) {
        setError(e.message || "Failed to load application data.");
      } finally {
        setIsLoading(false);
      }
    }
    fetchData();
  }, []);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  if (error) {
     return (
        <div className="text-center py-10 text-destructive">
            <AlertCircle className="mx-auto h-10 w-10 mb-2" />
            <p className="font-semibold">Could not load application form</p>
            <p className="text-sm">{error}</p>
        </div>
    );
  }

  if (settings && !settings.applicationsOpen) {
    return (
      <div className="text-center py-10 text-muted-foreground">
        <AlertCircle className="mx-auto h-10 w-10 mb-2 text-primary" />
        <p className="font-semibold">Applications Currently Closed</p>
        <p className="text-sm">We are not accepting new applications at this time. Please check back later.</p>
      </div>
    );
  }

  return <ApplicationForm courses={courses} />;
}
