
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import type { FeeItem } from '@/types';
import React from 'react';

const feeItemFormSchema = z.object({
  name: z.string().min(3, { message: 'Fee item name must be at least 3 characters.' }),
  tuitionFee: z.coerce.number().min(0, 'Must be a positive number').default(0),
  examFee: z.coerce.number().min(0, 'Must be a positive number').default(0),
  otherFee: z.coerce.number().min(0, 'Must be a positive number').default(0),
}).refine(data => (data.tuitionFee || 0) + (data.examFee || 0) + (data.otherFee || 0) > 0, {
  message: "At least one fee amount must be greater than zero.",
  path: ['tuitionFee'], // Attach error to the first amount field
});


type FeeItemFormValues = z.infer<typeof feeItemFormSchema>;

interface FeeItemDialogProps {
  feeItem?: FeeItem | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSave: (feeItemData: Omit<FeeItem, 'id'> | FeeItem) => void;
}

export function FeeItemDialog({ feeItem, open, onOpenChange, onSave }: FeeItemDialogProps) {
  const form = useForm<FeeItemFormValues>({
    resolver: zodResolver(feeItemFormSchema),
    defaultValues: feeItem
      ? { name: feeItem.name, tuitionFee: feeItem.tuitionFee, examFee: feeItem.examFee, otherFee: feeItem.otherFee }
      : { name: '', tuitionFee: 0, examFee: 0, otherFee: 0 },
  });

  React.useEffect(() => {
    if (open) {
      if (feeItem) {
        form.reset({ name: feeItem.name, tuitionFee: feeItem.tuitionFee || 0, examFee: feeItem.examFee || 0, otherFee: feeItem.otherFee || 0 });
      } else {
        form.reset({ name: '', tuitionFee: 0, examFee: 0, otherFee: 0 });
      }
    }
  }, [feeItem, form, open]);

  function onSubmit(data: FeeItemFormValues) {
    if (feeItem) {
      onSave({ ...feeItem, ...data });
    } else {
      onSave(data);
    }
    onOpenChange(false);
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="font-headline text-xl">{feeItem ? 'Edit Fee Item' : 'Add New Fee Item'}</DialogTitle>
          <DialogDescription>
            {feeItem ? 'Update the details for this fee item.' : 'Fill in the fee components for this new item.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Fee Item Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Semester 1 Fees" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
             <FormField
              control={form.control}
              name="tuitionFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tuition Fee (₹)</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" placeholder="0.00" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
             <FormField
              control={form.control}
              name="examFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Exam Fee (₹)</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" placeholder="0.00" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
             <FormField
              control={form.control}
              name="otherFee"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Other Fee (₹)</FormLabel>
                  <FormControl>
                    <Input type="number" step="0.01" placeholder="0.00" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">Cancel</Button>
              </DialogClose>
              <Button type="submit" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                {feeItem ? 'Save Changes' : 'Add Item'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
