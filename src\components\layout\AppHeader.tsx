
'use client';

import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Menu } from 'lucide-react';
import { Logo } from '@/components/navigation/Logo';
import { useSidebar } from '@/components/ui/sidebar';
import { NotificationBell } from '@/components/notifications/NotificationBell';

export function AppHeader() {
  const { user } = useAuth();
  const { toggleSidebar, isMobile } = useSidebar();

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-card shadow-sm no-print">
      <div className="container mx-auto flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
        <div className="flex items-center gap-4">
          {isMobile && (
             <Button variant="ghost" size="icon" onClick={toggleSidebar} aria-label="Toggle sidebar">
               <Menu className="h-6 w-6" />
             </Button>
          )}
          {/* Only show logo on mobile when sidebar is collapsed */}
          {isMobile && <Logo size="md" />}
        </div>

        <div className="flex items-center gap-2">
          {user && <NotificationBell />}
        </div>
      </div>
    </header>
  );
}
