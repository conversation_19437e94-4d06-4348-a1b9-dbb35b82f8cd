'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Printer } from 'lucide-react';

export function InvoiceActions() {
  const handlePrint = () => {
 window.print();
  };

  return (
    <div className="no-print flex justify-end mt-8">
 <Button onClick={handlePrint} variant="outline" className="bg-accent hover:bg-accent/90 text-accent-foreground"><Printer className="mr-2 h-4 w-4" /> Print Receipt</Button>
    </div>
  );
}
