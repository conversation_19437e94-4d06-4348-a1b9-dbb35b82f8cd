'use server';
/**
 * @fileOverview Genkit tool to retrieve fee information for a specific course.
 */
import { ai } from '@/ai/genkit';
import { getCourses } from '@/actions/courseActions';
import { getPublicCourseFeeStructure } from '@/actions/publicActions';
import { z } from 'zod';

export const getCourseFeeInfoTool = ai.defineTool(
  {
    name: 'getCourseFeeInfo',
    description: 'Get the fee structure and total cost for a specific course. Use this if the user asks about fees, cost, or price of a course.',
    inputSchema: z.object({
      courseIdentifier: z.string().describe('The name or code of the course, e.g., "Bachelor of Computer Applications" or "BCA".'),
    }),
    outputSchema: z.union([
        z.object({
            courseName: z.string(),
            totalFees: z.number(),
            feeItems: z.array(z.object({
                name: z.string(),
                amount: z.number(),
            }))
        }),
        z.object({error: z.string()})
    ]),
  },
  async ({ courseIdentifier }) => {
    try {
        const allCourses = await getCourses();
        const normalizedIdentifier = courseIdentifier.toLowerCase().trim();
        
        const course = allCourses.find(c => 
            c.name.toLowerCase().includes(normalizedIdentifier) || 
            c.code.toLowerCase() === normalizedIdentifier
        );

        if (!course) {
          return { error: `Course "${courseIdentifier}" not found.` };
        }

        const feeStructure = await getPublicCourseFeeStructure(course.id);

        if (!feeStructure || !feeStructure.items || feeStructure.items.length === 0) {
            return { error: `No fee structure found for "${course.name}".` };
        }

        const feeItems = feeStructure.items.map(item => ({ 
            name: item.name, 
            amount: (item.tuitionFee || 0) + (item.examFee || 0) + (item.otherFee || 0)
        }));
        const totalFees = feeItems.reduce((sum, item) => sum + item.amount, 0);

        return { courseName: course.name, totalFees, feeItems };
    } catch (e: any) {
        console.error("Error in getCourseFeeInfoTool:", e);
        return { error: `An internal error occurred while fetching fee information.` };
    }
  }
);
