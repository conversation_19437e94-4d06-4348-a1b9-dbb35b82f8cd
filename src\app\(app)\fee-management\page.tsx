
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import type { StudentFee, FeeStatus, User } from '@/types';
import { getStudentFees, updateFeeStatus, sendOverdueFeeReminders, deleteStudentFee, recordManualPayment } from '@/actions/feeActions';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Edit, MoreHorizontal, CheckCircle, XCircle, AlertCircle, Clock, FileText, Filter, MailWarning, Download, Trash2 } from 'lucide-react';
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogFooter,
    DialogClose,
  } from "@/components/ui/dialog";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import { type DropdownMenuCheckboxItemProps } from "@radix-ui/react-dropdown-menu";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format } from 'date-fns';
import { auth } from '@/lib/firebase';
import { useAuth } from '@/contexts/AuthContext';
import { Skeleton } from '@/components/ui/skeleton';

type Checked = DropdownMenuCheckboxItemProps["checked"];

const statusVariantMap: Record<FeeStatus, 'default' | 'secondary' | 'outline' | 'destructive'> = {
  Pending: 'default',
  'Partially Paid': 'secondary',
  Paid: 'outline',
  Overdue: 'destructive',
};

const statusIconMap: Record<FeeStatus, React.ElementType> = {
    Pending: Clock,
    'Partially Paid': AlertCircle,
    Paid: CheckCircle,
    Overdue: XCircle,
};

const RupeeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M6 3h12" /><path d="M6 8h12" /><path d="M6 13h12" /><path d="M6 18h12" /><path d="M6 3v18" /><path d="M18 3v18" /><path d="M9 3v18" /><path d="M15 3v18" /><path d="M9.5 8C7.015 8 5 10.015 5 12.5S7.015 17 9.5 17h5c2.485 0 4.5-2.015 4.5-4.5S16.985 8 14.5 8z" transform="matrix(1 0 0 1 -0.5 -1)"/><path d="M6 3h12"></path><path d="M6 8h12"></path><path d="M9.5 13H18"></path></svg>
);


export default function FeeManagementPage() {
  const [fees, setFees] = useState<StudentFee[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSendingReminders, setIsSendingReminders] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [feeToDelete, setFeeToDelete] = useState<StudentFee | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [filterStatus, setFilterStatus] = useState<Record<FeeStatus, Checked>>({
    Pending: true,
    'Partially Paid': true,
    Paid: true,
    Overdue: true,
  });

  const { toast } = useToast();
  const { user } = useAuth();

  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [feeToPay, setFeeToPay] = useState<StudentFee | null>(null);
  const [paymentAmount, setPaymentAmount] = useState('');
  const [isSubmittingPayment, setIsSubmittingPayment] = useState(false);

  const fetchFees = useCallback(async () => {
    setIsLoading(true);
    try {
      if (!auth.currentUser) throw new Error("Authentication required.");
      const idToken = await auth.currentUser.getIdToken();
      const fetchedFees = await getStudentFees(idToken);
      setFees(fetchedFees);
    } catch (error) {
      console.error("Failed to fetch student fees:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Could not fetch student fees. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchFees();
  }, [fetchFees]);

  const handleUpdateStatus = async (feeId: string, newStatus: FeeStatus) => {
    try {
      if (!auth.currentUser) throw new Error("Authentication required.");
      const idToken = await auth.currentUser.getIdToken();
      const updated = await updateFeeStatus(feeId, newStatus, idToken);
      if (updated) {
        toast({ title: "Status Updated", description: `Fee status for ${updated.studentName} changed to ${newStatus}.` });
        fetchFees();
      } else {
        throw new Error("Failed to update status.");
      }
    } catch (error) {
      console.error("Failed to update status:", error);
      toast({ title: "Error", description: error instanceof Error ? error.message : "Could not update fee status.", variant: "destructive" });
    }
  };
  
  const handleSendReminders = async () => {
    setIsSendingReminders(true);
    try {
        if (!auth.currentUser) throw new Error("Authentication required.");
        const idToken = await auth.currentUser.getIdToken();
        const result = await sendOverdueFeeReminders(idToken);
        toast({
            title: "Reminders Sent",
            description: `Successfully processed and sent ${result.count} overdue fee reminders. The table has been updated.`
        });
        fetchFees();
    } catch(error) {
        toast({ title: "Error", description: error instanceof Error ? error.message : "Could not send reminders.", variant: "destructive" });
    } finally {
        setIsSendingReminders(false);
    }
  };

  const openPaymentDialog = (fee: StudentFee) => {
    setFeeToPay(fee);
    setPaymentAmount(''); // Reset amount for new payment
    setIsPaymentDialogOpen(true);
  };

  const handleRecordPayment = async () => {
    if (!feeToPay || !paymentAmount || isNaN(parseFloat(paymentAmount))) {
      toast({ title: "Invalid Input", description: "Please enter a valid payment amount.", variant: "destructive" });
      return;
    }

    const amount = parseFloat(paymentAmount);
    if (amount <= 0) {
      toast({ title: "Invalid Amount", description: "Payment amount must be positive.", variant: "destructive" });
      return;
    }

    setIsSubmittingPayment(true);
    try {
      if (!auth.currentUser) throw new Error("Authentication required.");
      const idToken = await auth.currentUser.getIdToken();
      await recordManualPayment({ feeId: feeToPay.id, amountPaid: amount, paymentDate: format(new Date(), 'yyyy-MM-dd') }, idToken);
      toast({ title: "Payment Recorded", description: `₹${amount.toFixed(2)} recorded for ${feeToPay.studentName}.` });
      fetchFees(); // Refresh the list
      setIsPaymentDialogOpen(false);
    } catch (error) {
      toast({ title: "Error", description: error instanceof Error ? error.message : "Failed to record payment.", variant: "destructive" });
    } finally { setIsSubmittingPayment(false); }
  };

  const exportFeesToCSV = () => {
    if (fees.length === 0) {
        toast({ title: "No fee records to export", variant: "default" });
        return;
    }

    const headers = ['ID', 'Student Name', 'Student ID', 'Course Name', 'Fee Description', 'Amount Due', 'Amount Paid', 'Remaining', 'Due Date', 'Status', 'Last Payment Date', 'Final Payment Date'];
    const csvRows = [headers.join(',')];

    for (const fee of fees) {
        const remaining = fee.amountDue - fee.amountPaid;
        const values = [
            fee.id,
            `"${fee.studentName.replace(/"/g, '""')}"`,
            `"${fee.studentId}"`,
            `"${fee.courseName.replace(/"/g, '""')}"`,
            `"${fee.feeDescription.replace(/"/g, '""')}"`,
            fee.amountDue.toFixed(2),
            fee.amountPaid.toFixed(2),
            remaining.toFixed(2),
            fee.dueDate,
            fee.status,
            fee.lastPaymentDate || '',
            fee.paymentDate || ''
        ];
        csvRows.push(values.join(','));
    }

    const csvString = csvRows.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `edulite_fees_report_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const openDeleteDialog = (fee: StudentFee) => {
    setFeeToDelete(fee);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirmed = async () => {
    if (!feeToDelete) return;
    setIsDeleting(true);
    try {
      if (!auth.currentUser) throw new Error("Authentication required.");
      const idToken = await auth.currentUser.getIdToken();
      await deleteStudentFee(feeToDelete.id, idToken);
      toast({
        title: "Fee Record Deleted",
        description: `The fee for ${feeToDelete.studentName} has been permanently removed.`,
      });
      fetchFees();
      setIsDeleteDialogOpen(false);
    } catch (error) {
      console.error("Failed to delete fee record:", error);
      const errorMessage = error instanceof Error ? error.message : "Could not delete the fee record.";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsDeleting(false);
      setFeeToDelete(null);
    }
  };

  const filteredFees = fees.filter(fee => filterStatus[fee.status]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
            <div>
                <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">Student Fee Management</h1>
                <p className="text-muted-foreground">Oversee and manage student fee payments and statuses.</p>
            </div>
            <div className="flex flex-wrap gap-2">
                <Skeleton className="h-10 w-32" />
                <Skeleton className="h-10 w-48" />
                <Skeleton className="h-10 w-36" />
            </div>
        </div>
        <Card className="shadow-lg">
            <CardHeader>
                <CardTitle><Skeleton className="h-7 w-48" /></CardTitle>
                <CardDescription><Skeleton className="h-4 w-72" /></CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-lg border overflow-hidden">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {[...Array(10)].map((_, i) => <TableHead key={i}><Skeleton className="h-5 w-full min-w-20" /></TableHead>)}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {[...Array(5)].map((_, i) => (
                      <TableRow key={i}>
                        {[...Array(10)].map((_, j) => (
                            <TableCell key={j}>
                                {j === 8 ? <Skeleton className="h-6 w-24 rounded-full" /> : <Skeleton className="h-5 w-full" />}
                            </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">Student Fee Management</h1>
          <p className="text-muted-foreground">Oversee and manage student fee payments and statuses.</p>
        </div>
         <div className="flex flex-wrap gap-2">
            <Button variant="outline" onClick={exportFeesToCSV} disabled={isLoading || fees.length === 0}>
                <Download className="mr-2 h-4 w-4" /> Export Fees
            </Button>
            <Button variant="destructive" onClick={handleSendReminders} disabled={isSendingReminders}>
                {isSendingReminders ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <MailWarning className="mr-2 h-4 w-4" />}
                Send Overdue Reminders
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline">
                  <Filter className="mr-2 h-4 w-4" /> Filter Status
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56">
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                {(Object.keys(filterStatus) as Array<FeeStatus>).map((status) => (
                  <DropdownMenuCheckboxItem
                    key={status}
                    checked={filterStatus[status]}
                    onCheckedChange={(checked) => setFilterStatus(prev => ({...prev, [status]: checked}))}
                    className="capitalize"
                  >
                    {status}
                  </DropdownMenuCheckboxItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
         </div>
      </div>

      <Card className="shadow-lg">
        <CardHeader>
          <CardTitle className="font-headline text-xl">Fee Records</CardTitle>
           <CardDescription>
            Showing {filteredFees.length} of {fees.length} records. Click actions to manage.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredFees.length === 0 ? (
            <div className="text-center py-10 text-muted-foreground">
              <RupeeIcon className="mx-auto h-12 w-12 mb-4" />
              <p className="text-xl font-semibold">No fee records found.</p>
              <p>Check filters or wait for new records to be generated.</p>
            </div>
          ) : (
            <div className="rounded-lg border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Student</TableHead>
                    <TableHead>Course</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Due (₹)</TableHead>
                    <TableHead className="text-right">Paid (₹)</TableHead>
                    <TableHead className="text-right">Remaining (₹)</TableHead>
                    <TableHead>Due Date</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredFees.map((fee) => {
                    const remainingAmount = fee.amountDue - fee.amountPaid;
                    const StatusIcon = statusIconMap[fee.status] || AlertCircle;
                    
                    return (
                      <TableRow key={fee.id} className={fee.status === 'Overdue' && remainingAmount > 0 ? 'bg-destructive/5 hover:bg-destructive/10' : ''}>
                        <TableCell className="font-medium">{fee.studentName}</TableCell>
                        <TableCell>{fee.courseName}</TableCell>
                        <TableCell>{fee.feeDescription}</TableCell>
                        <TableCell className="text-right">₹{fee.amountDue.toFixed(2)}</TableCell>
                        <TableCell className="text-right">₹{fee.amountPaid.toFixed(2)}</TableCell>
                        <TableCell className={`text-right font-semibold ${remainingAmount > 0 ? 'text-destructive' : 'text-green-600'}`}>
                          ₹{remainingAmount.toFixed(2)}
                        </TableCell>
                        <TableCell>{format(new Date(fee.dueDate), "PP")}</TableCell>
                        <TableCell>
                          <Badge variant={statusVariantMap[fee.status] || 'default'} className="flex items-center gap-1.5 capitalize">
                            <StatusIcon className="h-3.5 w-3.5" />
                            {fee.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right space-x-2">
                           <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem asChild>
                                <Link href={`/invoice/${fee.id}`}>
                                  <FileText className="mr-2 h-4 w-4" /> View Receipt
                                </Link>
                              </DropdownMenuItem>
                               <DropdownMenuSeparator />
                              {remainingAmount > 0 && (
                                <>
                                  <DropdownMenuItem onClick={() => openPaymentDialog(fee)}>
                                    <RupeeIcon className="mr-2 h-4 w-4" /> Record Payment
                                  </DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                </>)}
                                <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                                {(Object.keys(statusVariantMap) as FeeStatus[]).map(statusValue => (
                                    <DropdownMenuItem
                                        key={statusValue}
                                        onClick={() => handleUpdateStatus(fee.id, statusValue)}
                                        disabled={fee.status === statusValue}
                                        className="capitalize"
                                    >
                                        {(statusIconMap[statusValue] && React.createElement(statusIconMap[statusValue], { className: "mr-2 h-4 w-4" })) || <Edit className="mr-2 h-4 w-4" />}
                                        {statusValue}
                                    </DropdownMenuItem>
                                ))}
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                    onClick={() => openDeleteDialog(fee)}
                                    className="text-destructive focus:text-destructive"
                                >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Delete Fee Record
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>

    <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
        <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
            This action cannot be undone. This will permanently delete the fee record
            for <span className="font-semibold">{feeToDelete?.feeDescription}</span> for student <span className="font-semibold">{feeToDelete?.studentName}</span>.
            </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
            onClick={handleDeleteConfirmed}
            disabled={isDeleting}
            className="bg-destructive hover:bg-destructive/90"
            >
            {isDeleting ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Delete'}
            </AlertDialogAction>
        </AlertDialogFooter>
        </AlertDialogContent>
    </AlertDialog>

    <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Record Payment</DialogTitle>
          <DialogDescription>
            Record a payment for <span className="font-semibold">{feeToPay?.feeDescription}</span> for student <span className="font-semibold">{feeToPay?.studentName}</span>.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="paymentAmount" className="text-right">
              Amount (₹)
            </Label>
            <Input
              id="paymentAmount"
              type="number"
              value={paymentAmount}
              onChange={(e) => setPaymentAmount(e.target.value)}
              className="col-span-3"
              disabled={isSubmittingPayment}
            />
          </div>
        </div>
        <DialogFooter>
          <DialogClose asChild>
            <Button type="button" variant="outline" disabled={isSubmittingPayment}>Cancel</Button>
          </DialogClose>
          <Button onClick={handleRecordPayment} disabled={isSubmittingPayment}>
            {isSubmittingPayment ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Record Payment'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
    </>
  );
}
