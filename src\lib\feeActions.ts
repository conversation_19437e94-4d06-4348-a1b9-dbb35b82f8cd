'use client';

import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { formatCurrency } from '@/lib/utils';
import { Fee, PaymentStatus } from '@/types';
import { useRouter } from 'next/navigation';

interface InvoiceActionsProps {
  fee: Fee;
}

export const InvoiceActions = ({ fee }: InvoiceActionsProps) => {
  const router = useRouter();

  const handleMarkAsPaid = () => {
    // Handle marking as paid (e.g., call an action or API)
    console.log('Marking fee as paid:', fee.id);
    // In a real application, you would update the fee status in your backend
  };

  const handleDownloadInvoice = () => {
    // Handle invoice download (e.g., generate PDF)
    console.log('Downloading invoice for fee:', fee.id);
  };

  const handleSendReminder = () => {
    // Handle sending a reminder (e.g., call an action or API)
    console.log('Sending reminder for fee:', fee.id);
  };

  return (
    <div className="flex justify-between items-center mb-6">
      <div>
        <h2 className="text-xl font-semibold">Invoice for {fee.description}</h2>
        <p className="text-sm text-muted-foreground">
          Amount Due: {formatCurrency(fee.amount)}
        </p>
        <p className="text-sm text-muted-foreground">Due Date: {fee.dueDate}</p>
        <p className="text-sm text-muted-foreground">
          Status: {fee.paymentStatus}
        </p>
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline">Actions</Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {fee.paymentStatus !== PaymentStatus.Paid && (
            <>
              <DropdownMenuItem onClick={handleMarkAsPaid}>
                Mark as Paid
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSendReminder}>
                Send Reminder
              </DropdownMenuItem>
            </>
          )}
          <DropdownMenuItem onClick={handleDownloadInvoice}>
            Download Invoice (PDF)
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};