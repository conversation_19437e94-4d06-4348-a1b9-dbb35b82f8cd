"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(app)/financial-reports/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FinancialReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _actions_feeActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/actions/feeActions */ \"(app-pages-browser)/./src/actions/feeActions.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter-x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/piggy-bank.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-bar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst feeStatuses = [\n    'Pending',\n    'Paid',\n    'Partially Paid',\n    'Overdue'\n];\nconst statusVariantMap = {\n    Pending: 'default',\n    'Partially Paid': 'secondary',\n    Paid: 'outline',\n    Overdue: 'destructive'\n};\nfunction FinancialReportsPage() {\n    _s();\n    const { user, isLoading: isAuthLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined); // Start with no date filter to show all records\n    const [courseId, setCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [paymentTransactions, setPaymentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchReportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FinancialReportsPage.useCallback[fetchReportData]\": async ()=>{\n            if (!user || !_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser) return;\n            setIsLoading(true);\n            try {\n                const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser.getIdToken();\n                const filters = {\n                    startDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.from, 'yyyy-MM-dd') : undefined,\n                    endDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.to) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.to, 'yyyy-MM-dd') : undefined,\n                    courseId: courseId || undefined,\n                    status: status || undefined\n                };\n                const [data, transactions] = await Promise.all([\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getFinancialReportData)(filters, idToken),\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getAllPaymentTransactions)(idToken)\n                ]);\n                setReportData(data);\n                setPaymentTransactions(transactions);\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"Could not fetch report data.\";\n                toast({\n                    title: \"Error\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"FinancialReportsPage.useCallback[fetchReportData]\"], [\n        user,\n        dateRange,\n        courseId,\n        status,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            async function fetchInitialCourses() {\n                try {\n                    const fetchedCourses = await getCourses();\n                    setCourses(fetchedCourses);\n                } catch (error) {\n                    toast({\n                        title: \"Error\",\n                        description: \"Could not load courses for filtering.\",\n                        variant: \"destructive\"\n                    });\n                }\n            }\n            fetchInitialCourses();\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            if (!isAuthLoading && user) {\n                fetchReportData();\n            }\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        fetchReportData,\n        isAuthLoading,\n        user\n    ]);\n    const clearFilters = ()=>{\n        setDateRange(undefined); // Clear date range to show all records\n        setCourseId('');\n        setStatus('');\n        // Fetch data immediately after clearing filters\n        setTimeout(()=>fetchReportData(), 100);\n    };\n    const summaryStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FinancialReportsPage.useMemo[summaryStats]\": ()=>{\n            const totalDue = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalDue\": (sum, fee)=>sum + fee.amountDue\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalDue\"], 0);\n            const totalPaid = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalPaid\": (sum, fee)=>sum + fee.amountPaid\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalPaid\"], 0);\n            const totalOutstanding = totalDue - totalPaid;\n            return {\n                totalDue,\n                totalPaid,\n                totalOutstanding,\n                recordCount: reportData.length\n            };\n        }\n    }[\"FinancialReportsPage.useMemo[summaryStats]\"], [\n        reportData\n    ]);\n    const exportToCSV = ()=>{\n        if (reportData.length === 0) {\n            toast({\n                title: \"No data to export\",\n                variant: \"default\"\n            });\n            return;\n        }\n        const headers = [\n            'ID',\n            'Student Name',\n            'Course Name',\n            'Fee Description',\n            'Amount Due',\n            'Amount Paid',\n            'Remaining',\n            'Due Date',\n            'Status',\n            'Last Payment Date'\n        ];\n        const csvRows = [\n            headers.join(',')\n        ];\n        for (const fee of reportData){\n            const remaining = fee.amountDue - fee.amountPaid;\n            const values = [\n                fee.id,\n                '\"'.concat(fee.studentName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.courseName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.feeDescription.replace(/\"/g, '\"\"'), '\"'),\n                fee.amountDue.toFixed(2),\n                fee.amountPaid.toFixed(2),\n                remaining.toFixed(2),\n                fee.dueDate,\n                fee.status,\n                fee.lastPaymentDate || ''\n            ];\n            csvRows.push(values.join(','));\n        }\n        const csvString = csvRows.join('\\n');\n        const blob = new Blob([\n            csvString\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"financial_report_\".concat(new Date().toISOString().split('T')[0], \".csv\"));\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                        children: \"Financial Reports\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Generate and view financial reports based on various filters.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 143,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                            children: \"Report Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Due Date Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"justify-start text-left font-normal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 161,\n                                                            columnNumber: 33\n                                                        }, this),\n                                                        (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? dateRange.to ? \"\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.from, \"LLL dd, y\"), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.to, \"LLL dd, y\")) : (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.from, \"LLL dd, y\") : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Pick a date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 169,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-auto p-0\",\n                                                align: \"start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_9__.Calendar, {\n                                                    mode: \"range\",\n                                                    selected: dateRange,\n                                                    onSelect: setDateRange,\n                                                    initialFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 174,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                        value: courseId || '_all_',\n                                        onValueChange: (value)=>setCourseId(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                    placeholder: \"All Courses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Courses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    courses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: course.id,\n                                                            children: course.name\n                                                        }, course.id, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 184,\n                                                            columnNumber: 52\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                        value: status || '_all_',\n                                        onValueChange: (value)=>setStatus(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                    placeholder: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    feeStatuses.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: s,\n                                                            className: \"capitalize\",\n                                                            children: s\n                                                        }, s, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 51\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        onClick: fetchReportData,\n                                        disabled: isLoading,\n                                        className: \"w-full\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 38\n                                        }, this) : 'Apply Filters'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        onClick: clearFilters,\n                                        disabled: isLoading,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 203,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 148,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Collected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 212,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalPaid.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 211,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Outstanding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-destructive\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalOutstanding.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 14\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Filtered Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-8 w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: summaryStats.recordCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 235,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 14\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 210,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        children: \"Filtered Fee Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                        children: [\n                                            \"A detailed list of fee records matching the selected filters. Found \",\n                                            reportData.length,\n                                            \" records.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: exportToCSV,\n                                disabled: isLoading || reportData.length === 0,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 255,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 254,\n                            columnNumber: 22\n                        }, this) : reportData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No records match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 258,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 266,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Due Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Due\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Paid\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                        children: reportData.map((fee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: fee.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: fee.courseName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: fee.feeDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(new Date(fee.dueDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: statusVariantMap[fee.status],\n                                                            className: \"capitalize\",\n                                                            children: fee.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 282,\n                                                            columnNumber: 52\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountDue.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountPaid.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, fee.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 240,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Payment Transaction History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 25\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                            variant: \"outline\",\n                                            children: [\n                                                paymentTransactions.length,\n                                                \" transactions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                children: \"Detailed history of all payment transactions recorded in the system.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 309,\n                            columnNumber: 22\n                        }, this) : paymentTransactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No payment transactions found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Payment Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 321,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Payment Method\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Transaction ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Recorded By\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Notes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                        children: paymentTransactions.map((transaction)=>{\n                                            var _reportData_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(new Date(transaction.paymentDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: transaction.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: ((_reportData_find = reportData.find((fee)=>fee.id === transaction.feeId)) === null || _reportData_find === void 0 ? void 0 : _reportData_find.feeDescription) || 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: transaction.paymentMethod\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 341,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            transaction.amount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.transactionId || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 347,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.recordedBy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.notes || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, transaction.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 37\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 295,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n        lineNumber: 142,\n        columnNumber: 5\n    }, this);\n}\n_s(FinancialReportsPage, \"y8jEenDhakqQ36qS1wk5rlTo0OI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = FinancialReportsPage;\nvar _c;\n$RefreshReg$(_c, \"FinancialReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx\n"));

/***/ })

});