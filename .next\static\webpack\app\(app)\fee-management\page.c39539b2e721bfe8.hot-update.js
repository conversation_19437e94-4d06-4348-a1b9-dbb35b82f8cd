"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/fee-management/page",{

/***/ "(app-pages-browser)/./src/app/(app)/fee-management/page.tsx":
/*!***********************************************!*\
  !*** ./src/app/(app)/fee-management/page.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FeeManagementPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _actions_feeActions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/feeActions */ \"(app-pages-browser)/./src/actions/feeActions.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail-warning.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Download,Edit,FileText,Filter,Loader2,MailWarning,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst statusVariantMap = {\n    Pending: 'default',\n    'Partially Paid': 'secondary',\n    Paid: 'outline',\n    Overdue: 'destructive'\n};\nconst statusIconMap = {\n    Pending: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n    'Partially Paid': _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n    Paid: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"],\n    Overdue: _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"]\n};\nconst RupeeIcon = (props)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        width: \"24\",\n        height: \"24\",\n        viewBox: \"0 0 24\",\n        fill: \"none\",\n        stroke: \"currentColor\",\n        strokeWidth: \"2\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 3h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 189\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 8h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 209\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 13h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 229\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 18h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 250\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 3v18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 271\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M18 3v18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 291\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9 3v18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 312\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M15 3v18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 332\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9.5 8C7.015 8 5 10.015 5 12.5S7.015 17 9.5 17h5c2.485 0 4.5-2.015 4.5-4.5S16.985 8 14.5 8z\",\n                transform: \"matrix(1 0 0 1 -0.5 -1)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 353\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 3h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 492\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M6 8h12\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 517\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                d: \"M9.5 13H18\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 68,\n                columnNumber: 542\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n        lineNumber: 68,\n        columnNumber: 3\n    }, undefined);\n_c = RupeeIcon;\nfunction FeeManagementPage() {\n    _s();\n    const [fees, setFees] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSendingReminders, setIsSendingReminders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [feeToDelete, setFeeToDelete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        Pending: true,\n        'Partially Paid': true,\n        Paid: true,\n        Overdue: true\n    });\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const { user } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth)();\n    const [isPaymentDialogOpen, setIsPaymentDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [feeToPay, setFeeToPay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [paymentAmount, setPaymentAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [paymentMethod, setPaymentMethod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('Cash');\n    const [paymentNotes, setPaymentNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmittingPayment, setIsSubmittingPayment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const fetchFees = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FeeManagementPage.useCallback[fetchFees]\": async ()=>{\n            setIsLoading(true);\n            try {\n                if (!_lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser) throw new Error(\"Authentication required.\");\n                const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser.getIdToken();\n                const fetchedFees = await (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_3__.getStudentFees)(idToken);\n                setFees(fetchedFees);\n            } catch (error) {\n                console.error(\"Failed to fetch student fees:\", error);\n                toast({\n                    title: \"Error\",\n                    description: error instanceof Error ? error.message : \"Could not fetch student fees. Please try again later.\",\n                    variant: \"destructive\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"FeeManagementPage.useCallback[fetchFees]\"], [\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FeeManagementPage.useEffect\": ()=>{\n            fetchFees();\n        }\n    }[\"FeeManagementPage.useEffect\"], [\n        fetchFees\n    ]);\n    const handleUpdateStatus = async (feeId, newStatus)=>{\n        try {\n            if (!_lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser) throw new Error(\"Authentication required.\");\n            const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser.getIdToken();\n            const updated = await (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_3__.updateFeeStatus)(feeId, newStatus, idToken);\n            if (updated) {\n                toast({\n                    title: \"Status Updated\",\n                    description: \"Fee status for \".concat(updated.studentName, \" changed to \").concat(newStatus, \".\")\n                });\n                fetchFees();\n            } else {\n                throw new Error(\"Failed to update status.\");\n            }\n        } catch (error) {\n            console.error(\"Failed to update status:\", error);\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Could not update fee status.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSendReminders = async ()=>{\n        setIsSendingReminders(true);\n        try {\n            if (!_lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser) throw new Error(\"Authentication required.\");\n            const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser.getIdToken();\n            const result = await (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_3__.sendOverdueFeeReminders)(idToken);\n            toast({\n                title: \"Reminders Sent\",\n                description: \"Successfully processed and sent \".concat(result.count, \" overdue fee reminders. The table has been updated.\")\n            });\n            fetchFees();\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Could not send reminders.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSendingReminders(false);\n        }\n    };\n    const openPaymentDialog = (fee)=>{\n        setFeeToPay(fee);\n        setPaymentAmount(''); // Reset amount for new payment\n        setIsPaymentDialogOpen(true);\n    };\n    const handleRecordPayment = async ()=>{\n        if (!feeToPay || !paymentAmount || isNaN(parseFloat(paymentAmount))) {\n            toast({\n                title: \"Invalid Input\",\n                description: \"Please enter a valid payment amount.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        const amount = parseFloat(paymentAmount);\n        if (amount <= 0) {\n            toast({\n                title: \"Invalid Amount\",\n                description: \"Payment amount must be positive.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        setIsSubmittingPayment(true);\n        try {\n            if (!_lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser) throw new Error(\"Authentication required.\");\n            const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser.getIdToken();\n            await (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_3__.recordManualPayment)({\n                feeId: feeToPay.id,\n                amountPaid: amount,\n                paymentDate: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_21__.format)(new Date(), 'yyyy-MM-dd')\n            }, idToken);\n            toast({\n                title: \"Payment Recorded\",\n                description: \"₹\".concat(amount.toFixed(2), \" recorded for \").concat(feeToPay.studentName, \".\")\n            });\n            fetchFees(); // Refresh the list\n            setIsPaymentDialogOpen(false);\n        } catch (error) {\n            toast({\n                title: \"Error\",\n                description: error instanceof Error ? error.message : \"Failed to record payment.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsSubmittingPayment(false);\n        }\n    };\n    const exportFeesToCSV = ()=>{\n        if (fees.length === 0) {\n            toast({\n                title: \"No fee records to export\",\n                variant: \"default\"\n            });\n            return;\n        }\n        const headers = [\n            'ID',\n            'Student Name',\n            'Student ID',\n            'Course Name',\n            'Fee Description',\n            'Amount Due',\n            'Amount Paid',\n            'Remaining',\n            'Due Date',\n            'Status',\n            'Last Payment Date',\n            'Final Payment Date'\n        ];\n        const csvRows = [\n            headers.join(',')\n        ];\n        for (const fee of fees){\n            const remaining = fee.amountDue - fee.amountPaid;\n            const values = [\n                fee.id,\n                '\"'.concat(fee.studentName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.studentId, '\"'),\n                '\"'.concat(fee.courseName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.feeDescription.replace(/\"/g, '\"\"'), '\"'),\n                fee.amountDue.toFixed(2),\n                fee.amountPaid.toFixed(2),\n                remaining.toFixed(2),\n                fee.dueDate,\n                fee.status,\n                fee.lastPaymentDate || '',\n                fee.paymentDate || ''\n            ];\n            csvRows.push(values.join(','));\n        }\n        const csvString = csvRows.join('\\n');\n        const blob = new Blob([\n            csvString\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"edulite_fees_report_\".concat(new Date().toISOString().split('T')[0], \".csv\"));\n        link.style.visibility = 'hidden';\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    const openDeleteDialog = (fee)=>{\n        setFeeToDelete(fee);\n        setIsDeleteDialogOpen(true);\n    };\n    const handleDeleteConfirmed = async ()=>{\n        if (!feeToDelete) return;\n        setIsDeleting(true);\n        try {\n            if (!_lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser) throw new Error(\"Authentication required.\");\n            const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_14__.auth.currentUser.getIdToken();\n            await (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_3__.deleteStudentFee)(feeToDelete.id, idToken);\n            toast({\n                title: \"Fee Record Deleted\",\n                description: \"The fee for \".concat(feeToDelete.studentName, \" has been permanently removed.\")\n            });\n            fetchFees();\n            setIsDeleteDialogOpen(false);\n        } catch (error) {\n            console.error(\"Failed to delete fee record:\", error);\n            const errorMessage = error instanceof Error ? error.message : \"Could not delete the fee record.\";\n            toast({\n                title: \"Error\",\n                description: errorMessage,\n                variant: \"destructive\"\n            });\n        } finally{\n            setIsDeleting(false);\n            setFeeToDelete(null);\n        }\n    };\n    const filteredFees = fees.filter((fee)=>filterStatus[fee.status]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row items-start md:items-center justify-between gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                                    children: \"Student Fee Management\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 265,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"Oversee and manage student fee payments and statuses.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 266,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                    className: \"h-10 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                    className: \"h-10 w-48\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 270,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                    className: \"h-10 w-36\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"shadow-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                        className: \"h-7 w-48\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 28\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 276,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                        className: \"h-4 w-72\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 34\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 277,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 275,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-lg border overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                children: [\n                                                    ...Array(10)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                            className: \"h-5 w-full min-w-20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 72\n                                                        }, this)\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 53\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                            children: [\n                                                ...Array(5)\n                                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                    children: [\n                                                        ...Array(10)\n                                                    ].map((_, j)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                            children: j === 8 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                                className: \"h-6 w-24 rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 44\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_16__.Skeleton, {\n                                                                className: \"h-5 w-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 93\n                                                            }, this)\n                                                        }, j, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 291,\n                                                            columnNumber: 29\n                                                        }, this))\n                                                }, i, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                    lineNumber: 289,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n            lineNumber: 262,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row items-start md:items-center justify-between gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                                        children: \"Student Fee Management\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"Oversee and manage student fee payments and statuses.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 11\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        variant: \"outline\",\n                                        onClick: exportFeesToCSV,\n                                        disabled: isLoading || fees.length === 0,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" Export Fees\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        variant: \"destructive\",\n                                        onClick: handleSendReminders,\n                                        disabled: isSendingReminders,\n                                        children: [\n                                            isSendingReminders ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 39\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 91\n                                            }, this),\n                                            \"Send Overdue Reminders\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"outline\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \" Filter Status\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                                                className: \"w-56\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuLabel, {\n                                                        children: \"Filter by Status\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    Object.keys(filterStatus).map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuCheckboxItem, {\n                                                            checked: filterStatus[status],\n                                                            onCheckedChange: (checked)=>setFilterStatus((prev)=>({\n                                                                        ...prev,\n                                                                        [status]: checked\n                                                                    })),\n                                                            className: \"capitalize\",\n                                                            children: status\n                                                        }, status, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 332,\n                                                            columnNumber: 19\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 10\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"shadow-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"font-headline text-xl\",\n                                        children: \"Fee Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: [\n                                            \"Showing \",\n                                            filteredFees.length,\n                                            \" of \",\n                                            fees.length,\n                                            \" records. Click actions to manage.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 12\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: filteredFees.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center py-10 text-muted-foreground\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RupeeIcon, {\n                                            className: \"mx-auto h-12 w-12 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: \"No fee records found.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Check filters or wait for new records to be generated.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"rounded-lg border overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.Table, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHeader, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            children: \"Student\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 365,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            children: \"Course\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            children: \"Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            className: \"text-right\",\n                                                            children: \"Due (₹)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 368,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            className: \"text-right\",\n                                                            children: \"Paid (₹)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            className: \"text-right\",\n                                                            children: \"Remaining (₹)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            children: \"Due Date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            children: \"Status\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 372,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableHead, {\n                                                            className: \"text-right\",\n                                                            children: \"Actions\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableBody, {\n                                                children: filteredFees.map((fee)=>{\n                                                    const remainingAmount = fee.amountDue - fee.amountPaid;\n                                                    const StatusIcon = statusIconMap[fee.status] || _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableRow, {\n                                                        className: fee.status === 'Overdue' && remainingAmount > 0 ? 'bg-destructive/5 hover:bg-destructive/10' : '',\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"font-medium\",\n                                                                children: fee.studentName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                children: fee.courseName\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                children: fee.feeDescription\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    fee.amountDue.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"text-right\",\n                                                                children: [\n                                                                    \"₹\",\n                                                                    fee.amountPaid.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 387,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"text-right font-semibold \".concat(remainingAmount > 0 ? 'text-destructive' : 'text-green-600'),\n                                                                children: [\n                                                                    \"₹\",\n                                                                    remainingAmount.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 388,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_21__.format)(new Date(fee.dueDate), \"PP\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 391,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                    variant: statusVariantMap[fee.status] || 'default',\n                                                                    className: \"flex items-center gap-1.5 capitalize\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StatusIcon, {\n                                                                            className: \"h-3.5 w-3.5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                            lineNumber: 394,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        fee.status\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                    lineNumber: 393,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 392,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_5__.TableCell, {\n                                                                className: \"text-right space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                                                                            asChild: true,\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                className: \"h-8 w-8 p-0\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"sr-only\",\n                                                                                        children: \"Open menu\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                        lineNumber: 402,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                        className: \"h-4 w-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                        lineNumber: 403,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                lineNumber: 401,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                            lineNumber: 400,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                                                                            align: \"end\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                                                        href: \"/invoice/\".concat(fee.id),\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                                className: \"mr-2 h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                                lineNumber: 409,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            \" View Receipt\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                        lineNumber: 408,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                    lineNumber: 407,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                    lineNumber: 412,\n                                                                                    columnNumber: 32\n                                                                                }, this),\n                                                                                remainingAmount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                                                            onClick: ()=>openPaymentDialog(fee),\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RupeeIcon, {\n                                                                                                    className: \"mr-2 h-4 w-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                                    lineNumber: 416,\n                                                                                                    columnNumber: 37\n                                                                                                }, this),\n                                                                                                \" Record Payment\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                            lineNumber: 415,\n                                                                                            columnNumber: 35\n                                                                                        }, this),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                            lineNumber: 418,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuLabel, {\n                                                                                    children: \"Change Status\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                    lineNumber: 420,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                Object.keys(statusVariantMap).map((statusValue)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                                                        onClick: ()=>handleUpdateStatus(fee.id, statusValue),\n                                                                                        disabled: fee.status === statusValue,\n                                                                                        className: \"capitalize\",\n                                                                                        children: [\n                                                                                            statusIconMap[statusValue] && /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(statusIconMap[statusValue], {\n                                                                                                className: \"mr-2 h-4 w-4\"\n                                                                                            }) || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                                                className: \"mr-2 h-4 w-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                                lineNumber: 428,\n                                                                                                columnNumber: 156\n                                                                                            }, this),\n                                                                                            statusValue\n                                                                                        ]\n                                                                                    }, statusValue, true, {\n                                                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                        lineNumber: 422,\n                                                                                        columnNumber: 37\n                                                                                    }, this)),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                    lineNumber: 432,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                                                    onClick: ()=>openDeleteDialog(fee),\n                                                                                    className: \"text-destructive focus:text-destructive\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                                                            className: \"mr-2 h-4 w-4\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                            lineNumber: 437,\n                                                                                            columnNumber: 37\n                                                                                        }, this),\n                                                                                        \"Delete Fee Record\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                                    lineNumber: 433,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                            lineNumber: 406,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 28\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                                lineNumber: 398,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, fee.id, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialog, {\n                open: isDeleteDialogOpen,\n                onOpenChange: setIsDeleteDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogTitle, {\n                                    children: \"Are you absolutely sure?\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogDescription, {\n                                    children: [\n                                        \"This action cannot be undone. This will permanently delete the fee record for \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: feeToDelete === null || feeToDelete === void 0 ? void 0 : feeToDelete.feeDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 17\n                                        }, this),\n                                        \" for student \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: feeToDelete === null || feeToDelete === void 0 ? void 0 : feeToDelete.studentName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 460,\n                                            columnNumber: 98\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 458,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 456,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogCancel, {\n                                    disabled: isDeleting,\n                                    children: \"Cancel\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogAction, {\n                                    onClick: handleDeleteConfirmed,\n                                    disabled: isDeleting,\n                                    className: \"bg-destructive hover:bg-destructive/90\",\n                                    children: isDeleting ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 27\n                                    }, this) : 'Delete'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 465,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                    lineNumber: 455,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 454,\n                columnNumber: 5\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isPaymentDialogOpen,\n                onOpenChange: setIsPaymentDialogOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    className: \"sm:max-w-[425px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                    children: \"Record Payment\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 479,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogDescription, {\n                                    children: [\n                                        \"Record a payment for \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: feeToPay === null || feeToPay === void 0 ? void 0 : feeToPay.feeDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 34\n                                        }, this),\n                                        \" for student \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-semibold\",\n                                            children: feeToPay === null || feeToPay === void 0 ? void 0 : feeToPay.studentName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                            lineNumber: 481,\n                                            columnNumber: 112\n                                        }, this),\n                                        \".\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 480,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-4 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-4 items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_12__.Label, {\n                                        htmlFor: \"paymentAmount\",\n                                        className: \"text-right\",\n                                        children: \"Amount (₹)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_13__.Input, {\n                                        id: \"paymentAmount\",\n                                        type: \"number\",\n                                        value: paymentAmount,\n                                        onChange: (e)=>setPaymentAmount(e.target.value),\n                                        className: \"col-span-3\",\n                                        disabled: isSubmittingPayment\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 11\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 9\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogClose, {\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        type: \"button\",\n                                        variant: \"outline\",\n                                        disabled: isSubmittingPayment,\n                                        children: \"Cancel\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 500,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                    onClick: handleRecordPayment,\n                                    disabled: isSubmittingPayment,\n                                    children: isSubmittingPayment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Download_Edit_FileText_Filter_Loader2_MailWarning_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 animate-spin\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 36\n                                    }, this) : 'Record Payment'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 9\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 7\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\fee-management\\\\page.tsx\",\n                lineNumber: 476,\n                columnNumber: 5\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(FeeManagementPage, \"G7+aPLMTEa4fmM2198mAKUkMax0=\", false, function() {\n    return [\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast,\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_15__.useAuth\n    ];\n});\n_c1 = FeeManagementPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"RupeeIcon\");\n$RefreshReg$(_c1, \"FeeManagementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/fee-management/page.tsx\n"));

/***/ })

});