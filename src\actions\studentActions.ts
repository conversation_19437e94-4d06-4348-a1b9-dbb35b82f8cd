
'use server';

import { db } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';
import type { EnrolledStudent, StudentApplication, Course } from '@/types';
import { getCourses } from './courseActions';
import { format } from 'date-fns';
import { revalidatePath } from 'next/cache';

const APPLICATIONS_COLLECTION = 'studentApplications';

/**
 * Fetches a list of all enrolled students (those with accepted applications).
 * This is a protected action for admins and accountants.
 * @param idToken The Firebase ID token of the calling user.
 * @returns A promise that resolves to an array of enrolled students.
 */
export async function getEnrolledStudents(idToken: string): Promise<EnrolledStudent[]> {
  await requireAuth(idToken, ['admin', 'accountant']);

  // Fetch all courses first to easily map course names later
  const courses = await getCourses();
  const coursesMap = new Map(courses.map(c => [c.id, c.name]));

  const snapshot = await db.collection(APPLICATIONS_COLLECTION)
    .where('status', '==', 'Accepted')
    .get();

  if (snapshot.empty) {
    return [];
  }

  const enrolledStudents: EnrolledStudent[] = snapshot.docs
    .map(doc => {
      const app = doc.data() as StudentApplication;
      // Only include students who have a valid userId
      if (!app.userId) {
        return null;
      }
      
      const admissionDateStr = app.applicationDate && (app.applicationDate as any).toDate 
        ? ((app.applicationDate as any).toDate() as Date).toISOString().split('T')[0] 
        : app.applicationDate;

      return {
        id: app.userId,
        applicationId: doc.id,
        name: app.fullName,
        email: app.email,
        courseId: app.desiredCourse,
        courseName: coursesMap.get(app.desiredCourse) || 'Unknown Course',
        admissionDate: admissionDateStr,
      };
    })
    .filter((student): student is EnrolledStudent => student !== null);

  // Sort by name in-memory
  enrolledStudents.sort((a, b) => a.name.localeCompare(b.name));
  
  return JSON.parse(JSON.stringify(enrolledStudents));
}


/**
 * Fetches the original application data for a specific student.
 * @param studentId The UID of the student.
 * @param idToken The Firebase ID token of the calling admin/accountant user.
 * @returns The student's application object or null if not found.
 */
export async function getStudentApplicationDetails(studentId: string, idToken: string): Promise<StudentApplication | null> {
    await requireAuth(idToken, ['admin', 'accountant']);

    const query = db.collection(APPLICATIONS_COLLECTION)
                    .where('userId', '==', studentId)
                    .limit(1);

    const snapshot = await query.get();

    if (snapshot.empty) {
        return null;
    }

    const doc = snapshot.docs[0];
    const data = doc.data();

    return JSON.parse(JSON.stringify({
      id: doc.id,
      ...data,
      applicationDate: data.applicationDate.toDate().toISOString().split('T')[0],
      dateOfBirth: data.dateOfBirth?.toDate ? data.dateOfBirth.toDate().toISOString().split('T')[0] : data.dateOfBirth,
    } as StudentApplication));
}

export type UpdateStudentDetailsData = Pick<StudentApplication, 'id' | 'fullName' | 'mobileNumber' | 'motherName' | 'fatherName' | 'dateOfBirth' | 'religion'>;

/**
 * Updates the details for an enrolled student.
 * This is a protected action for admins.
 * @param studentData The data to update.
 * @param idToken The Firebase ID token of the calling admin user.
 * @returns The updated student application object.
 */
export async function updateStudentDetails(studentData: UpdateStudentDetailsData, idToken: string): Promise<StudentApplication> {
  const { uid, token } = await requireAuth(idToken, ['admin']);

  const { id, ...dataToUpdate } = studentData;
  const applicationRef = db.collection(APPLICATIONS_COLLECTION).doc(id);
  
  const applicationDoc = await applicationRef.get();
  if (!applicationDoc.exists) {
    throw new Error("Student application record not found.");
  }

  await applicationRef.update({
    fullName: dataToUpdate.fullName,
    mobileNumber: dataToUpdate.mobileNumber,
    motherName: dataToUpdate.motherName,
    fatherName: dataToUpdate.fatherName,
    dateOfBirth: dataToUpdate.dateOfBirth,
    religion: dataToUpdate.religion,
  });

  const updatedDoc = await applicationRef.get();
  const updatedData = updatedDoc.data() as StudentApplication;

  // Revalidate the student details page and the main students list page
  revalidatePath(`/students/${updatedData.userId}`);
  revalidatePath('/students');
  
  return JSON.parse(JSON.stringify({
    ...updatedData,
    id: updatedDoc.id,
    applicationDate: updatedData.applicationDate.toDate().toISOString().split('T')[0],
    dateOfBirth: updatedData.dateOfBirth?.toDate ? updatedData.dateOfBirth.toDate().toISOString().split('T')[0] : updatedData.dateOfBirth,
  }));
}
