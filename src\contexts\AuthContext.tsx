
'use client';

import type { User } from '@/types';
import { useRouter, usePathname } from 'next/navigation';
import React, { createContext, useContext, useState, useEffect, type ReactNode } from 'react';
import { auth } from '@/lib/firebase';
import {
  onAuthStateChanged,
  signOut as firebaseSignOut,
  signInWithEmailAndPassword,
  updateProfile,
  EmailAuthProvider,
  reauthenticateWithCredential,
  updatePassword as firebaseUpdatePassword,
  type User as FirebaseUser,
  type AuthError
} from 'firebase/auth';

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: AuthError }>;
  logout: () => Promise<void>;
  isLoading: boolean;
  updateUserDisplayName: (newName: string) => Promise<void>;
  changeUserPassword: (currentPassword: string, newPassword: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    setIsLoading(true);
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser: FirebaseUser | null) => {
      if (firebaseUser) {
        try {
          const tokenResult = await firebaseUser.getIdTokenResult(true); // Force refresh for latest claims
          
          const determinedRole: 'admin' | 'student' | 'accountant' = 
            (tokenResult.claims.role && ['admin', 'student', 'accountant'].includes(tokenResult.claims.role as string))
              ? tokenResult.claims.role as 'admin' | 'student' | 'accountant'
              : 'student';

          if (determinedRole === 'student' && !tokenResult.claims.role) {
            // This log is important for administrators to diagnose role issues for admin/accountant accounts.
            // It's the expected, normal behavior for new students who have just applied.
             console.warn(
              `User ${firebaseUser.email} has no valid 'role' custom claim. ` +
              `Defaulting to 'student' role. This is correct for new applicants. See docs/managing-user-roles.md for admin/accountant setup.`
            );
          }

          setUser({
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            name: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User',
            phone: firebaseUser.phoneNumber,
            role: determinedRole,
          });

        } catch (error) {
          console.error("Error fetching user token or claims:", error);
          setUser(null); // Sign out on error
        }
      } else {
        setUser(null);
      }
      setIsLoading(false);
    });
    return () => unsubscribe();
  }, []);

  useEffect(() => {
    if (isLoading) return;

    const publicPages = ['/login', '/apply', '/capture-lead'];
    const isPublicPage = publicPages.some(page => pathname.startsWith(page));

    if (user) {
      // User is logged in. If they are on a public page, redirect them.
      if (isPublicPage) {
        if (user.role === 'admin') {
          router.push('/dashboard');
        } else if (user.role === 'accountant') {
          router.push('/fee-management');
        } else if (user.role === 'student') {
          router.push('/my-courses');
        }
      }
    } else {
      // User is not logged in. If they are on a protected page, redirect to login.
      if (!isPublicPage) {
        router.push('/login');
      }
    }
  }, [user, isLoading, pathname, router]);


  const login = async (email: string, password: string): Promise<{ success: boolean; error?: AuthError }> => {
    setIsLoading(true);
    try {
      await signInWithEmailAndPassword(auth, email, password);
      // If successful, onAuthStateChanged will handle setting the user and eventually setIsLoading(false)
      // The useEffect above will handle redirection based on role.
      return { success: true };
    } catch (error) {
      const authError = error as AuthError; // Cast to AuthError for code property
      
      const commonAuthErrorCodes = [
        'auth/invalid-credential', 
        'auth/wrong-password', 
        'auth/user-not-found', 
        'auth/too-many-requests', 
        'auth/network-request-failed',
        'auth/invalid-email',
        'auth/user-disabled'
      ];
      if (authError.code && !commonAuthErrorCodes.includes(authError.code)) {
        console.error("Firebase login error in AuthContext:", authError.code, authError.message);
      }
      setIsLoading(false); // Reset global loading state only on error
      return { success: false, error: authError };
    }
  };

  const logout = async () => {
    setIsLoading(true);
    try {
      await firebaseSignOut(auth);
    } catch (error) {
      console.error("Firebase logout error:", error);
    } finally {
      setUser(null);
      setIsLoading(false);
      router.push('/login');
    }
  };

  const updateUserDisplayName = async (newName: string) => {
    if (!auth.currentUser) {
      throw new Error("No user currently signed in.");
    }
    await updateProfile(auth.currentUser, { displayName: newName });
    setUser(prevUser => prevUser ? { ...prevUser, name: newName } : null);
  };

  const changeUserPassword = async (currentPassword: string, newPassword: string) => {
    if (!auth.currentUser || !auth.currentUser.email) {
      throw new Error("No user currently signed in or user email is not available.");
    }
    const credential = EmailAuthProvider.credential(auth.currentUser.email, currentPassword);
    await reauthenticateWithCredential(auth.currentUser, credential);
    await firebaseUpdatePassword(auth.currentUser, newPassword);
  };


  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading, updateUserDisplayName, changeUserPassword }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
