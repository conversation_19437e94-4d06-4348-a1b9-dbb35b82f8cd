
'use server';

import { revalidatePath } from 'next/cache';
import { db } from '@/lib/firebase-admin';
import { requireAuth } from '@/lib/authUtils';
import type { Course } from '@/types';

const COURSES_COLLECTION = 'courses';

// This function can be public as course info is needed for application forms
export async function getCourses(): Promise<Course[]> {
  try {
    const coursesSnapshot = await db.collection(COURSES_COLLECTION).get();
    if (coursesSnapshot.empty) {
      return [];
    }
    const courses: Course[] = coursesSnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        // Ensure that semesters and specializations are always arrays to prevent client-side errors
        semesters: data.semesters || [], 
        specializations: data.specializations || [],
      } as Course
    });

    // Sort by name in-memory to avoid needing a composite index
    courses.sort((a, b) => a.name.localeCompare(b.name));

    return JSON.parse(JSON.stringify(courses));
  } catch (error: any) {
    if (error.code === 5) { // Firestore NOT_FOUND error
      console.log("Courses collection not found. Returning empty array. This is expected if no courses have been created yet.");
      return [];
    }
    console.error("Error fetching courses from Firestore:", error);
    throw new Error("An unexpected error occurred while fetching courses.");
  }
}

// This function can be public
export async function getCourseById(courseId: string): Promise<Course | null> {
  const courseDoc = await db.collection(COURSES_COLLECTION).doc(courseId).get();
  if (!courseDoc.exists) {
    return null;
  }
  const data = courseDoc.data();
  // Ensure that semesters and specializations are always arrays
  return JSON.parse(JSON.stringify({ 
      id: courseDoc.id, 
      ...data,
      semesters: data?.semesters || [],
      specializations: data?.specializations || [],
    } as Course));
}

export async function createCourse(courseData: Omit<Course, 'id'>, idToken: string): Promise<Course> {
  await requireAuth(idToken, ['admin']);
  
  const coursePayload = {
    ...courseData,
    semesters: courseData.semesters || [], // Ensure semesters is an array
  };

  const courseRef = await db.collection(COURSES_COLLECTION).add(coursePayload);
  
  const newCourse: Course = {
    id: courseRef.id,
    ...coursePayload,
  };

  revalidatePath('/courses');
  revalidatePath('/apply');
  revalidatePath('/my-courses'); 
  return JSON.parse(JSON.stringify(newCourse));
}

export async function updateCourse(courseData: Course, idToken: string): Promise<Course | null> {
  await requireAuth(idToken, ['admin']);

  const { id, ...dataToUpdate } = courseData;
  const courseRef = db.collection(COURSES_COLLECTION).doc(id);

  // Ensure semesters is an array, as it's optional on the type but required for logic
  const updatedData = { ...dataToUpdate, semesters: dataToUpdate.semesters || [] };

  await courseRef.set(updatedData, { merge: true }); // Use set with merge to handle new/updated nested fields
  
  revalidatePath('/courses');
  revalidatePath('/apply');
  revalidatePath('/my-courses');
  revalidatePath(`/my-courses/${id}`);
  return JSON.parse(JSON.stringify(courseData));
}

export async function deleteCourse(courseId: string, idToken: string): Promise<{ success: boolean }> {
  await requireAuth(idToken, ['admin']);
  
  await db.collection(COURSES_COLLECTION).doc(courseId).delete();
  
  // Also delete associated fee structure
  try {
      await db.collection('courseFeeStructures').doc(courseId).delete();
  } catch (e) {
      console.warn(`Could not delete fee structure for course ${courseId}. It might not have existed.`)
  }

  revalidatePath('/courses');
  revalidatePath('/apply');
  revalidatePath('/my-courses');
  return { success: true };
}
