import admin from 'firebase-admin';
import { getFirestore } from 'firebase-admin/firestore';

let serviceAccount: admin.ServiceAccount;

try {
  // This file is expected to be at the project root.
  // require() resolves from the file's location. When the app is built, this resolution might change.
  // This path assumes a standard project structure where `src` is in the root.
  serviceAccount = require('../../service-account.json');
} catch (e) {
  console.error(
    'Error: service-account.json not found or could not be loaded. This file is required for server-side action authentication and should be placed at the project root. Please refer to Firebase documentation to generate one for your project.'
  );
  throw new Error(
    'Service account initialization failed. See server logs for details.'
  );
}

if (!admin.apps.length) {
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
  });
}

export const adminAuth = admin.auth();
export const db = getFirestore();
