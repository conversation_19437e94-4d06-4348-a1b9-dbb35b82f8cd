"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(app)/financial-reports/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FinancialReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _actions_feeActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/actions/feeActions */ \"(app-pages-browser)/./src/actions/feeActions.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter-x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/piggy-bank.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-bar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst feeStatuses = [\n    'Pending',\n    'Paid',\n    'Partially Paid',\n    'Overdue'\n];\nconst statusVariantMap = {\n    Pending: 'default',\n    'Partially Paid': 'secondary',\n    Paid: 'outline',\n    Overdue: 'destructive'\n};\nfunction FinancialReportsPage() {\n    _s();\n    const { user, isLoading: isAuthLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined); // Start with no date filter to show all records\n    const [courseId, setCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [paymentTransactions, setPaymentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchReportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FinancialReportsPage.useCallback[fetchReportData]\": async ()=>{\n            if (!user || !_lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser) return;\n            setIsLoading(true);\n            try {\n                const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_4__.auth.currentUser.getIdToken();\n                const filters = {\n                    startDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.from, 'yyyy-MM-dd') : undefined,\n                    endDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.to) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.to, 'yyyy-MM-dd') : undefined,\n                    courseId: courseId || undefined,\n                    status: status || undefined\n                };\n                const [data, transactions] = await Promise.all([\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getFinancialReportData)(filters, idToken),\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getAllPaymentTransactions)(idToken)\n                ]);\n                setReportData(data);\n                setPaymentTransactions(transactions);\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"Could not fetch report data.\";\n                toast({\n                    title: \"Error\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"FinancialReportsPage.useCallback[fetchReportData]\"], [\n        user,\n        dateRange,\n        courseId,\n        status,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            async function fetchInitialCourses() {\n                try {\n                    if (!user) return;\n                    const idToken = await user.getIdToken();\n                    const fetchedCourses = await (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getCoursesWithFees)(idToken);\n                    setCourses(fetchedCourses);\n                } catch (error) {\n                    toast({\n                        title: \"Error\",\n                        description: \"Could not load courses for filtering.\",\n                        variant: \"destructive\"\n                    });\n                }\n            }\n            fetchInitialCourses();\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            if (!isAuthLoading && user) {\n                fetchReportData();\n            }\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        fetchReportData,\n        isAuthLoading,\n        user\n    ]);\n    const clearFilters = ()=>{\n        setDateRange(undefined); // Clear date range to show all records\n        setCourseId('');\n        setStatus('');\n        // Fetch data immediately after clearing filters\n        setTimeout(()=>fetchReportData(), 100);\n    };\n    const summaryStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FinancialReportsPage.useMemo[summaryStats]\": ()=>{\n            const totalDue = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalDue\": (sum, fee)=>sum + fee.amountDue\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalDue\"], 0);\n            const totalPaid = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalPaid\": (sum, fee)=>sum + fee.amountPaid\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalPaid\"], 0);\n            const totalOutstanding = totalDue - totalPaid;\n            return {\n                totalDue,\n                totalPaid,\n                totalOutstanding,\n                recordCount: reportData.length\n            };\n        }\n    }[\"FinancialReportsPage.useMemo[summaryStats]\"], [\n        reportData\n    ]);\n    const exportToCSV = ()=>{\n        if (reportData.length === 0) {\n            toast({\n                title: \"No data to export\",\n                variant: \"default\"\n            });\n            return;\n        }\n        const headers = [\n            'ID',\n            'Student Name',\n            'Course Name',\n            'Fee Description',\n            'Amount Due',\n            'Amount Paid',\n            'Remaining',\n            'Due Date',\n            'Status',\n            'Last Payment Date'\n        ];\n        const csvRows = [\n            headers.join(',')\n        ];\n        for (const fee of reportData){\n            const remaining = fee.amountDue - fee.amountPaid;\n            const values = [\n                fee.id,\n                '\"'.concat(fee.studentName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.courseName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.feeDescription.replace(/\"/g, '\"\"'), '\"'),\n                fee.amountDue.toFixed(2),\n                fee.amountPaid.toFixed(2),\n                remaining.toFixed(2),\n                fee.dueDate,\n                fee.status,\n                fee.lastPaymentDate || ''\n            ];\n            csvRows.push(values.join(','));\n        }\n        const csvString = csvRows.join('\\n');\n        const blob = new Blob([\n            csvString\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"financial_report_\".concat(new Date().toISOString().split('T')[0], \".csv\"));\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                        children: \"Financial Reports\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Generate and view financial reports based on various filters.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 145,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                            children: \"Report Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Due Date Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"justify-start text-left font-normal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 163,\n                                                            columnNumber: 33\n                                                        }, this),\n                                                        (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? dateRange.to ? \"\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.from, \"LLL dd, y\"), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.to, \"LLL dd, y\")) : (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(dateRange.from, \"LLL dd, y\") : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Pick a date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 159,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_8__.PopoverContent, {\n                                                className: \"w-auto p-0\",\n                                                align: \"start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_9__.Calendar, {\n                                                    mode: \"range\",\n                                                    selected: dateRange,\n                                                    onSelect: setDateRange,\n                                                    initialFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 176,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 175,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 157,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                        value: courseId || '_all_',\n                                        onValueChange: (value)=>setCourseId(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                    placeholder: \"All Courses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 183,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Courses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 185,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    courses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: course.id,\n                                                            children: course.name\n                                                        }, course.id, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 186,\n                                                            columnNumber: 52\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.Select, {\n                                        value: status || '_all_',\n                                        onValueChange: (value)=>setStatus(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectValue, {\n                                                    placeholder: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    feeStatuses.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_10__.SelectItem, {\n                                                            value: s,\n                                                            className: \"capitalize\",\n                                                            children: s\n                                                        }, s, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 51\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 190,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        onClick: fetchReportData,\n                                        disabled: isLoading,\n                                        className: \"w-full\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 202,\n                                            columnNumber: 38\n                                        }, this) : 'Apply Filters'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                        onClick: clearFilters,\n                                        disabled: isLoading,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 150,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Collected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalPaid.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Outstanding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-destructive\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalOutstanding.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 14\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Filtered Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-8 w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: summaryStats.recordCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 231,\n                        columnNumber: 14\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 212,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                        children: \"Filtered Fee Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                        children: [\n                                            \"A detailed list of fee records matching the selected filters. Found \",\n                                            reportData.length,\n                                            \" records.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                                onClick: exportToCSV,\n                                disabled: isLoading || reportData.length === 0,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 22\n                        }, this) : reportData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No records match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Due Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Due\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Paid\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 267,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                        children: reportData.map((fee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: fee.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: fee.courseName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: fee.feeDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(new Date(fee.dueDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: statusVariantMap[fee.status],\n                                                            className: \"capitalize\",\n                                                            children: fee.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 52\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountDue.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountPaid.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 286,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, fee.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center justify-between\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Payment Transaction History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 25\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                            variant: \"outline\",\n                                            children: [\n                                                paymentTransactions.length,\n                                                \" transactions\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardDescription, {\n                                children: \"Detailed history of all payment transactions recorded in the system.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 298,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_13__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 22\n                        }, this) : paymentTransactions.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No payment transactions found.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Payment Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 325,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Payment Method\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 327,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Transaction ID\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Recorded By\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 329,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableHead, {\n                                                    children: \"Notes\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableBody, {\n                                        children: paymentTransactions.map((transaction)=>{\n                                            var _reportData_find;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_14__.format)(new Date(transaction.paymentDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: transaction.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: ((_reportData_find = reportData.find((fee)=>fee.id === transaction.feeId)) === null || _reportData_find === void 0 ? void 0 : _reportData_find.feeDescription) || 'N/A'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_12__.Badge, {\n                                                            variant: \"outline\",\n                                                            children: transaction.paymentMethod\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 45\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            transaction.amount.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 346,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.transactionId || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.recordedBy\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_11__.TableCell, {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: transaction.notes || '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, transaction.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 37\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 333,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 297,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_s(FinancialReportsPage, \"y8jEenDhakqQ36qS1wk5rlTo0OI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast\n    ];\n});\n_c = FinancialReportsPage;\nvar _c;\n$RefreshReg$(_c, \"FinancialReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx\n"));

/***/ })

});