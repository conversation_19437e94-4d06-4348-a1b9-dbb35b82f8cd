"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fast-equals";
exports.ids = ["vendor-chunks/fast-equals"];
exports.modules = {

/***/ "(ssr)/./node_modules/fast-equals/dist/esm/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/fast-equals/dist/esm/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   circularDeepEqual: () => (/* binding */ circularDeepEqual),\n/* harmony export */   circularShallowEqual: () => (/* binding */ circularShallowEqual),\n/* harmony export */   createCustomEqual: () => (/* binding */ createCustomEqual),\n/* harmony export */   deepEqual: () => (/* binding */ deepEqual),\n/* harmony export */   sameValueZeroEqual: () => (/* binding */ sameValueZeroEqual),\n/* harmony export */   shallowEqual: () => (/* binding */ shallowEqual),\n/* harmony export */   strictCircularDeepEqual: () => (/* binding */ strictCircularDeepEqual),\n/* harmony export */   strictCircularShallowEqual: () => (/* binding */ strictCircularShallowEqual),\n/* harmony export */   strictDeepEqual: () => (/* binding */ strictDeepEqual),\n/* harmony export */   strictShallowEqual: () => (/* binding */ strictShallowEqual)\n/* harmony export */ });\nvar getOwnPropertyNames = Object.getOwnPropertyNames, getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n/**\n * Combine two comparators into a single comparators.\n */\nfunction combineComparators(comparatorA, comparatorB) {\n    return function isEqual(a, b, state) {\n        return comparatorA(a, b, state) && comparatorB(a, b, state);\n    };\n}\n/**\n * Wrap the provided `areItemsEqual` method to manage the circular state, allowing\n * for circular references to be safely included in the comparison without creating\n * stack overflows.\n */\nfunction createIsCircular(areItemsEqual) {\n    return function isCircular(a, b, state) {\n        if (!a || !b || typeof a !== 'object' || typeof b !== 'object') {\n            return areItemsEqual(a, b, state);\n        }\n        var cache = state.cache;\n        var cachedA = cache.get(a);\n        var cachedB = cache.get(b);\n        if (cachedA && cachedB) {\n            return cachedA === b && cachedB === a;\n        }\n        cache.set(a, b);\n        cache.set(b, a);\n        var result = areItemsEqual(a, b, state);\n        cache.delete(a);\n        cache.delete(b);\n        return result;\n    };\n}\n/**\n * Get the properties to strictly examine, which include both own properties that are\n * not enumerable and symbol properties.\n */\nfunction getStrictProperties(object) {\n    return getOwnPropertyNames(object).concat(getOwnPropertySymbols(object));\n}\n/**\n * Whether the object contains the property passed as an own property.\n */\nvar hasOwn = Object.hasOwn ||\n    (function (object, property) {\n        return hasOwnProperty.call(object, property);\n    });\n/**\n * Whether the values passed are strictly equal or both NaN.\n */\nfunction sameValueZeroEqual(a, b) {\n    return a === b || (!a && !b && a !== a && b !== b);\n}\n\nvar PREACT_VNODE = '__v';\nvar PREACT_OWNER = '__o';\nvar REACT_OWNER = '_owner';\nvar getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor, keys = Object.keys;\n/**\n * Whether the arrays are equal in value.\n */\nfunction areArraysEqual(a, b, state) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (!state.equals(a[index], b[index], index, index, a, b, state)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the dates passed are equal in value.\n */\nfunction areDatesEqual(a, b) {\n    return sameValueZeroEqual(a.getTime(), b.getTime());\n}\n/**\n * Whether the errors passed are equal in value.\n */\nfunction areErrorsEqual(a, b) {\n    return (a.name === b.name &&\n        a.message === b.message &&\n        a.cause === b.cause &&\n        a.stack === b.stack);\n}\n/**\n * Whether the functions passed are equal in value.\n */\nfunction areFunctionsEqual(a, b) {\n    return a === b;\n}\n/**\n * Whether the `Map`s are equal in value.\n */\nfunction areMapsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n        return false;\n    }\n    if (!size) {\n        return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.entries();\n    var aResult;\n    var bResult;\n    var index = 0;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.entries();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            if (matchedIndices[matchIndex]) {\n                matchIndex++;\n                continue;\n            }\n            var aEntry = aResult.value;\n            var bEntry = bResult.value;\n            if (state.equals(aEntry[0], bEntry[0], index, matchIndex, a, b, state) &&\n                state.equals(aEntry[1], bEntry[1], aEntry[0], bEntry[0], a, b, state)) {\n                hasMatch = matchedIndices[matchIndex] = true;\n                break;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n        index++;\n    }\n    return true;\n}\n/**\n * Whether the numbers are equal in value.\n */\nvar areNumbersEqual = sameValueZeroEqual;\n/**\n * Whether the objects are equal in value.\n */\nfunction areObjectsEqual(a, b, state) {\n    var properties = keys(a);\n    var index = properties.length;\n    if (keys(b).length !== index) {\n        return false;\n    }\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        if (!isPropertyEqual(a, b, state, properties[index])) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the objects are equal in value with strict property checking.\n */\nfunction areObjectsEqualStrict(a, b, state) {\n    var properties = getStrictProperties(a);\n    var index = properties.length;\n    if (getStrictProperties(b).length !== index) {\n        return false;\n    }\n    var property;\n    var descriptorA;\n    var descriptorB;\n    // Decrementing `while` showed faster results than either incrementing or\n    // decrementing `for` loop and than an incrementing `while` loop. Declarative\n    // methods like `some` / `every` were not used to avoid incurring the garbage\n    // cost of anonymous callbacks.\n    while (index-- > 0) {\n        property = properties[index];\n        if (!isPropertyEqual(a, b, state, property)) {\n            return false;\n        }\n        descriptorA = getOwnPropertyDescriptor(a, property);\n        descriptorB = getOwnPropertyDescriptor(b, property);\n        if ((descriptorA || descriptorB) &&\n            (!descriptorA ||\n                !descriptorB ||\n                descriptorA.configurable !== descriptorB.configurable ||\n                descriptorA.enumerable !== descriptorB.enumerable ||\n                descriptorA.writable !== descriptorB.writable)) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the primitive wrappers passed are equal in value.\n */\nfunction arePrimitiveWrappersEqual(a, b) {\n    return sameValueZeroEqual(a.valueOf(), b.valueOf());\n}\n/**\n * Whether the regexps passed are equal in value.\n */\nfunction areRegExpsEqual(a, b) {\n    return a.source === b.source && a.flags === b.flags;\n}\n/**\n * Whether the `Set`s are equal in value.\n */\nfunction areSetsEqual(a, b, state) {\n    var size = a.size;\n    if (size !== b.size) {\n        return false;\n    }\n    if (!size) {\n        return true;\n    }\n    var matchedIndices = new Array(size);\n    var aIterable = a.values();\n    var aResult;\n    var bResult;\n    while ((aResult = aIterable.next())) {\n        if (aResult.done) {\n            break;\n        }\n        var bIterable = b.values();\n        var hasMatch = false;\n        var matchIndex = 0;\n        while ((bResult = bIterable.next())) {\n            if (bResult.done) {\n                break;\n            }\n            if (!matchedIndices[matchIndex] &&\n                state.equals(aResult.value, bResult.value, aResult.value, bResult.value, a, b, state)) {\n                hasMatch = matchedIndices[matchIndex] = true;\n                break;\n            }\n            matchIndex++;\n        }\n        if (!hasMatch) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the TypedArray instances are equal in value.\n */\nfunction areTypedArraysEqual(a, b) {\n    var index = a.length;\n    if (b.length !== index) {\n        return false;\n    }\n    while (index-- > 0) {\n        if (a[index] !== b[index]) {\n            return false;\n        }\n    }\n    return true;\n}\n/**\n * Whether the URL instances are equal in value.\n */\nfunction areUrlsEqual(a, b) {\n    return (a.hostname === b.hostname &&\n        a.pathname === b.pathname &&\n        a.protocol === b.protocol &&\n        a.port === b.port &&\n        a.hash === b.hash &&\n        a.username === b.username &&\n        a.password === b.password);\n}\nfunction isPropertyEqual(a, b, state, property) {\n    if ((property === REACT_OWNER ||\n        property === PREACT_OWNER ||\n        property === PREACT_VNODE) &&\n        (a.$$typeof || b.$$typeof)) {\n        return true;\n    }\n    return (hasOwn(b, property) &&\n        state.equals(a[property], b[property], property, property, a, b, state));\n}\n\nvar ARGUMENTS_TAG = '[object Arguments]';\nvar BOOLEAN_TAG = '[object Boolean]';\nvar DATE_TAG = '[object Date]';\nvar ERROR_TAG = '[object Error]';\nvar MAP_TAG = '[object Map]';\nvar NUMBER_TAG = '[object Number]';\nvar OBJECT_TAG = '[object Object]';\nvar REG_EXP_TAG = '[object RegExp]';\nvar SET_TAG = '[object Set]';\nvar STRING_TAG = '[object String]';\nvar URL_TAG = '[object URL]';\nvar isArray = Array.isArray;\nvar isTypedArray = typeof ArrayBuffer === 'function' && ArrayBuffer.isView\n    ? ArrayBuffer.isView\n    : null;\nvar assign = Object.assign;\nvar getTag = Object.prototype.toString.call.bind(Object.prototype.toString);\n/**\n * Create a comparator method based on the type-specific equality comparators passed.\n */\nfunction createEqualityComparator(_a) {\n    var areArraysEqual = _a.areArraysEqual, areDatesEqual = _a.areDatesEqual, areErrorsEqual = _a.areErrorsEqual, areFunctionsEqual = _a.areFunctionsEqual, areMapsEqual = _a.areMapsEqual, areNumbersEqual = _a.areNumbersEqual, areObjectsEqual = _a.areObjectsEqual, arePrimitiveWrappersEqual = _a.arePrimitiveWrappersEqual, areRegExpsEqual = _a.areRegExpsEqual, areSetsEqual = _a.areSetsEqual, areTypedArraysEqual = _a.areTypedArraysEqual, areUrlsEqual = _a.areUrlsEqual;\n    /**\n     * compare the value of the two objects and return true if they are equivalent in values\n     */\n    return function comparator(a, b, state) {\n        // If the items are strictly equal, no need to do a value comparison.\n        if (a === b) {\n            return true;\n        }\n        // If either of the items are nullish and fail the strictly equal check\n        // above, then they must be unequal.\n        if (a == null || b == null) {\n            return false;\n        }\n        var type = typeof a;\n        if (type !== typeof b) {\n            return false;\n        }\n        if (type !== 'object') {\n            if (type === 'number') {\n                return areNumbersEqual(a, b, state);\n            }\n            if (type === 'function') {\n                return areFunctionsEqual(a, b, state);\n            }\n            // If a primitive value that is not strictly equal, it must be unequal.\n            return false;\n        }\n        var constructor = a.constructor;\n        // Checks are listed in order of commonality of use-case:\n        //   1. Common complex object types (plain object, array)\n        //   2. Common data values (date, regexp)\n        //   3. Less-common complex object types (map, set)\n        //   4. Less-common data values (promise, primitive wrappers)\n        // Inherently this is both subjective and assumptive, however\n        // when reviewing comparable libraries in the wild this order\n        // appears to be generally consistent.\n        // Constructors should match, otherwise there is potential for false positives\n        // between class and subclass or custom object and POJO.\n        if (constructor !== b.constructor) {\n            return false;\n        }\n        // `isPlainObject` only checks against the object's own realm. Cross-realm\n        // comparisons are rare, and will be handled in the ultimate fallback, so\n        // we can avoid capturing the string tag.\n        if (constructor === Object) {\n            return areObjectsEqual(a, b, state);\n        }\n        // `isArray()` works on subclasses and is cross-realm, so we can avoid capturing\n        // the string tag or doing an `instanceof` check.\n        if (isArray(a)) {\n            return areArraysEqual(a, b, state);\n        }\n        // `isTypedArray()` works on all possible TypedArray classes, so we can avoid\n        // capturing the string tag or comparing against all possible constructors.\n        if (isTypedArray != null && isTypedArray(a)) {\n            return areTypedArraysEqual(a, b, state);\n        }\n        // Try to fast-path equality checks for other complex object types in the\n        // same realm to avoid capturing the string tag. Strict equality is used\n        // instead of `instanceof` because it is more performant for the common\n        // use-case. If someone is subclassing a native class, it will be handled\n        // with the string tag comparison.\n        if (constructor === Date) {\n            return areDatesEqual(a, b, state);\n        }\n        if (constructor === RegExp) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (constructor === Map) {\n            return areMapsEqual(a, b, state);\n        }\n        if (constructor === Set) {\n            return areSetsEqual(a, b, state);\n        }\n        // Since this is a custom object, capture the string tag to determing its type.\n        // This is reasonably performant in modern environments like v8 and SpiderMonkey.\n        var tag = getTag(a);\n        if (tag === DATE_TAG) {\n            return areDatesEqual(a, b, state);\n        }\n        // For RegExp, the properties are not enumerable, and therefore will give false positives if\n        // tested like a standard object.\n        if (tag === REG_EXP_TAG) {\n            return areRegExpsEqual(a, b, state);\n        }\n        if (tag === MAP_TAG) {\n            return areMapsEqual(a, b, state);\n        }\n        if (tag === SET_TAG) {\n            return areSetsEqual(a, b, state);\n        }\n        if (tag === OBJECT_TAG) {\n            // The exception for value comparison is custom `Promise`-like class instances. These should\n            // be treated the same as standard `Promise` objects, which means strict equality, and if\n            // it reaches this point then that strict equality comparison has already failed.\n            return (typeof a.then !== 'function' &&\n                typeof b.then !== 'function' &&\n                areObjectsEqual(a, b, state));\n        }\n        // If a URL tag, it should be tested explicitly. Like RegExp, the properties are not\n        // enumerable, and therefore will give false positives if tested like a standard object.\n        if (tag === URL_TAG) {\n            return areUrlsEqual(a, b, state);\n        }\n        // If an error tag, it should be tested explicitly. Like RegExp, the properties are not\n        // enumerable, and therefore will give false positives if tested like a standard object.\n        if (tag === ERROR_TAG) {\n            return areErrorsEqual(a, b, state);\n        }\n        // If an arguments tag, it should be treated as a standard object.\n        if (tag === ARGUMENTS_TAG) {\n            return areObjectsEqual(a, b, state);\n        }\n        // As the penultimate fallback, check if the values passed are primitive wrappers. This\n        // is very rare in modern JS, which is why it is deprioritized compared to all other object\n        // types.\n        if (tag === BOOLEAN_TAG || tag === NUMBER_TAG || tag === STRING_TAG) {\n            return arePrimitiveWrappersEqual(a, b, state);\n        }\n        // If not matching any tags that require a specific type of comparison, then we hard-code false because\n        // the only thing remaining is strict equality, which has already been compared. This is for a few reasons:\n        //   - Certain types that cannot be introspected (e.g., `WeakMap`). For these types, this is the only\n        //     comparison that can be made.\n        //   - For types that can be introspected, but rarely have requirements to be compared\n        //     (`ArrayBuffer`, `DataView`, etc.), the cost is avoided to prioritize the common\n        //     use-cases (may be included in a future release, if requested enough).\n        //   - For types that can be introspected but do not have an objective definition of what\n        //     equality is (`Error`, etc.), the subjective decision is to be conservative and strictly compare.\n        // In all cases, these decisions should be reevaluated based on changes to the language and\n        // common development practices.\n        return false;\n    };\n}\n/**\n * Create the configuration object used for building comparators.\n */\nfunction createEqualityComparatorConfig(_a) {\n    var circular = _a.circular, createCustomConfig = _a.createCustomConfig, strict = _a.strict;\n    var config = {\n        areArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areArraysEqual,\n        areDatesEqual: areDatesEqual,\n        areErrorsEqual: areErrorsEqual,\n        areFunctionsEqual: areFunctionsEqual,\n        areMapsEqual: strict\n            ? combineComparators(areMapsEqual, areObjectsEqualStrict)\n            : areMapsEqual,\n        areNumbersEqual: areNumbersEqual,\n        areObjectsEqual: strict\n            ? areObjectsEqualStrict\n            : areObjectsEqual,\n        arePrimitiveWrappersEqual: arePrimitiveWrappersEqual,\n        areRegExpsEqual: areRegExpsEqual,\n        areSetsEqual: strict\n            ? combineComparators(areSetsEqual, areObjectsEqualStrict)\n            : areSetsEqual,\n        areTypedArraysEqual: strict\n            ? areObjectsEqualStrict\n            : areTypedArraysEqual,\n        areUrlsEqual: areUrlsEqual,\n    };\n    if (createCustomConfig) {\n        config = assign({}, config, createCustomConfig(config));\n    }\n    if (circular) {\n        var areArraysEqual$1 = createIsCircular(config.areArraysEqual);\n        var areMapsEqual$1 = createIsCircular(config.areMapsEqual);\n        var areObjectsEqual$1 = createIsCircular(config.areObjectsEqual);\n        var areSetsEqual$1 = createIsCircular(config.areSetsEqual);\n        config = assign({}, config, {\n            areArraysEqual: areArraysEqual$1,\n            areMapsEqual: areMapsEqual$1,\n            areObjectsEqual: areObjectsEqual$1,\n            areSetsEqual: areSetsEqual$1,\n        });\n    }\n    return config;\n}\n/**\n * Default equality comparator pass-through, used as the standard `isEqual` creator for\n * use inside the built comparator.\n */\nfunction createInternalEqualityComparator(compare) {\n    return function (a, b, _indexOrKeyA, _indexOrKeyB, _parentA, _parentB, state) {\n        return compare(a, b, state);\n    };\n}\n/**\n * Create the `isEqual` function used by the consuming application.\n */\nfunction createIsEqual(_a) {\n    var circular = _a.circular, comparator = _a.comparator, createState = _a.createState, equals = _a.equals, strict = _a.strict;\n    if (createState) {\n        return function isEqual(a, b) {\n            var _a = createState(), _b = _a.cache, cache = _b === void 0 ? circular ? new WeakMap() : undefined : _b, meta = _a.meta;\n            return comparator(a, b, {\n                cache: cache,\n                equals: equals,\n                meta: meta,\n                strict: strict,\n            });\n        };\n    }\n    if (circular) {\n        return function isEqual(a, b) {\n            return comparator(a, b, {\n                cache: new WeakMap(),\n                equals: equals,\n                meta: undefined,\n                strict: strict,\n            });\n        };\n    }\n    var state = {\n        cache: undefined,\n        equals: equals,\n        meta: undefined,\n        strict: strict,\n    };\n    return function isEqual(a, b) {\n        return comparator(a, b, state);\n    };\n}\n\n/**\n * Whether the items passed are deeply-equal in value.\n */\nvar deepEqual = createCustomEqual();\n/**\n * Whether the items passed are deeply-equal in value based on strict comparison.\n */\nvar strictDeepEqual = createCustomEqual({ strict: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references.\n */\nvar circularDeepEqual = createCustomEqual({ circular: true });\n/**\n * Whether the items passed are deeply-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularDeepEqual = createCustomEqual({\n    circular: true,\n    strict: true,\n});\n/**\n * Whether the items passed are shallowly-equal in value.\n */\nvar shallowEqual = createCustomEqual({\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value based on strict comparison\n */\nvar strictShallowEqual = createCustomEqual({\n    strict: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references.\n */\nvar circularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n});\n/**\n * Whether the items passed are shallowly-equal in value, including circular references,\n * based on strict comparison.\n */\nvar strictCircularShallowEqual = createCustomEqual({\n    circular: true,\n    createInternalComparator: function () { return sameValueZeroEqual; },\n    strict: true,\n});\n/**\n * Create a custom equality comparison method.\n *\n * This can be done to create very targeted comparisons in extreme hot-path scenarios\n * where the standard methods are not performant enough, but can also be used to provide\n * support for legacy environments that do not support expected features like\n * `RegExp.prototype.flags` out of the box.\n */\nfunction createCustomEqual(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.circular, circular = _a === void 0 ? false : _a, createCustomInternalComparator = options.createInternalComparator, createState = options.createState, _b = options.strict, strict = _b === void 0 ? false : _b;\n    var config = createEqualityComparatorConfig(options);\n    var comparator = createEqualityComparator(config);\n    var equals = createCustomInternalComparator\n        ? createCustomInternalComparator(comparator)\n        : createInternalEqualityComparator(comparator);\n    return createIsEqual({ circular: circular, comparator: comparator, createState: createState, equals: equals, strict: strict });\n}\n\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/fast-equals/dist/esm/index.mjs\n");

/***/ })

};
;