
import React from 'react';
import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
  Section,
} from '@react-email/components';

interface ApplicationSubmittedEmailProps {
  applicantName: string;
  courseName: string;
  applicationDate: string;
  applicationId: string;
}

const ApplicationSubmittedEmail: React.FC<ApplicationSubmittedEmailProps> = ({
  applicantName,
  courseName,
  applicationDate,
  applicationId,
}) => {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9002';
  
  return (
    <Html>
      <Head />
      <Preview>New Student Application Received: {applicantName}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={heading}>New Application Received</Heading>
          <Text style={paragraph}>A new application has been submitted on the EduLite portal.</Text>
          <Section style={detailsSection}>
            <Text style={detailText}><strong>Applicant:</strong> {applicantName}</Text>
            <Text style={detailText}><strong>Course:</strong> {courseName}</Text>
            <Text style={detailText}><strong>Application Date:</strong> {new Date(applicationDate).toLocaleDateString()}</Text>
          </Section>
          <Button
            style={button}
            href={`${baseUrl}/applications`}
          >
            View Application
          </Button>
          <Text style={footer}>
            Application ID: {applicationId}
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default ApplicationSubmittedEmail;

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  border: '1px solid #f0f0f0',
  borderRadius: '4px',
};

const heading = {
  fontSize: '28px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  color: '#3F51B5', // Primary color
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '24px',
  textAlign: 'center' as const,
  color: '#525f7f',
  padding: '0 40px',
};

const detailsSection = {
  padding: '20px',
  margin: '20px 40px',
  backgroundColor: '#f1f4f8',
  borderRadius: '4px',
};

const detailText = {
  fontSize: '14px',
  lineHeight: '1.5',
  color: '#333',
  margin: '0 0 10px 0',
};

const button = {
  backgroundColor: '#009688', // Accent color
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '200px',
  padding: '12px',
  margin: '0 auto',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  textAlign: 'center' as const,
  marginTop: '20px',
};
