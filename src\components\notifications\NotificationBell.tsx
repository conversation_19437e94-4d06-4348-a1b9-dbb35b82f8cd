
'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
} from '@/actions/notificationActions';
import type { Notification } from '@/types';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>etHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>ooter,
  SheetTrigger,
} from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Bell, BellRing, CheckCheck, Loader2 } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';
import { auth } from '@/lib/firebase';

export function NotificationBell() {
  const { user } = useAuth();
  const { toast } = useToast();
  const router = useRouter();

  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isOpen, setIsOpen] = useState(false);

  const fetchNotifications = useCallback(async () => {
    if (!user || !auth.currentUser) return;
    setIsLoading(true);
    try {
      const idToken = await auth.currentUser.getIdToken();
      const fetchedNotifications = await getNotifications(idToken);
      setNotifications(fetchedNotifications);
      setUnreadCount(fetchedNotifications.filter(n => !n.read).length);
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
      // Don't show toast for this as it might be annoying on every poll
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    if (user) {
        fetchNotifications();
        // Poll for new notifications every minute
        const interval = setInterval(fetchNotifications, 60000);
        return () => clearInterval(interval);
    }
  }, [user, fetchNotifications]);

  const handleNotificationClick = async (notification: Notification) => {
    // Optimistically close sheet and navigate
    setIsOpen(false);
    if (notification.link) {
      router.push(notification.link);
    }
    
    if (!notification.read) {
      // Optimistically update UI
      const previousNotifications = [...notifications];
      setNotifications(prev => prev.map(n => n.id === notification.id ? { ...n, read: true } : n));
      setUnreadCount(prev => Math.max(0, prev - 1));

      try {
        const idToken = await auth.currentUser?.getIdToken();
        if (!idToken) throw new Error("Not authenticated");
        await markNotificationAsRead(notification.id, idToken);
      } catch (error) {
        // Revert optimistic update on API call failure
        setNotifications(previousNotifications);
        setUnreadCount(previousNotifications.filter(n => !n.read).length);
        console.error('Failed to mark notification as read:', error);
      }
    }
  };

  const handleMarkAllAsRead = async () => {
    if (!user || unreadCount === 0 || !auth.currentUser) return;
    const previousNotifications = [...notifications];
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
    setUnreadCount(0);
    try {
      const idToken = await auth.currentUser.getIdToken();
      const result = await markAllNotificationsAsRead(idToken);
      if (result.success) {
        toast({ title: 'Success', description: `Marked ${result.count} notifications as read.` });
      }
    } catch (error) {
      setNotifications(previousNotifications);
      setUnreadCount(previousNotifications.filter(n => !n.read).length);
      toast({ title: 'Error', description: 'Could not mark all as read.', variant: 'destructive' });
    }
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="relative h-10 w-10 rounded-full">
          {unreadCount > 0 ? (
            <BellRing className="h-5 w-5 animate-pulse text-accent" />
          ) : (
            <Bell className="h-5 w-5" />
          )}
          {unreadCount > 0 && (
            <span className="absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-full bg-destructive text-xs font-bold text-destructive-foreground">
              {unreadCount}
            </span>
          )}
          <span className="sr-only">Toggle Notifications</span>
        </Button>
      </SheetTrigger>
      <SheetContent className="flex flex-col p-0">
        <SheetHeader className="border-b p-6 pb-4">
          <SheetTitle>Notifications</SheetTitle>
        </SheetHeader>
        {isLoading && notifications.length === 0 ? (
          <div className="flex flex-1 items-center justify-center">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : notifications.length === 0 ? (
          <div className="flex flex-1 items-center justify-center p-6 text-center text-muted-foreground">
            <div>
              <Bell className="mx-auto h-12 w-12 mb-4" />
              <p className="text-lg font-semibold">No notifications yet</p>
              <p>We'll let you know when there's news for you.</p>
            </div>
          </div>
        ) : (
          <ScrollArea className="flex-1">
            <div className="divide-y divide-border">
              {notifications.map(notification => (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={cn(
                    'flex items-start gap-4 p-4 transition-colors cursor-pointer hover:bg-muted/50',
                    !notification.read && 'bg-primary/5'
                  )}
                >
                  <div className={cn(
                      'mt-1.5 h-2 w-2 shrink-0 rounded-full',
                      notification.read ? 'bg-transparent' : 'bg-primary animate-pulse'
                  )}/>
                  <div className="flex-grow">
                    <p className="text-sm">{notification.message}</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
        <SheetFooter className="border-t p-4">
          <Button
            onClick={handleMarkAllAsRead}
            variant="outline"
            className="w-full"
            disabled={unreadCount === 0 || isLoading}
          >
            <CheckCheck className="mr-2 h-4 w-4" /> Mark all as read
          </Button>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
}
