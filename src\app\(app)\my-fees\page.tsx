
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import type { StudentFee, FeeStatus, User } from '@/types';
import { getStudentFeesByStudentId } from '@/actions/feeActions';
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Loader2, FileText, CheckCircle, XCircle, Clock, AlertCircle, CircleDollarSign, PiggyBank, BarChartHorizontal } from 'lucide-react';
import { format } from 'date-fns';

const RupeeIcon = (props: React.SVGProps<SVGSVGElement>) => (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M6 3h12" /><path d="M6 8h12" /><path d="M6 13h12" /><path d="M6 18h12" /><path d="M6 3v18" /><path d="M18 3v18" /><path d="M9 3v18" /><path d="M15 3v18" /><path d="M9.5 8C7.015 8 5 10.015 5 12.5S7.015 17 9.5 17h5c2.485 0 4.5-2.015 4.5-4.5S16.985 8 14.5 8z" transform="matrix(1 0 0 1 -0.5 -1)"/><path d="M6 3h12"></path><path d="M6 8h12"></path><path d="M9.5 13H18"></path></svg>
);

const statusVariantMap: Record<FeeStatus, 'default' | 'secondary' | 'outline' | 'destructive'> = {
  Pending: 'default',
  'Partially Paid': 'secondary',
  Paid: 'outline',
  Overdue: 'destructive',
};

const statusIconMap: Record<FeeStatus, React.ElementType> = {
    Pending: Clock,
    'Partially Paid': AlertCircle,
    Paid: CheckCircle,
    Overdue: XCircle,
};

export default function MyFeesPage() {
    const { user, isLoading: authLoading } = useAuth();
    const { toast } = useToast();
    const [fees, setFees] = useState<StudentFee[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const fetchFees = useCallback(async () => {
        if (!user) return;
        setIsLoading(true);
        try {
            if (!auth.currentUser) throw new Error("Authentication required.");
            const idToken = await auth.currentUser.getIdToken();
            const fetchedFees = await getStudentFeesByStudentId(user.uid, idToken);
            setFees(fetchedFees.sort((a,b) => new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime()));
        } catch (error) {
            toast({
                title: "Error",
                description: error instanceof Error ? error.message : "Could not fetch your fee records.",
                variant: "destructive",
            });
        } finally {
            setIsLoading(false);
        }
    }, [user, toast]);

    useEffect(() => {
        if (!authLoading && user) {
            fetchFees();
        } else if (!authLoading && !user) {
            setIsLoading(false);
        }
    }, [authLoading, user, fetchFees]);

    const summary = React.useMemo(() => {
        const totalDue = fees.reduce((sum, fee) => sum + fee.amountDue, 0);
        const totalPaid = fees.reduce((sum, fee) => sum + fee.amountPaid, 0);
        const outstanding = totalDue - totalPaid;
        return { totalDue, totalPaid, outstanding };
    }, [fees]);

    if (isLoading || authLoading) {
        return (
            <div className="space-y-6">
                <div>
                    <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">My Fees</h1>
                    <p className="text-muted-foreground">Review your fee history and payment status.</p>
                </div>
                <div className="grid gap-4 md:grid-cols-3">
                    <Card><CardHeader><Skeleton className="h-5 w-24" /></CardHeader><CardContent><Skeleton className="h-8 w-32" /></CardContent></Card>
                    <Card><CardHeader><Skeleton className="h-5 w-24" /></CardHeader><CardContent><Skeleton className="h-8 w-32" /></CardContent></Card>
                    <Card><CardHeader><Skeleton className="h-5 w-24" /></CardHeader><CardContent><Skeleton className="h-8 w-32" /></CardContent></Card>
                </div>
                <Card className="shadow-lg">
                    <CardHeader>
                        <CardTitle><Skeleton className="h-7 w-48" /></CardTitle>
                        <CardDescription><Skeleton className="h-4 w-72" /></CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                          {[...Array(3)].map((_, i) => <Skeleton key={i} className="h-10 w-full" />)}
                      </div>
                    </CardContent>
                </Card>
            </div>
        );
    }
    
    return (
        <div className="space-y-6">
            <div>
                <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">My Fees</h1>
                <p className="text-muted-foreground">Review your fee history and payment status.</p>
            </div>
            
            <div className="grid gap-4 md:grid-cols-3">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Fees</CardTitle>
                        <CircleDollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">₹{summary.totalDue.toFixed(2)}</div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Paid</CardTitle>
                        <PiggyBank className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-green-600">₹{summary.totalPaid.toFixed(2)}</div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Outstanding Balance</CardTitle>
                        <BarChartHorizontal className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold text-destructive">₹{summary.outstanding.toFixed(2)}</div>
                    </CardContent>
                </Card>
            </div>

            <Card className="shadow-lg">
                <CardHeader>
                    <CardTitle className="font-headline text-xl">Fee Records</CardTitle>
                    <CardDescription>A list of all fee items for your enrolled courses.</CardDescription>
                </CardHeader>
                <CardContent>
                    {fees.length === 0 ? (
                        <div className="text-center py-10 text-muted-foreground">
                            <RupeeIcon className="mx-auto h-12 w-12 mb-4" />
                            <p className="text-xl font-semibold">No fee records found.</p>
                            <p>Once your application is accepted, fee records will appear here.</p>
                        </div>
                    ) : (
                        <div className="rounded-lg border overflow-hidden">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Course</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead>Due Date</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Amount (₹)</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {fees.map((fee) => {
                                        const StatusIcon = statusIconMap[fee.status] || AlertCircle;
                                        
                                        return (
                                            <TableRow key={fee.id} className={fee.status === 'Overdue' && (fee.amountDue - fee.amountPaid > 0) ? 'bg-destructive/5 hover:bg-destructive/10' : ''}>
                                                <TableCell className="font-medium">{fee.courseName}</TableCell>
                                                <TableCell>{fee.feeDescription}</TableCell>
                                                <TableCell>{format(new Date(fee.dueDate), "PP")}</TableCell>
                                                <TableCell>
                                                    <Badge variant={statusVariantMap[fee.status] || 'default'} className="flex items-center gap-1.5 capitalize">
                                                        <StatusIcon className="h-3.5 w-3.5" />
                                                        {fee.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-right font-semibold">
                                                  ₹{fee.amountDue.toFixed(2)}
                                                </TableCell>
                                                <TableCell className="text-right space-x-2">
                                                    <Button asChild variant="secondary" size="sm">
                                                        <Link href={`/invoice/${fee.id}`}>
                                                            <FileText className="mr-2 h-4 w-4" />
                                                            View Receipt
                                                        </Link>
                                                    </Button>
                                                </TableCell>
                                            </TableRow>
                                        );
                                    })}
                                </TableBody>
                            </Table>
                        </div>
                    )}
                </CardContent>
            </Card>
        </div>
    );
}
