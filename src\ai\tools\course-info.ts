
'use server';
/**
 * @fileOverview Genkit tool to retrieve information about available courses.
 */

import { ai } from '@/ai/genkit';
import { getCourses } from '@/actions/courseActions';
import { z } from 'zod';

export const getCourseInfoTool = ai.defineTool(
  {
    name: 'getCourseInfo',
    description: 'Get details about a specific course offered by the institution, such as its name, code, instructor, dates, and the subjects, units, and study materials included in it. Use this if the user asks for details, subjects, or curriculum content about a specific course.',
    inputSchema: z.object({
      courseIdentifier: z.string().describe('The name or code of the course, e.g., "Bachelor of Computer Applications" or "BCA".'),
    }),
    outputSchema: z.union([
      z.object({
        name: z.string(),
        code: z.string(),
        instructor: z.string(),
        specializations: z.array(z.string()).optional(),
        startDate: z.string().optional(),
        endDate: z.string().optional(),
        semestersCount: z.number(),
        subjects: z.array(z.object({
            name: z.string(),
            credits: z.number(),
            semester: z.number(),
            units: z.array(z.object({
                name: z.string(),
                materials: z.array(z.object({
                    name: z.string(),
                    type: z.string(),
                    url: z.string().optional(),
                    content: z.string().optional(),
                })).optional(),
            })).optional(),
        })).optional(),
      }),
      z.object({ error: z.string() })
    ]),
  },
  async ({ courseIdentifier }) => {
    try {
        const allCourses = await getCourses();
        const normalizedIdentifier = courseIdentifier.toLowerCase().trim();
        
        const course = allCourses.find(c => 
            c.name.toLowerCase().includes(normalizedIdentifier) || 
            c.code.toLowerCase() === normalizedIdentifier
        );

        if (!course) {
          return { error: `Course "${courseIdentifier}" not found.` };
        }

        const subjects = course.semesters?.flatMap(semester => 
            semester.subjects.map(subject => ({
                name: subject.name,
                credits: subject.credits,
                semester: semester.semesterNumber,
                units: subject.units?.map(unit => ({
                    name: unit.name,
                    materials: unit.materials?.map(material => ({
                        name: material.name,
                        type: material.type,
                         ...(material.url && { url: material.url }),
                         ...(material.content && { content: material.content }),
                    })) || [],
                })) || [],
            }))
        ) || [];

        return {
          name: course.name,
          code: course.code,
          instructor: course.instructor,
          specializations: course.specializations,
          startDate: course.startDate,
          endDate: course.endDate,
          semestersCount: course.semesters?.length || 0,
          subjects: subjects,
        };
    } catch (e: any) {
        console.error("Error in getCourseInfoTool:", e);
        return { error: `An internal error occurred while fetching course information.` };
    }
  }
);
