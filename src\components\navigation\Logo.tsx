
'use client';

import Link from 'next/link';
import { GraduationCap, Loader2 } from 'lucide-react';
import { getSystemSettings } from '@/actions/settingsActions';
import { useEffect, useState } from 'react';
import type { SystemSettings } from '@/types';
import Image from 'next/image';

export function Logo({ size = 'md' }: { size?: 'sm' | 'md' | 'lg' }) {
  const [settings, setSettings] = useState<SystemSettings | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchSettings() {
      try {
        const fetchedSettings = await getSystemSettings();
        setSettings(fetchedSettings);
      } catch (error) {
        console.error("Failed to fetch settings for logo:", error);
      } finally {
        setLoading(false);
      }
    }
    fetchSettings();
  }, []);


  const sizeClasses = {
    sm: { container: 'h-6 w-6', text: 'text-xl' },
    md: { container: 'h-7 w-7', text: 'text-2xl' },
    lg: { container: 'h-8 w-8', text: 'text-3xl' },
  };

  const name = settings?.institutionName.split(' ')[0] || 'EduLite';

  return (
    <Link href="/" className="flex items-center gap-2 text-primary hover:opacity-80 transition-opacity">
      <div className={`relative ${sizeClasses[size].container}`}>
        {loading ? (
           <Loader2 className={`animate-spin text-accent ${sizeClasses[size].container}`} />
        ) : settings?.logoUrl ? (
          <Image
            src={settings.logoUrl}
            alt={`${name} Logo`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 160px"
          />
        ) : (
          <GraduationCap className={`text-accent ${sizeClasses[size].container}`} />
        )}
      </div>
      <span className={`font-headline font-semibold ${sizeClasses[size].text}`}>{name}</span>
    </Link>
  );
}
