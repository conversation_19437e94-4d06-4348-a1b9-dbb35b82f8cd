
'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogClose,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import type { Course } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, Edit, CalendarIcon } from 'lucide-react';
import React from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';

const courseFormSchema = z.object({
  name: z.string().min(3, { message: 'Course name must be at least 3 characters.' }),
  code: z.string().min(2, { message: 'Course code must be at least 2 characters.' }),
  specializations: z.string().optional(),
  instructor: z.string().min(3, { message: 'Instructor name must be at least 3 characters.' }),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
});

type CourseFormValues = z.infer<typeof courseFormSchema>;

interface CourseDialogProps {
  course?: Course | null; // For editing
  onSave: (courseData: Course | Omit<Course, 'id'>) => void;
  triggerButton?: React.ReactNode;
}

export function CourseDialog({ course, onSave, triggerButton }: CourseDialogProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = React.useState(false);

  const form = useForm<CourseFormValues>({
    resolver: zodResolver(courseFormSchema),
    defaultValues: course
      ? { 
          ...course, 
          specializations: course.specializations?.join(', ') || '',
          startDate: course.startDate ? new Date(course.startDate) : undefined,
          endDate: course.endDate ? new Date(course.endDate) : undefined,
        }
      : {
          name: '',
          code: '',
          specializations: '',
          instructor: '',
          startDate: undefined,
          endDate: undefined,
        },
  });

  React.useEffect(() => {
    if (isOpen) {
      if (course) {
        form.reset({ 
          ...course, 
          specializations: course.specializations?.join(', ') || '',
          startDate: course.startDate ? new Date(course.startDate) : undefined,
          endDate: course.endDate ? new Date(course.endDate) : undefined,
        });
      } else {
        form.reset({
          name: '',
          code: '',
          specializations: '',
          instructor: '',
          startDate: undefined,
          endDate: undefined,
        });
      }
    }
  }, [course, form, isOpen]);


  function onSubmit(data: CourseFormValues) {
    const specializationsArray = data.specializations
      ? data.specializations.split(',').map(s => s.trim()).filter(Boolean)
      : [];

    const coursePayload = {
      ...data,
      specializations: specializationsArray,
      startDate: data.startDate ? format(data.startDate, 'yyyy-MM-dd') : undefined,
      endDate: data.endDate ? format(data.endDate, 'yyyy-MM-dd') : undefined,
    };

    if (course) {
      onSave({ ...course, ...coursePayload });
    } else {
      onSave({ ...coursePayload, semesters: [] });
    }

    toast({
      title: course ? 'Course Updated!' : 'Course Created!',
      description: `The course "${data.name}" has been successfully ${course ? 'updated' : 'created'}.`,
    });
    setIsOpen(false); 
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {triggerButton ? (
          triggerButton
        ) : course ? (
          <Button variant="ghost" size="sm"><Edit className="mr-2 h-4 w-4" /> Edit</Button>
        ) : (
          <Button className="bg-accent hover:bg-accent/90 text-accent-foreground">
            <PlusCircle className="mr-2 h-4 w-4" /> Add New Course
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[625px]">
        <DialogHeader>
          <DialogTitle className="font-headline text-2xl">{course ? 'Edit Course' : 'Add New Course'}</DialogTitle>
          <DialogDescription>
            {course ? 'Update the details for this course.' : 'Fill in the details for the new course.'}
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 py-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Course Name</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., Bachelor of Computer Applications" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="code"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Course Code</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g., BCA" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="specializations"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Specializations</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Software Development, AI, Data Science" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter multiple specializations separated by commas.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
             <FormField
                control={form.control}
                name="instructor"
                render={({ field }) => (
                    <FormItem>
                    <FormLabel>Lead Instructor</FormLabel>
                    <FormControl>
                        <Input placeholder="e.g., Dr. Jane Smith" {...field} />
                    </FormControl>
                    <FormMessage />
                    </FormItem>
                )}
                />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date (Optional)</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date (Optional)</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => 
                            form.getValues("startDate") ? date < form.getValues("startDate")! : false
                          }
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="outline">Cancel</Button>
              </DialogClose>
              <Button type="submit" className="bg-primary hover:bg-primary/90 text-primary-foreground">
                {course ? 'Save Changes' : 'Create Course'}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
