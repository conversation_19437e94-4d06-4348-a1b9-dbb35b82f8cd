
'use client';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import type { StudentApplication, Course, EducationDetails } from '@/types';
import { format } from 'date-fns';
import type React from 'react';

interface ApplicationDetailsDialogProps {
  application: StudentApplication | null;
  courses: Course[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const educationLevelLabels: Record<keyof EducationDetails, string> = {
  tenthCompletionYear: '10th Grade Completion Year',
  twelfthCompletionYear: '12th Grade Completion Year',
  diplomaCompletionYear: 'Diploma Completion Year',
  graduateCompletionYear: "Bachelor's Degree Completion Year",
  mastersCompletionYear: "Master's Degree Completion Year",
};


export function ApplicationDetailsDialog({ application, courses, open, onOpenChange }: ApplicationDetailsDialogProps) {
  if (!application) return null;

  const getCourseName = (courseId: string) => {
    return courses.find(c => c.id === courseId)?.name || courseId;
  };

  const renderDetailItem = (label: string, value?: React.ReactNode, itemKey?: string) => {
    if (value === undefined || value === null || (typeof value === 'string' && !value.trim()) || (Array.isArray(value) && value.length === 0)) {
      return null;
    }
    return (
      <div key={itemKey} className="py-3 sm:grid sm:grid-cols-3 sm:gap-4">
        <dt className="text-sm font-medium text-muted-foreground">{label}</dt>
        <dd className="mt-1 text-sm text-foreground sm:col-span-2 sm:mt-0">
          {Array.isArray(value) ? value.join(', ') : value}
        </dd>
      </div>
    );
  };

  const renderEducationDetails = (details?: EducationDetails) => {
    if (!details) return null;
    const items = Object.entries(details)
      .map(([objectKey, value]) => {
        if (value && educationLevelLabels[objectKey as keyof EducationDetails]) {
          return renderDetailItem(educationLevelLabels[objectKey as keyof EducationDetails], value, objectKey);
        }
        return null;
      })
      .filter(Boolean);
    
    return items.length > 0 ? <>{items}</> : renderDetailItem("Education Details", "Not provided", "edu-details-fallback");
  };


  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {/* 
        Using CSS Grid for a robust header-content-footer layout.
        grid-rows-[auto_1fr_auto] makes the content area (1fr) take all available space.
        max-h-[90vh] ensures the dialog doesn't exceed the viewport height.
        p-0 on DialogContent to allow fine-grained padding on children.
      */}
      <DialogContent className="sm:max-w-2xl max-h-[90vh] grid grid-rows-[auto_1fr_auto] p-0">
        <DialogHeader className="p-6 pb-4 flex-shrink-0 border-b">
          <DialogTitle className="font-headline text-2xl text-primary">Application Details</DialogTitle>
          <DialogDescription>
            Full information for applicant: <span className="font-semibold">{application.fullName}</span>
          </DialogDescription>
        </DialogHeader>
        
        {/*
          overflow-y-auto enables scrolling ONLY for this content area when it overflows.
          The header and footer remain fixed.
        */}
        <div className="overflow-y-auto p-6">
          <dl className="divide-y divide-border">
            {renderDetailItem("Applicant ID (Internal)", application.id, "app-id")}
            {renderDetailItem("Full Name", application.fullName, "app-fullName")}
            {renderDetailItem("Email Address", application.email, "app-email")}
            {renderDetailItem("Mobile Number", application.mobileNumber, "app-mobile")}
            {renderDetailItem("Status", <Badge variant={application.status === 'Accepted' ? 'outline' : application.status === 'Rejected' ? 'destructive' : 'default'}>{application.status}</Badge>, "app-status")}
            
            <div className="pt-3">
              <h3 className="text-md font-semibold text-primary mb-2 mt-3">Personal Information</h3>
              {renderDetailItem("Mother's Name", application.motherName, "app-motherName")}
              {renderDetailItem("Father's Name", application.fatherName, "app-fatherName")}
              {renderDetailItem("Date of Birth", format(new Date(application.dateOfBirth), 'PPP'), "app-dob")}
              {renderDetailItem("Religion", application.religion, "app-religion")}
            </div>

            <div className="pt-3">
              <h3 className="text-md font-semibold text-primary mb-2 mt-3">Application Data</h3>
              {renderDetailItem("Desired Course", getCourseName(application.desiredCourse), "app-desiredCourse")}
              {application.specialization && renderDetailItem("Specialization", application.specialization, "app-specialization")}
              {renderDetailItem("Application Date", format(new Date(application.applicationDate), 'PPP'), "app-appDate")}
              {renderDetailItem("Reference", application.reference, "app-reference")}
              {application.userId && renderDetailItem("Registered User ID", application.userId, "app-userId")}
            </div>
            
            <div className="pt-3">
              <h3 className="text-md font-semibold text-primary mb-2 mt-3">Education History</h3>
              {renderDetailItem("Selected Qualifications", application.previousEducation.join(', '), "app-prevEdu")}
              {renderEducationDetails(application.educationDetails)}
            </div>
          </dl>
        </div>
        
        <div className="p-6 pt-4 border-t flex-shrink-0 flex justify-end">
            <DialogClose asChild>
                <Button type="button" variant="outline">Close</Button>
            </DialogClose>
        </div>
      </DialogContent>
    </Dialog>
  );
}
