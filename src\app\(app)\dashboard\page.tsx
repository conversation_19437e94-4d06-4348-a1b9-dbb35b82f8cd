
'use client';
import React, { useState, useEffect, useCallback, lazy, Suspense } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { PlusCircle, Download, Users, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Filter } from 'lucide-react';
import { getLeads, createLead } from '@/actions/leadActions';
import { getCourses } from '@/actions/courseActions';
import type { Lead, Course } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Lazy load heavy components
const LeadTable = lazy(() => import('@/components/dashboard/LeadTable').then(module => ({ default: module.LeadTable })));
const LeadDialog = lazy(() => import('@/components/leads/LeadDialog').then(module => ({ default: module.LeadDialog })));
const LeadsByStatusChart = lazy(() => import('@/components/dashboard/LeadsByStatusChart').then(module => ({ default: module.LeadsByStatusChart })));

const leadStatuses: Lead['status'][] = ['New', 'Contacted', 'Qualified', 'Lost'];

export default function DashboardPage() {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLeadDialogOpen, setIsLeadDialogOpen] = useState(false);
  const [filterStatus, setFilterStatus] = useState<Lead['status'] | 'all'>('all');
  const { toast } = useToast();
  const { user } = useAuth();

  const fetchPageData = useCallback(async () => {
    if (!user) return;
    setIsLoading(true);
    try {
      const idToken = await auth.currentUser?.getIdToken();
      if (!idToken) throw new Error("Authentication token not available.");

      const [fetchedLeads, fetchedCourses] = await Promise.all([
        getLeads(idToken),
        getCourses()
      ]);

      setLeads(fetchedLeads);
      setCourses(fetchedCourses);
    } catch (error) {
      console.error("Failed to fetch page data:", error);
      toast({
        title: "Error",
        description: "Could not fetch dashboard data. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast, user]);

  useEffect(() => {
    fetchPageData();
  }, [fetchPageData]);

  const handleCreateLead = async (leadData: Omit<Lead, 'id' | 'inquiryDate' | 'status'>) => {
    try {
      await createLead(leadData);
      fetchPageData(); // Re-fetch to show the new lead and update stats
    } catch (error) {
      console.error("Error creating lead from dashboard page:", error);
      toast({
        title: "Error",
        description: "Could not create lead.",
        variant: "destructive",
      });
    }
  };

  const exportLeadsToCSV = () => {
    if (leads.length === 0) {
        toast({ title: "No leads to export", variant: "default" });
        return;
    }

    const headers = ['ID', 'Name', 'Email', 'Phone', 'Desired Course', 'Inquiry Date', 'Source', 'Status', 'Notes'];
    const csvRows = [headers.join(',')];
    const coursesMap = new Map(courses.map(c => [c.id, c.name]));

    for (const lead of leads) {
        const values = [
            lead.id,
            `"${lead.name.replace(/"/g, '""')}"`,
            `"${lead.email}"`,
            `"${lead.phone}"`,
            `"${lead.desiredCourse ? coursesMap.get(lead.desiredCourse) || lead.desiredCourse : ''}"`,
            lead.inquiryDate,
            `"${lead.source}"`,
            lead.status,
            `"${(lead.notes || '').replace(/"/g, '""').replace(/\n/g, ' ')}"`
        ];
        csvRows.push(values.join(','));
    }

    const csvString = csvRows.join('\n');
    const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `edulite_leads_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const filteredLeads = leads.filter(lead => {
    if (filterStatus === 'all') {
      return true;
    }
    return lead.status === filterStatus;
  });


  return (
    <>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-headline font-bold tracking-tight text-primary">Lead Management</h1>
            <p className="text-muted-foreground">View and manage student inquiries and leads.</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={exportLeadsToCSV} disabled={isLoading || leads.length === 0}>
              <Download className="mr-2 h-4 w-4" /> Export Leads
            </Button>
            <Button
              className="bg-accent hover:bg-accent/90 text-accent-foreground"
              onClick={() => setIsLeadDialogOpen(true)}
            >
              <PlusCircle className="mr-2 h-4 w-4" /> Add New Lead
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Inquiries</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{isLoading ? <Skeleton className="h-7 w-12" /> : leads.length}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Qualified Leads</CardTitle>
                <UserCheck className="h-4 w-4 text-accent" />
            </CardHeader>
            <CardContent>
                <div className="text-2xl font-bold">
                    {isLoading ? <Skeleton className="h-7 w-12" /> :
                    leads.filter(l => l.status === 'Qualified').length
                    }
                </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Leads Today</CardTitle>
              <Users className="h-4 w-4 text-primary" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {isLoading ? <Skeleton className="h-7 w-12" /> :
                  leads.filter(l => l.inquiryDate === new Date().toISOString().split('T')[0] && l.status === 'New').length
                }
              </div>
            </CardContent>
          </Card>
        </div>

        <Card className="shadow-lg">
            <CardHeader>
                <CardTitle className="font-headline text-xl">Leads by Status</CardTitle>
                <CardDescription>A visual summary of lead statuses.</CardDescription>
            </CardHeader>
            <CardContent className="pl-2">
                <Suspense fallback={<Skeleton className="h-[300px] w-full" />}>
                    <LeadsByStatusChart leads={leads} isLoading={isLoading} />
                </Suspense>
            </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="font-headline text-xl">Current Leads</CardTitle>
              <CardDescription>A list of all active leads and inquiries.</CardDescription>
            </div>
            <div className="w-full sm:w-48">
               <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value as Lead['status'] | 'all')}>
                <SelectTrigger>
                    <div className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        <SelectValue placeholder="Filter by status..." />
                    </div>
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {leadStatuses.map(status => (
                    <SelectItem key={status} value={status}>{status}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<Skeleton className="h-[400px] w-full" />}>
                <LeadTable
                  leads={filteredLeads}
                  totalLeadsCount={leads.length}
                  courses={courses}
                  isLoading={isLoading}
                  onLeadUpdated={fetchPageData}
                  onLeadDeleted={fetchPageData}
                />
            </Suspense>
          </CardContent>
        </Card>

      </div>

      <Suspense fallback={null}>
          <LeadDialog
            open={isLeadDialogOpen}
            onOpenChange={setIsLeadDialogOpen}
            onSave={handleCreateLead}
            formType="create"
            courses={courses}
          />
      </Suspense>
    </>
  );
}
