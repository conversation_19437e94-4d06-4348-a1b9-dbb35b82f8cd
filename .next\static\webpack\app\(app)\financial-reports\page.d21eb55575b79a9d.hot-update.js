"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(app)/financial-reports/page",{

/***/ "(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx":
/*!**************************************************!*\
  !*** ./src/app/(app)/financial-reports/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FinancialReportsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _actions_feeActions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/actions/feeActions */ \"(app-pages-browser)/./src/actions/feeActions.ts\");\n/* harmony import */ var _actions_courseActions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/actions/courseActions */ \"(app-pages-browser)/./src/actions/courseActions.ts\");\n/* harmony import */ var _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* harmony import */ var _lib_firebase__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/firebase */ \"(app-pages-browser)/./src/lib/firebase.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_popover__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/popover */ \"(app-pages-browser)/./src/components/ui/popover.tsx\");\n/* harmony import */ var _components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/calendar */ \"(app-pages-browser)/./src/components/ui/calendar.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/filter-x.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/piggy-bank.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-dollar-sign.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-bar.js\");\n/* harmony import */ var _barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=BarChartHorizontal,Calendar,CircleDollarSign,Download,FilterX,Loader2,PiggyBank!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/format.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst feeStatuses = [\n    'Pending',\n    'Paid',\n    'Partially Paid',\n    'Overdue'\n];\nconst statusVariantMap = {\n    Pending: 'default',\n    'Partially Paid': 'secondary',\n    Paid: 'outline',\n    Overdue: 'destructive'\n};\nfunction FinancialReportsPage() {\n    _s();\n    const { user, isLoading: isAuthLoading } = (0,_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined); // Start with no date filter to show all records\n    const [courseId, setCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [reportData, setReportData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [paymentTransactions, setPaymentTransactions] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const fetchReportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"FinancialReportsPage.useCallback[fetchReportData]\": async ()=>{\n            if (!user || !_lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser) return;\n            setIsLoading(true);\n            try {\n                const idToken = await _lib_firebase__WEBPACK_IMPORTED_MODULE_5__.auth.currentUser.getIdToken();\n                const filters = {\n                    startDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, 'yyyy-MM-dd') : undefined,\n                    endDate: (dateRange === null || dateRange === void 0 ? void 0 : dateRange.to) ? (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, 'yyyy-MM-dd') : undefined,\n                    courseId: courseId || undefined,\n                    status: status || undefined\n                };\n                const [data, transactions] = await Promise.all([\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getFinancialReportData)(filters, idToken),\n                    (0,_actions_feeActions__WEBPACK_IMPORTED_MODULE_2__.getAllPaymentTransactions)(idToken)\n                ]);\n                setReportData(data);\n                setPaymentTransactions(transactions);\n            } catch (error) {\n                const errorMessage = error instanceof Error ? error.message : \"Could not fetch report data.\";\n                toast({\n                    title: \"Error\",\n                    description: errorMessage,\n                    variant: \"destructive\"\n                });\n            } finally{\n                setIsLoading(false);\n            }\n        }\n    }[\"FinancialReportsPage.useCallback[fetchReportData]\"], [\n        user,\n        dateRange,\n        courseId,\n        status,\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            async function fetchInitialCourses() {\n                try {\n                    const fetchedCourses = await (0,_actions_courseActions__WEBPACK_IMPORTED_MODULE_3__.getCourses)();\n                    setCourses(fetchedCourses);\n                } catch (error) {\n                    toast({\n                        title: \"Error\",\n                        description: \"Could not load courses for filtering.\",\n                        variant: \"destructive\"\n                    });\n                }\n            }\n            fetchInitialCourses();\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        toast\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"FinancialReportsPage.useEffect\": ()=>{\n            if (!isAuthLoading && user) {\n                fetchReportData();\n            }\n        }\n    }[\"FinancialReportsPage.useEffect\"], [\n        fetchReportData,\n        isAuthLoading,\n        user\n    ]);\n    const clearFilters = ()=>{\n        setDateRange(undefined); // Clear date range to show all records\n        setCourseId('');\n        setStatus('');\n        // Fetch data immediately after clearing filters\n        setTimeout(()=>fetchReportData(), 100);\n    };\n    const summaryStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"FinancialReportsPage.useMemo[summaryStats]\": ()=>{\n            const totalDue = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalDue\": (sum, fee)=>sum + fee.amountDue\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalDue\"], 0);\n            const totalPaid = reportData.reduce({\n                \"FinancialReportsPage.useMemo[summaryStats].totalPaid\": (sum, fee)=>sum + fee.amountPaid\n            }[\"FinancialReportsPage.useMemo[summaryStats].totalPaid\"], 0);\n            const totalOutstanding = totalDue - totalPaid;\n            return {\n                totalDue,\n                totalPaid,\n                totalOutstanding,\n                recordCount: reportData.length\n            };\n        }\n    }[\"FinancialReportsPage.useMemo[summaryStats]\"], [\n        reportData\n    ]);\n    const exportToCSV = ()=>{\n        if (reportData.length === 0) {\n            toast({\n                title: \"No data to export\",\n                variant: \"default\"\n            });\n            return;\n        }\n        const headers = [\n            'ID',\n            'Student Name',\n            'Course Name',\n            'Fee Description',\n            'Amount Due',\n            'Amount Paid',\n            'Remaining',\n            'Due Date',\n            'Status',\n            'Last Payment Date'\n        ];\n        const csvRows = [\n            headers.join(',')\n        ];\n        for (const fee of reportData){\n            const remaining = fee.amountDue - fee.amountPaid;\n            const values = [\n                fee.id,\n                '\"'.concat(fee.studentName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.courseName.replace(/\"/g, '\"\"'), '\"'),\n                '\"'.concat(fee.feeDescription.replace(/\"/g, '\"\"'), '\"'),\n                fee.amountDue.toFixed(2),\n                fee.amountPaid.toFixed(2),\n                remaining.toFixed(2),\n                fee.dueDate,\n                fee.status,\n                fee.lastPaymentDate || ''\n            ];\n            csvRows.push(values.join(','));\n        }\n        const csvString = csvRows.join('\\n');\n        const blob = new Blob([\n            csvString\n        ], {\n            type: 'text/csv;charset=utf-8;'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.setAttribute('href', url);\n        link.setAttribute('download', \"financial_report_\".concat(new Date().toISOString().split('T')[0], \".csv\"));\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-headline font-bold tracking-tight text-primary\",\n                        children: \"Financial Reports\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 145,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Generate and view financial reports based on various filters.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                            children: \"Report Filters\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 items-end\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Due Date Range\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.Popover, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"justify-start text-left font-normal\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 33\n                                                        }, this),\n                                                        (dateRange === null || dateRange === void 0 ? void 0 : dateRange.from) ? dateRange.to ? \"\".concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\"), \" - \").concat((0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.to, \"LLL dd, y\")) : (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(dateRange.from, \"LLL dd, y\") : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Pick a date\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_popover__WEBPACK_IMPORTED_MODULE_9__.PopoverContent, {\n                                                className: \"w-auto p-0\",\n                                                align: \"start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_calendar__WEBPACK_IMPORTED_MODULE_10__.Calendar, {\n                                                    mode: \"range\",\n                                                    selected: dateRange,\n                                                    onSelect: setDateRange,\n                                                    initialFocus: true\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 29\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Course\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: courseId || '_all_',\n                                        onValueChange: (value)=>setCourseId(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Courses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Courses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 184,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    courses.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                            value: course.id,\n                                                            children: course.name\n                                                        }, course.id, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 185,\n                                                            columnNumber: 52\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Status\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 190,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.Select, {\n                                        value: status || '_all_',\n                                        onValueChange: (value)=>setStatus(value === '_all_' ? '' : value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectTrigger, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectValue, {\n                                                    placeholder: \"All Statuses\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 40\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                        value: \"_all_\",\n                                                        children: \"All Statuses\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 194,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    feeStatuses.map((s)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_11__.SelectItem, {\n                                                            value: s,\n                                                            className: \"capitalize\",\n                                                            children: s\n                                                        }, s, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 51\n                                                        }, this))\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 18\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: fetchReportData,\n                                        disabled: isLoading,\n                                        className: \"w-full\",\n                                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4 animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 38\n                                        }, this) : 'Apply Filters'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                        onClick: clearFilters,\n                                        disabled: isLoading,\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only\",\n                                                children: \"Clear Filters\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 203,\n                                        columnNumber: 22\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 149,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-4 md:grid-cols-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Collected\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 213,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalPaid.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Outstanding\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-32\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-destructive\",\n                                    children: [\n                                        \"₹\",\n                                        summaryStats.totalOutstanding.toFixed(2)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 14\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Filtered Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-8 w-16\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 34\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: summaryStats.recordCount\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 69\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 14\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardHeader, {\n                        className: \"flex flex-row items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardTitle, {\n                                        children: \"Filtered Fee Records\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardDescription, {\n                                        children: [\n                                            \"A detailed list of fee records matching the selected filters. Found \",\n                                            reportData.length,\n                                            \" records.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 17\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_8__.Button, {\n                                onClick: exportToCSV,\n                                disabled: isLoading || reportData.length === 0,\n                                variant: \"outline\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChartHorizontal_Calendar_CircleDollarSign_Download_FilterX_Loader2_PiggyBank_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 21\n                                    }, this),\n                                    \" Export CSV\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 249,\n                                columnNumber: 18\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_7__.CardContent, {\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: [\n                                ...Array(5)\n                            ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_14__.Skeleton, {\n                                    className: \"h-10 w-full\"\n                                }, i, false, {\n                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 54\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 22\n                        }, this) : reportData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center text-muted-foreground py-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"No records match the current filters.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 259,\n                            columnNumber: 21\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Student\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 267,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Course\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 268,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Fee Item\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Due Date\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    children: \"Status\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 271,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Due\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 37\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableHead, {\n                                                    className: \"text-right\",\n                                                    children: \"Amount Paid\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 37\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 33\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 29\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableBody, {\n                                        children: reportData.map((fee)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"font-medium\",\n                                                        children: fee.studentName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.courseName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: fee.feeDescription\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_15__.format)(new Date(fee.dueDate), 'PP')\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_13__.Badge, {\n                                                            variant: statusVariantMap[fee.status],\n                                                            className: \"capitalize\",\n                                                            children: fee.status\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                            lineNumber: 283,\n                                                            columnNumber: 52\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountDue.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 41\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_12__.TableCell, {\n                                                        className: \"text-right font-semibold text-green-700\",\n                                                        children: [\n                                                            \"₹\",\n                                                            fee.amountPaid.toFixed(2)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 41\n                                                    }, this)\n                                                ]\n                                            }, fee.id, true, {\n                                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n                lineNumber: 241,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Code\\\\edulite\\\\src\\\\app\\\\(app)\\\\financial-reports\\\\page.tsx\",\n        lineNumber: 143,\n        columnNumber: 5\n    }, this);\n}\n_s(FinancialReportsPage, \"y8jEenDhakqQ36qS1wk5rlTo0OI=\", false, function() {\n    return [\n        _contexts_AuthContext__WEBPACK_IMPORTED_MODULE_4__.useAuth,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast\n    ];\n});\n_c = FinancialReportsPage;\nvar _c;\n$RefreshReg$(_c, \"FinancialReportsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(app)/financial-reports/page.tsx\n"));

/***/ })

});