
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import type { Course } from '@/types';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CourseDialog } from './CourseDialog';
import { FeeStructureManager } from './FeeStructureManager';
import { CurriculumManager } from './CurriculumManager';
import { Edit, Trash2, BookUser, Info, Loader2, DollarSign, Clock, ListTree } from 'lucide-react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  Di<PERSON><PERSON>eader as <PERSON><PERSON><PERSON><PERSON><PERSON>Header,
  Di<PERSON><PERSON>it<PERSON> as <PERSON>hadDialogTitle,
  DialogClose,
  DialogDescription as ShadDialogDescription,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { getCourses, createCourse, updateCourse, deleteCourse } from '@/actions/courseActions';
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';
import { format } from 'date-fns';
import { Skeleton } from '@/components/ui/skeleton';

export function CourseList() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const [selectedCourseForFee, setSelectedCourseForFee] = useState<Course | null>(null);
  const [isFeeStructureDialogOpen, setIsFeeStructureDialogOpen] = useState(false);
  const [selectedCourseForCurriculum, setSelectedCourseForCurriculum] = useState<Course | null>(null);
  const [isCurriculumDialogOpen, setIsCurriculumDialogOpen] = useState(false);
  const { user } = useAuth();


  const fetchCourses = useCallback(async () => {
    setIsLoading(true);
    try {
      const fetchedCourses = await getCourses();
      setCourses(fetchedCourses);
    } catch (error) {
      console.error("Failed to fetch courses:", error);
      toast({
        title: "Error",
        description: "Could not fetch courses. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    fetchCourses();
  }, [fetchCourses]);

  const handleSaveCourse = async (courseData: Course | Omit<Course, 'id'>) => {
    if (!user) {
        toast({ title: "Unauthorized", description: "You must be logged in to save a course.", variant: "destructive" });
        return;
    }
    try {
      const idToken = await auth.currentUser?.getIdToken();
      if (!idToken) throw new Error("Authentication token not available.");

      if ('id' in courseData) {
        await updateCourse(courseData, idToken);
      } else {
        await createCourse(courseData, idToken);
      }
      fetchCourses();
    } catch (error) {
      console.error("Failed to save course:", error);
      toast({
        title: "Error Saving Course",
        description: error instanceof Error ? error.message : "Could not save the course.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteCourse = async (courseId: string) => {
    if (!user) {
        toast({ title: "Unauthorized", description: "You must be logged in to delete a course.", variant: "destructive" });
        return;
    }
    try {
      const idToken = await auth.currentUser?.getIdToken();
      if (!idToken) throw new Error("Authentication token not available.");

      await deleteCourse(courseId, idToken);
      toast({
        title: "Course Deleted",
        description: "The course has been successfully deleted.",
      });
      fetchCourses();
    } catch (error) {
      console.error("Failed to delete course:", error);
      toast({
        title: "Error Deleting Course",
        description: error instanceof Error ? error.message : "Could not delete the course.",
        variant: "destructive",
      });
    }
  };

  const openFeeStructureDialog = (course: Course) => {
    setSelectedCourseForFee(course);
    setIsFeeStructureDialogOpen(true);
  };

  const openCurriculumDialog = (course: Course) => {
    setSelectedCourseForCurriculum(course);
    setIsCurriculumDialogOpen(true);
  };


  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-end">
            <Skeleton className="h-10 w-40 rounded-md" />
        </div>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="flex flex-col shadow-lg">
              <CardHeader>
                <div className="flex justify-between items-start gap-2">
                  <Skeleton className="h-7 w-3/5" />
                  <Skeleton className="h-6 w-16 rounded-full" />
                </div>
                <CardDescription>
                  <Skeleton className="h-5 w-2/5 rounded-full" />
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow space-y-3">
                <Skeleton className="h-4 w-4/5" />
                <Skeleton className="h-4 w-3/5" />
                <Skeleton className="h-4 w-1/2" />
              </CardContent>
              <CardFooter className="flex flex-wrap justify-end gap-2 border-t pt-4">
                <Skeleton className="h-9 w-24 rounded-md" />
                <Skeleton className="h-9 w-32 rounded-md" />
                <Skeleton className="h-9 w-28 rounded-md" />
                <Skeleton className="h-9 w-20 rounded-md" />
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-end">
        <CourseDialog onSave={handleSaveCourse} />
      </div>
      {courses.length === 0 ? (
        <div className="text-center py-10 text-muted-foreground">
          <Info className="mx-auto h-12 w-12 mb-4" />
          <p className="text-xl font-semibold">No courses available.</p>
          <p>Click "Add New Course" to get started.</p>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {courses.map((course) => (
            <Card key={course.id} className="flex flex-col shadow-lg hover:shadow-xl transition-shadow duration-300">
              <CardHeader>
                <div className="flex justify-between items-start gap-2">
                  <CardTitle className="font-headline text-xl text-primary">{course.name}</CardTitle>
                  <Badge variant="secondary" className="whitespace-nowrap">{course.code}</Badge>
                </div>
                <CardDescription>
                  <div className="flex flex-wrap gap-1">
                    {course.specializations && course.specializations.map(spec => (
                      <Badge key={spec} variant="outline">{spec}</Badge>
                    ))}
                  </div>
                </CardDescription>
              </CardHeader>
              <CardContent className="flex-grow space-y-3">
                <div className="flex items-center text-sm text-muted-foreground">
                  <BookUser className="h-4 w-4 mr-2 text-accent" />
                  <span>Instructor: {course.instructor}</span>
                </div>
                {course.startDate && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="h-4 w-4 mr-2 text-accent" />
                    <span>Start: {format(new Date(course.startDate), "PPP")}</span>
                  </div>
                )}
                <div className="flex items-center text-sm text-muted-foreground">
                  <ListTree className="h-4 w-4 mr-2 text-accent" />
                  <span>{course.semesters?.length || 0} Semesters</span>
                </div>
              </CardContent>
              <CardFooter className="flex flex-wrap justify-end gap-2 border-t pt-4">
                 <Button variant="outline" size="sm" onClick={() => openFeeStructureDialog(course)}>
                    <DollarSign className="mr-1 h-3 w-3 text-green-600" /> Manage Fees
                 </Button>
                 <Button variant="outline" size="sm" onClick={() => openCurriculumDialog(course)}>
                    <ListTree className="mr-1 h-3 w-3 text-blue-600" /> Manage Curriculum
                </Button>
                 <CourseDialog
                    course={course}
                    onSave={handleSaveCourse}
                    triggerButton={
                        <Button variant="outline" size="sm">
                            <Edit className="mr-1 h-3 w-3" /> Edit Course
                        </Button>
                    }
                 />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm" className="bg-destructive hover:bg-destructive/90 text-destructive-foreground">
                      <Trash2 className="mr-1 h-3 w-3" /> Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete the course "{course.name}".
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={() => handleDeleteCourse(course.id)} className="bg-destructive hover:bg-destructive/90">
                        Delete
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {selectedCourseForFee && (
        <Dialog open={isFeeStructureDialogOpen} onOpenChange={setIsFeeStructureDialogOpen}>
            <DialogContent className="sm:max-w-3xl max-h-[80vh] flex flex-col">
                <ShadDialogHeader>
                    <ShadDialogTitle className="font-headline text-2xl text-primary">
                        Manage Fee Structure: {selectedCourseForFee.name}
                    </ShadDialogTitle>
                    <ShadDialogDescription>
                        Define the fee items for this course. These items will be used to automatically generate student fees upon enrollment.
                    </ShadDialogDescription>
                </ShadDialogHeader>
                <div className="flex-grow overflow-y-auto p-1 pr-2 mt-4">
                    <FeeStructureManager
                        courseId={selectedCourseForFee.id}
                        courseName={selectedCourseForFee.name}
                    />
                </div>
                <div className="pt-4 border-t mt-auto">
                  <DialogClose asChild>
                      <Button variant="outline">Close</Button>
                  </DialogClose>
                </div>
            </DialogContent>
        </Dialog>
      )}

      {selectedCourseForCurriculum && (
        <Dialog open={isCurriculumDialogOpen} onOpenChange={(isOpen) => {
            setIsCurriculumDialogOpen(isOpen);
            if (!isOpen) {
                fetchCourses(); // Re-fetch courses when dialog is closed to see updated semester count
            }
        }}>
            <DialogContent className="sm:max-w-4xl max-h-[90vh] flex flex-col">
                <ShadDialogHeader>
                    <ShadDialogTitle className="font-headline text-2xl text-primary">
                        Manage Curriculum: {selectedCourseForCurriculum.name}
                    </ShadDialogTitle>
                    <ShadDialogDescription>
                        Add, edit, or remove semesters and their subjects. All changes must be saved.
                    </ShadDialogDescription>
                </ShadDialogHeader>
                <div className="flex-grow overflow-y-auto p-1 pr-4 mt-4 -mr-2">
                    <CurriculumManager courseId={selectedCourseForCurriculum.id} />
                </div>
                <div className="pt-4 border-t mt-auto flex justify-end">
                  <DialogClose asChild>
                      <Button variant="outline">Close</Button>
                  </DialogClose>
                </div>
            </DialogContent>
        </Dialog>
      )}

    </div>
  );
}
