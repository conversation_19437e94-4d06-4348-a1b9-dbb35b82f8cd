
'use client';

import React, { useState, useEffect, useCallback } from 'react';
import type { FeeItem, CourseFeeStructure } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { PlusCircle, Edit, Trash2, Loader2, Info, Users } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { getCourseFeeStructureByCourseId, saveCourseFeeStructure, generateFeeForEnrolledStudents } from '@/actions/feeActions';
import { FeeItemDialog } from './FeeItemDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ooter,
  AlertDialogHeader,
  AlertDialog<PERSON>itle,
} from "@/components/ui/alert-dialog";
import { useAuth } from '@/contexts/AuthContext';
import { auth } from '@/lib/firebase';

// Custom Lucide icon for INR, as lucide-react doesn't have a direct one
const RupeeIcon = (props: React.SVGProps<SVGSVGElement>) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="24"
    height="24"
    viewBox="0 0 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
    {...props}
  >
    <path d="M6 3h12" />
    <path d="M6 8h12" />
    <path d="M6 13h12" />
    <path d="M6 18h12" />
    <path d="M6 3v18" />
    <path d="M18 3v18" />
    <path d="M9 3v18" />
    <path d="M15 3v18" />
    <path d="M9.5 8C7.015 8 5 10.015 5 12.5S7.015 17 9.5 17h5c2.485 0 4.5-2.015 4.5-4.5S16.985 8 14.5 8z" transform="matrix(1 0 0 1 -0.5 -1)"/>
     <path d="M6 3h12"></path>
     <path d="M6 8h12"></path>
     <path d="M9.5 13H18"></path>
  </svg>
);


interface FeeStructureManagerProps {
  courseId: string;
  courseName: string;
}

export function FeeStructureManager({ courseId, courseName }: FeeStructureManagerProps) {
  const [feeStructure, setFeeStructure] = useState<CourseFeeStructure | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isFeeItemDialogOpen, setIsFeeItemDialogOpen] = useState(false);
  const [editingFeeItem, setEditingFeeItem] = useState<FeeItem | null>(null);
  const [itemToDelete, setItemToDelete] = useState<FeeItem | null>(null);
  const [generatingFor, setGeneratingFor] = useState<FeeItem | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();

  const fetchFeeStructure = useCallback(async () => {
    if (!user) {
        setIsLoading(false);
        return;
    }
    setIsLoading(true);
    try {
      const idToken = await auth.currentUser?.getIdToken();
      if (!idToken) {
          throw new Error('Authentication token not available.');
      }
      const structure = await getCourseFeeStructureByCourseId(courseId, idToken);
      setFeeStructure(structure);
    } catch (error) {
      let errorMessage = "Could not fetch fee structure.";
      if (error instanceof Error) {
          errorMessage = error.message;
      }
      console.error(`Failed to fetch fee structure for course ${courseId}:`, error);
      toast({ title: "Error", description: errorMessage, variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  }, [courseId, toast, user]);

  useEffect(() => {
    fetchFeeStructure();
  }, [fetchFeeStructure]);

  const handleOpenFeeItemDialog = (item?: FeeItem) => {
    setEditingFeeItem(item || null);
    setIsFeeItemDialogOpen(true);
  };

  const handleSaveFeeItem = (itemData: Omit<FeeItem, 'id'> | FeeItem) => {
    setFeeStructure(prevStructure => {
      if (!prevStructure) return null; // Should not happen if dialog is openable
      const newItems = [...(prevStructure.items || [])];
      if ('id' in itemData && itemData.id) { // Editing existing item
        const index = newItems.findIndex(item => item.id === itemData.id);
        if (index > -1) {
          newItems[index] = itemData as FeeItem;
        }
      } else { // Adding new item
        newItems.push({ ...itemData, id: `new-${Date.now()}-${Math.random().toString(36).substring(2,7)}` } as FeeItem);
      }
      return { ...prevStructure, items: newItems };
    });
    setIsFeeItemDialogOpen(false);
    setEditingFeeItem(null);
  };

  const handleSaveStructure = async () => {
    if (!feeStructure || !feeStructure.items || !user) {
        toast({ title: "Error", description: "You must be logged in to save.", variant: "destructive" });
        return;
    };
    setIsSaving(true);
    try {
      const idToken = await auth.currentUser?.getIdToken();
      if (!idToken) {
        throw new Error('Authentication token not available.');
      }
      const savedStructure = await saveCourseFeeStructure(courseId, feeStructure.items, idToken);
      if (savedStructure) {
        setFeeStructure(savedStructure);
        toast({ title: "Success", description: "Fee structure saved successfully." });
      } else {
        throw new Error("Save operation failed.");
      }
    } catch (error) {
      let errorMessage = "Could not save fee structure.";
        if (error instanceof Error) {
            errorMessage = error.message;
        }
      console.error(`Failed to save fee structure for course ${courseId}:`, error);
      toast({ title: "Error", description: errorMessage, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };
  
  const openDeleteConfirmDialog = (item: FeeItem) => {
    setItemToDelete(item);
  };

  const handleDeleteFeeItem = () => {
    if (!itemToDelete) return;
    setFeeStructure(prevStructure => {
      if (!prevStructure) return null;
      return {
        ...prevStructure,
        items: prevStructure.items.filter(item => item.id !== itemToDelete.id),
      };
    });
    setItemToDelete(null); // Close dialog
    toast({ title: "Item Removed", description: `"${itemToDelete.name}" removed from structure. Save to persist.`});
  };

  const handleGenerateForStudents = async () => {
    if (!generatingFor || !user || !auth.currentUser) return;
    setIsGenerating(true);
    try {
      const idToken = await auth.currentUser.getIdToken();
      const result = await generateFeeForEnrolledStudents(courseId, generatingFor, idToken);
      if (result.success) {
        toast({
          title: "Fees Generated",
          description: `Successfully generated "${generatingFor.name}" for ${result.count} enrolled student(s).`
        });
      } else {
        throw new Error("Fee generation failed on the server.");
      }
    } catch (err: any) {
        toast({ title: "Error", description: err.message || "Could not generate fees.", variant: "destructive" });
    } finally {
      setIsGenerating(false);
      setGeneratingFor(null);
    }
  };


  if (isLoading) {
    return <div className="flex justify-center items-center py-8"><Loader2 className="h-8 w-8 animate-spin text-primary" /></div>;
  }

  if (!user) {
    return (
        <div className="text-center py-8 text-muted-foreground">
            <Info className="mx-auto h-10 w-10 mb-3" />
            <p>Please log in to manage fee structures.</p>
        </div>
    );
  }

  if (!feeStructure) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <Info className="mx-auto h-10 w-10 mb-3" />
        <p>No fee structure data available for this course.</p>
        <p>You can start by adding fee items.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <CardHeader className="p-0 mb-4">
        <CardTitle className="font-headline text-xl flex items-center gap-2">
            <RupeeIcon className="h-6 w-6 text-primary" />
            Fee Structure: {courseName}
        </CardTitle>
        <CardDescription>Define the fee items associated with this course. These items will be used to automatically generate student fees upon enrollment.</CardDescription>
      </CardHeader>

      <div className="flex justify-end gap-2 mb-4">
        <Button onClick={() => handleOpenFeeItemDialog()} className="bg-accent hover:bg-accent/90">
          <PlusCircle className="mr-2 h-4 w-4" /> Add Fee Item
        </Button>
      </div>

      {feeStructure.items.length === 0 ? (
        <div className="text-center py-6 text-muted-foreground border-2 border-dashed rounded-lg">
          <p>No fee items defined yet for this course.</p>
          <p>Click "Add Fee Item" to get started.</p>
        </div>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Item Name</TableHead>
                  <TableHead className="text-right">Tuition Fee (₹)</TableHead>
                  <TableHead className="text-right">Exam Fee (₹)</TableHead>
                  <TableHead className="text-right">Other Fee (₹)</TableHead>
                  <TableHead className="text-right font-bold text-primary">Total (₹)</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {feeStructure.items.map((item) => {
                    const total = (item.tuitionFee || 0) + (item.examFee || 0) + (item.otherFee || 0);
                    return (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.name}</TableCell>
                        <TableCell className="text-right">₹{(item.tuitionFee || 0).toFixed(2)}</TableCell>
                        <TableCell className="text-right">₹{(item.examFee || 0).toFixed(2)}</TableCell>
                        <TableCell className="text-right">₹{(item.otherFee || 0).toFixed(2)}</TableCell>
                        <TableCell className="text-right font-bold text-primary">₹{total.toFixed(2)}</TableCell>
                        <TableCell className="text-right">
                          <Button variant="ghost" title="Generate for Students" size="icon" onClick={() => setGeneratingFor(item)} className="mr-1 h-8 w-8">
                            <Users className="h-4 w-4 text-accent" />
                          </Button>
                          <Button variant="ghost" title="Edit Item" size="icon" onClick={() => handleOpenFeeItemDialog(item)} className="mr-1 h-8 w-8">
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" title="Delete Item" size="icon" onClick={() => openDeleteConfirmDialog(item)} className="text-destructive hover:text-destructive h-8 w-8">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    );
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
      
      <div className="mt-6 flex justify-end">
        <Button onClick={handleSaveStructure} disabled={isSaving || isLoading} className="min-w-[120px]">
          {isSaving ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Save Structure'}
        </Button>
      </div>

      <FeeItemDialog
        feeItem={editingFeeItem}
        open={isFeeItemDialogOpen}
        onOpenChange={setIsFeeItemDialogOpen}
        onSave={handleSaveFeeItem}
      />

      <AlertDialog open={!!generatingFor} onOpenChange={() => setGeneratingFor(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Generate Fee for Students?</AlertDialogTitle>
            <AlertDialogDescription>
              This will create a new fee record for <strong>"{generatingFor?.name}"</strong> (Total: ₹{generatingFor ? ((generatingFor.tuitionFee || 0) + (generatingFor.examFee || 0) + (generatingFor.otherFee || 0)).toFixed(2) : '0.00'}) for all students currently enrolled in <strong>{courseName}</strong> who do not already have this exact fee. This action cannot be undone. Are you sure?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isGenerating}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleGenerateForStudents} disabled={isGenerating}>
              {isGenerating ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : 'Generate Fees'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {itemToDelete && (
         <AlertDialog open={!!itemToDelete} onOpenChange={() => setItemToDelete(null)}>
            <AlertDialogContent>
                <AlertDialogHeader>
                <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                <AlertDialogDescription>
                    This will remove the fee item "{itemToDelete.name}" from the structure. This action is not saved until you click "Save Structure".
                </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setItemToDelete(null)}>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleDeleteFeeItem} className="bg-destructive hover:bg-destructive/90">
                    Remove Item
                </AlertDialogAction>
                </AlertDialogFooter>
            </AlertDialogContent>
        </AlertDialog>
      )}
    </div>
  );
}
