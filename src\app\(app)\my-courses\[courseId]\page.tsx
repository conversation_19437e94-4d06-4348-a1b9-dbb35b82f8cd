
'use client'; 

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useParams } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ArrowLeft, BookUser, CalendarDays, CheckCircle, Info, ListChecks, Star, Loader2, FileText, Video, Link2, Check, CheckCheck } from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { useAuth } from '@/contexts/AuthContext';
import { getCourseById } from '@/actions/courseActions';
import { getStudentProgressForCourse, updateStudentProgress } from '@/actions/progressActions';
import type { Course, StudyMaterial, Subject, Unit } from '@/types';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { auth } from '@/lib/firebase';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';

const materialIconMap: Record<StudyMaterial['type'], React.ElementType> = {
  PDF: FileText,
  Document: FileText,
  Video: Video,
  Link: Link2,
  Text: FileText,
};

export default function CourseDetailPage() {
  const params = useParams();
  const courseId = params.courseId as string;
  
  const { user, isLoading: authLoading } = useAuth();
  const { toast } = useToast();
  const [course, setCourse] = useState<Course | null>(null);
  const [isLoadingCourse, setIsLoadingCourse] = useState(true);
  const [isLoadingProgress, setIsLoadingProgress] = useState(true);
  const [completedUnits, setCompletedUnits] = useState<Set<string>>(new Set());

  useEffect(() => {
    async function fetchCourseData() {
      if (!courseId) return;
      setIsLoadingCourse(true);
      try {
        const fetchedCourse = await getCourseById(courseId);
        setCourse(fetchedCourse);
      } catch (error) {
        console.error("Failed to fetch course details:", error);
        toast({ title: "Error", description: "Could not load course details.", variant: "destructive" });
      } finally {
        setIsLoadingCourse(false);
      }
    }
    fetchCourseData();
  }, [courseId, toast]);

  useEffect(() => {
    async function fetchProgress() {
        if (!user || !courseId || !auth.currentUser) {
            setIsLoadingProgress(false);
            return;
        }
        setIsLoadingProgress(true);
        try {
            const idToken = await auth.currentUser.getIdToken();
            const progressData = await getStudentProgressForCourse(user.uid, courseId, idToken);
            if (progressData && progressData.completedUnits) {
                setCompletedUnits(new Set(progressData.completedUnits));
            }
        } catch (error) {
            console.error("Failed to fetch student progress:", error);
            // Silent failure is okay here, it just means no progress is stored yet.
        } finally {
            setIsLoadingProgress(false);
        }
    }
    fetchProgress();
  }, [user, courseId]);

  const totalUnits = useMemo(() => {
    if (!course) return 0;
    return course.semesters.reduce((semTotal, sem) => 
        semTotal + sem.subjects.reduce((subTotal, sub) => 
            subTotal + (sub.units?.length || 0), 0), 0);
  }, [course]);

  const progress = totalUnits > 0 ? Math.round((completedUnits.size / totalUnits) * 100) : 0;

  const handleToggleUnitCompletion = async (unitId: string) => {
    if (!user || !courseId || !auth.currentUser) {
        toast({ title: "Error", description: "You must be logged in to update progress.", variant: "destructive" });
        return;
    }

    const newCompletedUnitsSet = new Set(completedUnits);
    if (newCompletedUnitsSet.has(unitId)) {
        newCompletedUnitsSet.delete(unitId);
    } else {
        newCompletedUnitsSet.add(unitId);
    }

    // Optimistic update for instant UI feedback
    setCompletedUnits(newCompletedUnitsSet);

    try {
        const idToken = await auth.currentUser.getIdToken();
        await updateStudentProgress(user.uid, courseId, Array.from(newCompletedUnitsSet), idToken);
    } catch (error) {
        // Revert optimistic update on failure
        setCompletedUnits(completedUnits);
        const errorMessage = error instanceof Error ? error.message : "Could not save your progress.";
        toast({
            title: "Update Failed",
            description: errorMessage,
            variant: "destructive"
        });
        console.error("Failed to update progress:", error);
    }
  };

  if (isLoadingCourse || authLoading || !courseId || isLoadingProgress) {
    return (
      <div className="max-w-4xl mx-auto p-4 md:p-6 lg:p-8 space-y-8 animate-pulse">
        <div className="mb-6">
            <Skeleton className="h-9 w-44 rounded-md" />
        </div>
        <Card className="shadow-xl overflow-hidden rounded-lg bg-card">
            <Skeleton className="h-64 w-full md:h-80" />
            <CardContent className="p-6 md:p-8 space-y-6">
                <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6">
                    <Skeleton className="h-20 w-full rounded-lg" />
                    <Skeleton className="h-20 w-full rounded-lg" />
                </div>
                <Separator />
                <div className="grid md:grid-cols-2 gap-6">
                    <Skeleton className="h-16 w-full rounded-lg" />
                    <Skeleton className="h-16 w-full rounded-lg" />
                </div>
                <Separator />
                <div>
                    <Skeleton className="h-7 w-48 mb-3" />
                    <Skeleton className="h-3 w-full rounded-full" />
                </div>
                 <Separator />
                <div>
                    <Skeleton className="h-7 w-56 mb-4" />
                    <div className="space-y-2">
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                        <Skeleton className="h-12 w-full" />
                    </div>
                </div>
            </CardContent>
        </Card>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[calc(100vh-200px)] text-center p-4">
        <Info className="h-16 w-16 text-destructive mb-4" />
        <h1 className="text-2xl font-bold text-destructive mb-2">Course Not Found</h1>
        <p className="text-muted-foreground mb-6">The course you are looking for does not exist or may have been removed.</p>
        <Button asChild variant="outline">
          <Link href="/my-courses">
            <ArrowLeft className="mr-2 h-4 w-4" /> Go Back to My Courses
          </Link>
        </Button>
      </div>
    );
  }

  const totalCredits = course.semesters.reduce((total, sem) => total + sem.subjects.reduce((subTotal, sub) => subTotal + sub.credits, 0), 0);
  
  return (
    <div className="max-w-4xl mx-auto p-4 md:p-6 lg:p-8 space-y-8">
      <div className="mb-6">
        <Button asChild variant="outline" size="sm" className="hover:bg-accent/10">
          <Link href="/my-courses">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to My Courses
          </Link>
        </Button>
      </div>

      <Card className="shadow-xl overflow-hidden rounded-lg bg-card">
        <div className="relative h-64 w-full md:h-80">
          <Image
            src={`https://placehold.co/800x400.png`}
            alt={course.name}
            layout="fill"
            objectFit="cover"
            priority
            data-ai-hint="course content education"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent" />
          <div className="absolute bottom-0 left-0 p-6 md:p-8">
            <Badge variant="secondary" className="mb-2 text-sm px-3 py-1">{course.code}</Badge>
            <h1 className="text-3xl md:text-4xl font-headline font-bold text-white shadow-md">{course.name}</h1>
          </div>
        </div>
        
        <CardContent className="p-6 md:p-8 space-y-6">
          <div className="grid md:grid-cols-2 lg:grid-cols-2 gap-6 text-sm">
            <div className="flex items-center p-4 bg-muted/50 rounded-lg shadow-sm">
              <BookUser className="h-6 w-6 mr-3 text-accent flex-shrink-0" />
              <div>
                <p className="text-muted-foreground">Lead Instructor</p>
                <p className="font-semibold text-lg text-foreground">{course.instructor}</p>
              </div>
            </div>
            <div className="flex items-center p-4 bg-muted/50 rounded-lg shadow-sm">
              <Star className="h-6 w-6 mr-3 text-accent flex-shrink-0" />
              <div>
                <p className="text-muted-foreground">Total Credits</p>
                <p className="font-semibold text-lg text-foreground">{totalCredits}</p>
              </div>
            </div>
          </div>
          
          {(course.startDate || course.endDate) && (
            <>
            <Separator />
            <div className="grid md:grid-cols-2 gap-6 text-sm">
                {course.startDate && (
                    <div className="flex items-start p-4 bg-muted/50 rounded-lg shadow-sm">
                        <CalendarDays className="h-5 w-5 mr-3 mt-1 text-green-500 flex-shrink-0" />
                        <div>
                            <p className="text-muted-foreground">Start Date</p>
                            <p className="font-semibold text-foreground">{format(new Date(course.startDate), "PPP")}</p>
                        </div>
                    </div>
                )}
                {course.endDate && (
                     <div className="flex items-start p-4 bg-muted/50 rounded-lg shadow-sm">
                        <CalendarDays className="h-5 w-5 mr-3 mt-1 text-red-500 flex-shrink-0" />
                        <div>
                            <p className="text-muted-foreground">End Date</p>
                            <p className="font-semibold text-foreground">{format(new Date(course.endDate), "PPP")}</p>
                        </div>
                    </div>
                )}
            </div>
            </>
          )}

          <>
            <Separator />
            <div>
              <h2 className="text-xl font-headline font-semibold text-primary mb-3 flex items-center">
                <CheckCircle className="mr-3 h-6 w-6 text-accent" />
                Course Progress
              </h2>
              <div className="space-y-2">
                <Progress value={progress} className="h-3" />
                <div className="flex justify-between items-center text-sm text-muted-foreground">
                  <span>Completed Units: {completedUnits.size}</span>
                  <span className="font-semibold text-primary">{progress}% Complete</span>
                  <span>Total Units: {totalUnits}</span>
                </div>
              </div>
            </div>
          </>

          {course.specializations && course.specializations.length > 0 && (
            <>
              <Separator />
              <div>
                <h2 className="text-xl font-headline font-semibold text-primary mb-3">Specializations</h2>
                <div className="flex flex-wrap gap-2">
                  {course.specializations.map(spec => (
                    <Badge key={spec} variant="secondary" className="text-base">{spec}</Badge>
                  ))}
                </div>
              </div>
            </>
          )}

          {course.semesters && course.semesters.length > 0 && (
            <>
              <Separator />
              <div>
                <h2 className="text-xl font-headline font-semibold text-primary mb-4 flex items-center">
                  <ListChecks className="mr-3 h-6 w-6 text-accent" />
                  Course Curriculum
                </h2>
                <Accordion type="single" collapsible className="w-full" defaultValue={`item-0`}>
                  {course.semesters.map((semester, index) => (
                    <AccordionItem value={`item-${index}`} key={semester.id}>
                      <AccordionTrigger className="text-lg hover:no-underline">
                        Semester {semester.semesterNumber}
                      </AccordionTrigger>
                      <AccordionContent>
                        <ul className="space-y-4 pl-4 border-l-2 border-accent ml-2">
                          {semester.subjects.map((subject: Subject) => (
                            <li key={subject.id}>
                              <div className="flex justify-between items-center text-muted-foreground">
                                <span className="font-medium text-foreground">{subject.name}</span>
                                <Badge variant="outline">{subject.credits} Credits</Badge>
                              </div>
                              {subject.units && subject.units.length > 0 && (
                                <Accordion type="single" collapsible className="w-full mt-2 pl-2">
                                  {subject.units.map((unit: Unit) => (
                                    <AccordionItem value={`unit-${unit.id}`} key={unit.id} className="border-b-0">
                                      <AccordionTrigger className="text-md py-2 font-normal text-foreground/80 hover:no-underline">
                                        {unit.name}
                                      </AccordionTrigger>
                                      <AccordionContent className="pl-4 pb-2">
                                         {unit.materials && unit.materials.length > 0 ? (
                                             <ul className="space-y-1">
                                                {unit.materials.map((material) => {
                                                  const Icon = materialIconMap[material.type] || Link2;
                                                  return (
                                                    <li key={material.id} className="text-sm">
                                                      {material.type === 'Text' && material.content ? (
                                                        <Accordion type="single" collapsible className="w-full -my-2">
                                                            <AccordionItem value={material.id} className="border-b-0">
                                                                <AccordionTrigger className="py-1 text-accent hover:no-underline hover:text-primary transition-colors justify-start gap-2 font-medium">
                                                                    <Icon className="h-4 w-4" />
                                                                    <span>{material.name}</span>
                                                                </AccordionTrigger>
                                                                <AccordionContent className="pl-[26px] pt-2 pb-1">
                                                                    <p className="text-sm text-foreground whitespace-pre-wrap bg-muted/50 p-3 rounded-md border">{material.content}</p>
                                                                </AccordionContent>
                                                            </AccordionItem>
                                                        </Accordion>
                                                      ) : (
                                                        <a href={material.url || '#'} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 text-accent hover:underline hover:text-primary transition-colors">
                                                          <Icon className="h-4 w-4" />
                                                          <span>{material.name}</span>
                                                        </a>
                                                      )}
                                                    </li>
                                                  )
                                                })}
                                              </ul>
                                         ) : <p className="text-xs text-muted-foreground italic">No materials for this unit.</p>}
                                        <div className="mt-4 flex justify-end">
                                            <Button
                                                variant={completedUnits.has(unit.id) ? "secondary" : "outline"}
                                                size="sm"
                                                onClick={() => handleToggleUnitCompletion(unit.id)}
                                            >
                                                {completedUnits.has(unit.id) ? (
                                                <>
                                                    <CheckCheck className="mr-2 h-4 w-4 text-green-700" />
                                                    Completed
                                                </>
                                                ) : (
                                                <>
                                                    <Check className="mr-2 h-4 w-4" />
                                                    Mark as Complete
                                                </>
                                                )}
                                            </Button>
                                        </div>
                                      </AccordionContent>
                                    </AccordionItem>
                                  ))}
                                </Accordion>
                              )}
                            </li>
                          ))}
                        </ul>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
