
import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
  Section,
} from '@react-email/components';

interface ApplicationStatusChangeEmailProps {
  applicantName: string;
  courseName: string;
  status: 'Accepted' | 'Rejected';
  applicationDate: string;
}

const ApplicationStatusChangeEmail: React.FC<ApplicationStatusChangeEmailProps> = ({
  applicantName,
  courseName,
  status,
  applicationDate,
}) => {
  const isAccepted = status === 'Accepted';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9002';
  
  return (
    <Html>
      <Head />
      <Preview>Your EduLite Application Status Has Been Updated</Preview>
      <Body style={main}>
        <Container style={container}>
          <Heading style={heading}>Application Status Update</Heading>
          <Text style={paragraph}>Dear {applicantName},</Text>
          <Text style={paragraph}>
            Thank you for your application to EduLite for the {courseName} program, submitted on {new Date(applicationDate).toLocaleDateString()}. 
            We have reviewed your application and have an update for you.
          </Text>
          <Section style={statusSection(isAccepted)}>
            <Text style={statusText(isAccepted)}>
              Your application status is now: <strong>{status}</strong>
            </Text>
          </Section>
          
          {isAccepted ? (
            <Text style={paragraph}>
              Congratulations! We are delighted to offer you a place. Further details regarding enrollment and next steps will be communicated shortly. You can log in to your student portal to view your enrolled courses and fee details.
            </Text>
          ) : (
            <Text style={paragraph}>
              We appreciate the time you invested in your application. After careful consideration, we are unable to offer you a place at this time. We received a large number of highly qualified applicants, making the selection process very competitive. We wish you the best in your future endeavors.
            </Text>
          )}

          <Button
            style={button}
            href={`${baseUrl}/login`}
          >
            Login to Your Portal
          </Button>
          <Text style={footer}>
            This is an automated notification from the EduLite system.
          </Text>
        </Container>
      </Body>
    </Html>
  );
};

export default ApplicationStatusChangeEmail;

// Styles
const main = {
  backgroundColor: '#f6f9fc',
  fontFamily: '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Ubuntu,sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
  border: '1px solid #f0f0f0',
  borderRadius: '4px',
};

const heading = {
  fontSize: '28px',
  fontWeight: 'bold',
  textAlign: 'center' as const,
  color: '#3F51B5',
};

const paragraph = {
  fontSize: '16px',
  lineHeight: '24px',
  color: '#525f7f',
  padding: '0 40px',
};

const statusSection = (isAccepted: boolean) => ({
  padding: '20px',
  margin: '20px 40px',
  backgroundColor: isAccepted ? '#e6fffa' : '#fff5f5',
  border: `1px solid ${isAccepted ? '#38a169' : '#e53e3e'}`,
  borderRadius: '4px',
  textAlign: 'center' as const,
});

const statusText = (isAccepted: boolean) => ({
    fontSize: '18px',
    fontWeight: 'bold',
    color: isAccepted ? '#2f855a' : '#c53030',
    margin: 0,
});

const button = {
  backgroundColor: '#009688',
  borderRadius: '5px',
  color: '#fff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '200px',
  padding: '12px',
  margin: '20px auto',
};

const footer = {
  color: '#8898aa',
  fontSize: '12px',
  lineHeight: '16px',
  textAlign: 'center' as const,
  marginTop: '20px',
};
